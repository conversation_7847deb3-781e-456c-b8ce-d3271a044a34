import { AuthenticatedRequest } from "../types";
import { Response } from "express";
import {
  createOrderFromCart,
  processPayment,
  getUserDefaultShippingAddress,
  getOrderById,
  getOrderByIdForSeller,
  fetchAllOrdersForAdmin,
} from "../services/order.service";
import * as OrderService from "../services/order.service";
import logger from "../utils/logger";
import { timeoutAnalyzer } from "../utils/timeoutAnalyzer";

export const checkoutController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user!.id;
    const { shippingAddress, paymentMethod, notes } = req.body;

    // Validate required fields
    if (!shippingAddress || !paymentMethod) {
      res.status(400).json({
        message: "Shipping address and payment method are required",
      });
      return;
    }

    // Create order with checkout data
    const order = await createOrderFromCart(userId, {
      shippingAddress,
      paymentMethod,
      notes,
    });

    logger.info("Order created successfully", {
      orderId: order.id,
      userId,
      total: order.total,
    });

    res.status(201).json({
      message: "Order created successfully",
      data: order,
    });
  } catch (e: any) {
    logger.error("Checkout failed", {
      error: e.message,
      stack: e.stack,
      userId: req.user?.id,
      body: req.body,
    });

    // Determine appropriate status code based on error type
    let statusCode = 400;
    let message = e.message || "Checkout failed";

    if (message.includes("Cart is empty")) {
      statusCode = 400;
    } else if (
      message.includes("insufficient stock") ||
      message.includes("out of stock")
    ) {
      statusCode = 409; // Conflict
    } else if (message.includes("User not found")) {
      statusCode = 404;
    } else if (message.includes("network") || message.includes("connection")) {
      statusCode = 503; // Service Unavailable
    }

    res.status(statusCode).json({
      message,
      error: process.env.NODE_ENV === "development" ? e.stack : undefined,
    });
    return;
  }
};

export const processPaymentController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { orderId } = req.params;
    const { paymentMethod } = req.body;

    if (!paymentMethod) {
      res.status(400).json({ message: "Payment method is required" });
      return;
    }

    const result = await processPayment(orderId, paymentMethod);

    if (result.success) {
      res.status(200).json({
        message: "Payment processed successfully",
        data: {
          transactionId: result.transactionId,
          status: "completed",
        },
      });
    } else {
      res.status(400).json({
        message: result.error || "Payment processing failed",
      });
    }
  } catch (e: any) {
    logger.error("Payment processing failed", {
      error: e.message,
      orderId: req.params.orderId,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

export const getDefaultShippingAddressController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user!.id;
    const defaultAddress = await getUserDefaultShippingAddress(userId);

    res.status(200).json({
      message: "Default shipping address retrieved",
      data: defaultAddress,
    });
  } catch (e: any) {
    logger.error("Failed to get default shipping address", {
      error: e.message,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

export const getMyOrders = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const orders = await OrderService.fetchBuyerOrders(userId);
  res.status(200).json({ orders });
  return;
};

export const getOrderByIdController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user!.id;
    const userRole = req.user!.role;
    const { orderId } = req.params;

    let order;

    // Try to get order based on user role
    if (userRole === "admin") {
      // Admin can access any order
      const { AppDataSource } = await import("../config/db.config");
      const { Order } = await import("../entities");
      const orderRepo = AppDataSource.getRepository(Order);
      order = await orderRepo.findOne({
        where: { id: orderId },
        relations: ["items", "items.product", "items.product.seller", "buyer"],
      });
    } else if (userRole === "seller") {
      // Seller can only access orders containing their products
      order = await getOrderByIdForSeller(orderId, userId);
    } else {
      // Buyer can only access their own orders
      order = await getOrderById(orderId, userId);
    }

    if (!order) {
      res.status(404).json({ message: "Order not found" });
      return;
    }

    res.status(200).json({
      message: "Order retrieved successfully",
      data: order,
    });
  } catch (e: any) {
    logger.error("Failed to get order", {
      error: e.message,
      orderId: req.params.orderId,
      userId: req.user?.id,
      userRole: req.user?.role,
    });

    if (
      e.message === "Order not found" ||
      e.message.includes("don't have access")
    ) {
      res.status(404).json({ message: "Order not found" });
    } else {
      res.status(500).json({ message: "Internal server error" });
    }
  }
};

export const getSoldOrders = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  const userId = req.user!.id;
  const orders = await OrderService.fetchSellerOrders(userId);
  res.status(200).json({ orders });
  return;
};

export const updateOrderItemStatus = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  const startTime = Date.now();

  try {
    const sellerId = req.user!.id;
    const { orderItemId } = req.params;
    const { status } = req.body;

    logger.info("Order status update request", {
      sellerId,
      orderItemId,
      status,
      body: req.body,
    });

    if (!status) {
      res.status(400).json({ message: "Status is required" });
      return;
    }

    // Set a timeout for the operation
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error("Operation timeout")), 10000); // 10 second timeout
    });

    const updatePromise = OrderService.updateOrderItemStatus(
      orderItemId,
      status
    );

    const updated = await Promise.race([updatePromise, timeoutPromise]);

    const duration = Date.now() - startTime;

    logger.info("Order status updated successfully", {
      sellerId,
      orderItemId,
      newStatus: status,
      duration: `${duration}ms`,
    });

    res.status(200).json({
      orderItem: updated,
      message:
        "Order status updated successfully. Email notification will be sent shortly.",
    });
    return;
  } catch (error: any) {
    const duration = Date.now() - startTime;

    logger.error("Failed to update order status", {
      sellerId: req.user?.id,
      orderItemId: req.params.orderItemId,
      status: req.body.status,
      error: error.message,
      duration: `${duration}ms`,
      stack: error.stack,
    });

    let statusCode = 500;
    let message = error.message || "Failed to update order status";

    if (message.includes("not found")) {
      statusCode = 404;
    } else if (message.includes("Invalid status")) {
      statusCode = 400;
    } else if (message.includes("timeout")) {
      statusCode = 408; // Request Timeout
      message =
        "Request timeout. The order status may have been updated. Please refresh and check.";

      // Record timeout for analysis
      timeoutAnalyzer.recordTimeout(
        req.originalUrl,
        req.method,
        duration,
        req.get("User-Agent"),
        req.ip
      );
    } else if (message.includes("network") || message.includes("connection")) {
      statusCode = 503; // Service Unavailable
      message = "Network connection issue. Please try again.";
    }

    res.status(statusCode).json({
      message,
      error: process.env.NODE_ENV === "development" ? error.stack : undefined,
    });
    return;
  }
};

export const getAdminOrdersController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const orders = await fetchAllOrdersForAdmin();

    res.status(200).json({
      message: "Orders retrieved successfully",
      orders: orders,
    });
  } catch (e: any) {
    logger.error("Failed to get admin orders", {
      error: e.message,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};
