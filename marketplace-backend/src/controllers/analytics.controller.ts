import { Response } from "express";
import { AuthenticatedRequest } from "../types";
import {
  getDashboardStats,
  getTopProducts,
  getRevenueData,
  getUserGrowthData,
  getSellerStats,
} from "../services/analytics.service";
import logger from "../utils/logger";

// Get admin dashboard statistics
export const getAdminDashboardStats = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const stats = await getDashboardStats();

    res.status(200).json({
      message: "Dashboard statistics retrieved successfully",
      data: stats,
    });
  } catch (error: any) {
    logger.error("Failed to get admin dashboard stats", {
      error: error.message,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get top performing products
export const getTopProductsController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    const topProducts = await getTopProducts(limit);

    res.status(200).json({
      message: "Top products retrieved successfully",
      data: topProducts,
    });
  } catch (error: any) {
    logger.error("Failed to get top products", {
      error: error.message,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get revenue chart data
export const getRevenueChartData = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const days = parseInt(req.query.days as string) || 30;
    const revenueData = await getRevenueData(days);

    res.status(200).json({
      message: "Revenue data retrieved successfully",
      data: revenueData,
    });
  } catch (error: any) {
    logger.error("Failed to get revenue chart data", {
      error: error.message,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get user growth chart data
export const getUserGrowthChartData = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const days = parseInt(req.query.days as string) || 30;
    const userGrowthData = await getUserGrowthData(days);

    res.status(200).json({
      message: "User growth data retrieved successfully",
      data: userGrowthData,
    });
  } catch (error: any) {
    logger.error("Failed to get user growth chart data", {
      error: error.message,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get seller dashboard statistics
export const getSellerDashboardStats = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const sellerId = req.user!.id;
    const stats = await getSellerStats(sellerId);

    res.status(200).json({
      message: "Seller statistics retrieved successfully",
      data: stats,
    });
  } catch (error: any) {
    logger.error("Failed to get seller dashboard stats", {
      error: error.message,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get comprehensive analytics data for admin
export const getComprehensiveAnalytics = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const days = parseInt(req.query.days as string) || 30;
    const topProductsLimit = parseInt(req.query.topProductsLimit as string) || 10;

    const [dashboardStats, topProducts, revenueData, userGrowthData] = await Promise.all([
      getDashboardStats(),
      getTopProducts(topProductsLimit),
      getRevenueData(days),
      getUserGrowthData(days),
    ]);

    res.status(200).json({
      message: "Comprehensive analytics retrieved successfully",
      data: {
        dashboardStats,
        topProducts,
        revenueData,
        userGrowthData,
      },
    });
  } catch (error: any) {
    logger.error("Failed to get comprehensive analytics", {
      error: error.message,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};
