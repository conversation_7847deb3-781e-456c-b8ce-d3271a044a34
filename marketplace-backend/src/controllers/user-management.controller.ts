import { Response } from "express";
import { AuthenticatedRequest } from "../types";
import {
  getAllUsers,
  getUserStats,
  getUserById,
  updateUserRole,
  toggleUserStatus,
  deleteUser,
  getRecentUserActivities,
} from "../services/user-management.service";
import logger from "../utils/logger";

// Get all users with filtering and pagination
export const getAllUsersController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const search = req.query.search as string;
    const role = req.query.role as string;
    const status = req.query.status as string;

    const result = await getAllUsers(page, limit, search, role, status);

    res.status(200).json({
      message: "Users retrieved successfully",
      data: result,
    });
  } catch (error: any) {
    logger.error("Failed to get users", {
      error: error.message,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get user statistics
export const getUserStatsController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const stats = await getUserStats();

    res.status(200).json({
      message: "User statistics retrieved successfully",
      data: stats,
    });
  } catch (error: any) {
    logger.error("Failed to get user statistics", {
      error: error.message,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get user by ID
export const getUserByIdController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { userId } = req.params;
    const user = await getUserById(userId);

    if (!user) {
      res.status(404).json({ message: "User not found" });
      return;
    }

    res.status(200).json({
      message: "User retrieved successfully",
      data: user,
    });
  } catch (error: any) {
    logger.error("Failed to get user by ID", {
      error: error.message,
      userId: req.user?.id,
      targetUserId: req.params.userId,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

// Update user role
export const updateUserRoleController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { userId } = req.params;
    const { role } = req.body;

    if (!["buyer", "seller", "admin"].includes(role)) {
      res.status(400).json({ message: "Invalid role specified" });
      return;
    }

    // Prevent users from changing their own role
    if (userId === req.user?.id) {
      res.status(403).json({ message: "Cannot change your own role" });
      return;
    }

    const updatedUser = await updateUserRole(userId, role);

    if (!updatedUser) {
      res.status(404).json({ message: "User not found" });
      return;
    }

    logger.info("User role updated", {
      adminId: req.user?.id,
      targetUserId: userId,
      newRole: role,
    });

    res.status(200).json({
      message: "User role updated successfully",
      data: updatedUser,
    });
  } catch (error: any) {
    logger.error("Failed to update user role", {
      error: error.message,
      userId: req.user?.id,
      targetUserId: req.params.userId,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

// Toggle user status (suspend/unsuspend)
export const toggleUserStatusController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { userId } = req.params;
    const { suspend } = req.body;

    // Prevent users from suspending themselves
    if (userId === req.user?.id) {
      res.status(403).json({ message: "Cannot change your own status" });
      return;
    }

    const updatedUser = await toggleUserStatus(userId, suspend);

    if (!updatedUser) {
      res.status(404).json({ message: "User not found" });
      return;
    }

    logger.info("User status updated", {
      adminId: req.user?.id,
      targetUserId: userId,
      suspended: suspend,
    });

    res.status(200).json({
      message: `User ${suspend ? "suspended" : "unsuspended"} successfully`,
      data: updatedUser,
    });
  } catch (error: any) {
    logger.error("Failed to update user status", {
      error: error.message,
      userId: req.user?.id,
      targetUserId: req.params.userId,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

// Delete user
export const deleteUserController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { userId } = req.params;

    // Prevent users from deleting themselves
    if (userId === req.user?.id) {
      res.status(403).json({ message: "Cannot delete your own account" });
      return;
    }

    const deleted = await deleteUser(userId);

    if (!deleted) {
      res.status(404).json({ message: "User not found or could not be deleted" });
      return;
    }

    logger.warn("User deleted", {
      adminId: req.user?.id,
      deletedUserId: userId,
    });

    res.status(200).json({
      message: "User deleted successfully",
    });
  } catch (error: any) {
    logger.error("Failed to delete user", {
      error: error.message,
      userId: req.user?.id,
      targetUserId: req.params.userId,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get recent user activities
export const getRecentUserActivitiesController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    const activities = await getRecentUserActivities(limit);

    res.status(200).json({
      message: "Recent user activities retrieved successfully",
      data: activities,
    });
  } catch (error: any) {
    logger.error("Failed to get recent user activities", {
      error: error.message,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};
