import { Response } from "express";
import { AuthenticatedRequest } from "../types";
import inventoryService from "../services/inventory.service";
import { StockMovementType } from "../entities/Inventory.entity";

export const createInventory = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const {
      productId,
      currentStock,
      lowStockThreshold,
      reorderPoint,
      reorderQuantity,
      sku,
      location,
      costPrice,
    } = req.body;

    const sellerId = req.user!.id;

    const inventory = await inventoryService.createInventory({
      productId,
      currentStock: Number(currentStock),
      lowStockThreshold: lowStockThreshold
        ? Number(lowStockThreshold)
        : undefined,
      reorderPoint: reorderPoint ? Number(reorderPoint) : undefined,
      reorderQuantity: reorderQuantity ? Number(reorderQuantity) : undefined,
      sku,
      location,
      costPrice: costPrice ? Number(costPrice) : undefined,
      sellerId,
    });

    res.status(201).json({
      success: true,
      data: inventory,
      message: "Inventory created successfully",
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      message: error.message || "Failed to create inventory",
    });
  }
};

export const updateStock = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { inventoryId } = req.params;
    const { quantity, type, reason, reference } = req.body;

    if (!Object.values(StockMovementType).includes(type)) {
      return res.status(400).json({
        success: false,
        message: "Invalid stock movement type",
      });
    }

    const inventory = await inventoryService.updateStock({
      inventoryId,
      quantity: Number(quantity),
      type,
      reason,
      reference,
      performedBy: req.user!.id,
    });

    res.json({
      success: true,
      data: inventory,
      message: "Stock updated successfully",
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      message: error.message || "Failed to update stock",
    });
  }
};

export const getSellerInventory = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const sellerId = req.user!.id;
    const { status, lowStock, outOfStock, search, page, limit } = req.query;

    const filters = {
      status: status as string,
      lowStock: lowStock === "true",
      outOfStock: outOfStock === "true",
      search: search as string,
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
    };

    const result = await inventoryService.getSellerInventory(sellerId, filters);

    res.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to fetch inventory",
    });
  }
};

export const getInventoryByProduct = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const { productId } = req.params;

    const inventory = await inventoryService.getInventoryByProduct(productId);

    if (!inventory) {
      return res.status(404).json({
        success: false,
        message: "Inventory not found for this product",
      });
    }

    res.json({
      success: true,
      data: inventory,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to fetch inventory",
    });
  }
};

export const getLowStockItems = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const sellerId = req.user!.role === "admin" ? undefined : req.user!.id;

    const lowStockItems = await inventoryService.getLowStockItems(sellerId);

    res.json({
      success: true,
      data: lowStockItems,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to fetch low stock items",
    });
  }
};

export const getInventoryStats = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const sellerId = req.user!.role === "admin" ? undefined : req.user!.id;

    const stats = await inventoryService.getInventoryStats(sellerId);

    res.json({
      success: true,
      data: stats,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to fetch inventory stats",
    });
  }
};

// Update inventory details
export const updateInventory = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const { id } = req.params;
    const {
      newQuantity,
      lowStockThreshold,
      reorderPoint,
      reorderQuantity,
      sku,
      location,
      costPrice,
      reason,
    } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const result = await inventoryService.updateInventoryDetails({
      inventoryId: id,
      newQuantity,
      lowStockThreshold,
      reorderPoint,
      reorderQuantity,
      sku,
      location,
      costPrice,
      reason: reason || "Inventory details updated",
      performedBy: userId,
    });

    res.json({
      success: true,
      data: result,
      message: "Inventory updated successfully",
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      message: error.message || "Failed to update inventory",
    });
  }
};

export const bulkUpdateStock = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const { updates } = req.body;

    if (!Array.isArray(updates)) {
      return res.status(400).json({
        success: false,
        message: "Updates must be an array",
      });
    }

    const results = [];

    for (const update of updates) {
      try {
        const inventory = await inventoryService.updateStock({
          inventoryId: update.inventoryId,
          quantity: Number(update.quantity),
          type: update.type,
          reason: update.reason || "Bulk update",
          reference: update.reference,
          performedBy: req.user!.id,
        });
        results.push({
          success: true,
          inventoryId: update.inventoryId,
          data: inventory,
        });
      } catch (error: any) {
        results.push({
          success: false,
          inventoryId: update.inventoryId,
          error: error.message,
        });
      }
    }

    res.json({
      success: true,
      data: results,
      message: "Bulk update completed",
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to perform bulk update",
    });
  }
};

export const adjustStock = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { inventoryId } = req.params;
    const { newQuantity, reason } = req.body;

    const inventory = await inventoryService.updateStock({
      inventoryId,
      quantity: Number(newQuantity),
      type: StockMovementType.ADJUSTMENT,
      reason: reason || "Stock adjustment",
      performedBy: req.user!.id,
    });

    res.json({
      success: true,
      data: inventory,
      message: "Stock adjusted successfully",
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      message: error.message || "Failed to adjust stock",
    });
  }
};
