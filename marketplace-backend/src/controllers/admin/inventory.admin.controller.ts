import { Response } from "express";
import { AuthenticatedRequest } from "../../types";
import inventoryService from "../../services/inventory.service";
import { adminInventoryService } from "../../services/admin/inventory.admin.service";
import logger from "../../utils/logger";

export const getAllInventoryController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      status,
      sellerId,
      category,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = req.query;

    logger.info("Admin fetching all inventory", {
      adminId: req.user?.id,
      filters: { page, limit, search, status, sellerId, category },
    });

    const filters = {
      page: Number(page),
      limit: Number(limit),
      search: search as string,
      status: status as string,
      sellerId: sellerId as string,
      category: category as string,
      sortBy: sortBy as string,
      sortOrder: sortOrder as "asc" | "desc",
    };

    const result = await adminInventoryService.getAllInventory(filters);

    res.status(200).json({
      success: true,
      data: result,
      message: "Inventory retrieved successfully",
    });
  } catch (error: any) {
    logger.error("Failed to get admin inventory", {
      adminId: req.user?.id,
      error: error.message,
    });
    res.status(500).json({
      success: false,
      message: "Failed to retrieve inventory",
    });
  }
};

export const getInventoryStatsController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    logger.info("Admin fetching inventory stats", {
      adminId: req.user?.id,
    });

    const stats = await adminInventoryService.getComprehensiveStats();

    res.status(200).json({
      success: true,
      data: stats,
      message: "Inventory statistics retrieved successfully",
    });
  } catch (error: any) {
    logger.error("Failed to get inventory stats", {
      adminId: req.user?.id,
      error: error.message,
    });
    res.status(500).json({
      success: false,
      message: "Failed to retrieve inventory statistics",
    });
  }
};

export const getLowStockAlertsController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { severity = "all" } = req.query;

    logger.info("Admin fetching low stock alerts", {
      adminId: req.user?.id,
      severity,
    });

    const alerts = await adminInventoryService.getLowStockAlerts(
      severity as string
    );

    res.status(200).json({
      success: true,
      data: alerts,
      message: "Low stock alerts retrieved successfully",
    });
  } catch (error: any) {
    logger.error("Failed to get low stock alerts", {
      adminId: req.user?.id,
      error: error.message,
    });
    res.status(500).json({
      success: false,
      message: "Failed to retrieve low stock alerts",
    });
  }
};

export const getInventoryAnalyticsController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { period = "30d" } = req.query;

    logger.info("Admin fetching inventory analytics", {
      adminId: req.user?.id,
      period,
    });

    const analytics = await adminInventoryService.getInventoryAnalytics(
      period as string
    );

    res.status(200).json({
      success: true,
      data: analytics,
      message: "Inventory analytics retrieved successfully",
    });
  } catch (error: any) {
    logger.error("Failed to get inventory analytics", {
      adminId: req.user?.id,
      error: error.message,
    });
    res.status(500).json({
      success: false,
      message: "Failed to retrieve inventory analytics",
    });
  }
};

export const getInventoryByIdController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    logger.info("Admin fetching inventory by ID", {
      adminId: req.user?.id,
      inventoryId: id,
    });

    const inventory = await adminInventoryService.getInventoryById(id);

    if (!inventory) {
      res.status(404).json({
        success: false,
        message: "Inventory item not found",
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: inventory,
      message: "Inventory item retrieved successfully",
    });
  } catch (error: any) {
    logger.error("Failed to get inventory by ID", {
      adminId: req.user?.id,
      inventoryId: req.params.id,
      error: error.message,
    });
    res.status(500).json({
      success: false,
      message: "Failed to retrieve inventory item",
    });
  }
};

export const getInventoryHistoryController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20 } = req.query;

    logger.info("Admin fetching inventory history", {
      adminId: req.user?.id,
      inventoryId: id,
    });

    const history = await adminInventoryService.getInventoryHistory(
      id,
      Number(page),
      Number(limit)
    );

    res.status(200).json({
      success: true,
      data: history,
      message: "Inventory history retrieved successfully",
    });
  } catch (error: any) {
    logger.error("Failed to get inventory history", {
      adminId: req.user?.id,
      inventoryId: req.params.id,
      error: error.message,
    });
    res.status(500).json({
      success: false,
      message: "Failed to retrieve inventory history",
    });
  }
};

export const bulkUpdateInventoryController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { updates } = req.body;
    const adminId = req.user!.id;

    if (!Array.isArray(updates) || updates.length === 0) {
      res.status(400).json({
        success: false,
        message: "Updates array is required and cannot be empty",
      });
      return;
    }

    logger.info("Admin performing bulk inventory update", {
      adminId,
      updateCount: updates.length,
    });

    const results = await adminInventoryService.bulkUpdateInventory(
      updates,
      adminId
    );

    res.status(200).json({
      success: true,
      data: results,
      message: "Bulk update completed successfully",
    });
  } catch (error: any) {
    logger.error("Failed to perform bulk update", {
      adminId: req.user?.id,
      error: error.message,
    });
    res.status(500).json({
      success: false,
      message: "Failed to perform bulk update",
    });
  }
};

export const updateInventoryStatusController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const { status, reason } = req.body;
    const adminId = req.user!.id;

    logger.info("Admin updating inventory status", {
      adminId,
      inventoryId: id,
      newStatus: status,
    });

    const inventory = await adminInventoryService.updateInventoryStatus(
      id,
      status,
      reason || "Status updated by admin",
      adminId
    );

    res.status(200).json({
      success: true,
      data: inventory,
      message: "Inventory status updated successfully",
    });
  } catch (error: any) {
    logger.error("Failed to update inventory status", {
      adminId: req.user?.id,
      inventoryId: req.params.id,
      error: error.message,
    });
    res.status(500).json({
      success: false,
      message: "Failed to update inventory status",
    });
  }
};

export const autoRestockController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { inventoryIds, restockQuantity } = req.body;
    const adminId = req.user!.id;

    if (!Array.isArray(inventoryIds) || inventoryIds.length === 0) {
      res.status(400).json({
        success: false,
        message: "Inventory IDs array is required",
      });
      return;
    }

    logger.info("Admin triggering auto-restock", {
      adminId,
      inventoryCount: inventoryIds.length,
      restockQuantity,
    });

    const results = await adminInventoryService.autoRestock(
      inventoryIds,
      restockQuantity,
      adminId
    );

    res.status(200).json({
      success: true,
      data: results,
      message: "Auto-restock completed successfully",
    });
  } catch (error: any) {
    logger.error("Failed to perform auto-restock", {
      adminId: req.user?.id,
      error: error.message,
    });
    res.status(500).json({
      success: false,
      message: "Failed to perform auto-restock",
    });
  }
};

export const exportInventoryReportController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { format = "csv" } = req.query;
    const adminId = req.user!.id;

    logger.info("Admin exporting inventory report", {
      adminId,
      format,
    });

    const report = await adminInventoryService.exportInventoryReport(
      format as string
    );

    // Set appropriate headers for file download
    const filename = `inventory-report-${new Date().toISOString().split('T')[0]}.${format}`;
    
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', getContentType(format as string));

    res.status(200).send(report);
  } catch (error: any) {
    logger.error("Failed to export inventory report", {
      adminId: req.user?.id,
      error: error.message,
    });
    res.status(500).json({
      success: false,
      message: "Failed to export inventory report",
    });
  }
};

function getContentType(format: string): string {
  switch (format) {
    case 'csv':
      return 'text/csv';
    case 'excel':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case 'pdf':
      return 'application/pdf';
    default:
      return 'text/plain';
  }
}
