import { Response } from "express";
import { AuthenticatedRequest } from "../types";
import { getRecentActivities, getActivitySummary } from "../services/activity.service";
import logger from "../utils/logger";

// Get recent system activities
export const getRecentActivitiesController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    const activities = await getRecentActivities(limit);

    res.status(200).json({
      message: "Recent activities retrieved successfully",
      data: activities,
    });
  } catch (error: any) {
    logger.error("Failed to get recent activities", {
      error: error.message,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get activity summary
export const getActivitySummaryController = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const summary = await getActivitySummary();

    res.status(200).json({
      message: "Activity summary retrieved successfully",
      data: summary,
    });
  } catch (error: any) {
    logger.error("Failed to get activity summary", {
      error: error.message,
      userId: req.user?.id,
    });
    res.status(500).json({ message: "Internal server error" });
  }
};
