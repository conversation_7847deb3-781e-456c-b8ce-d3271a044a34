import { getEmailBaseTemplate } from './email-base.template';

interface OrderItem {
  product: {
    name: string;
    price: number;
  };
  quantity: number;
  price: number;
}

interface Order {
  id: string;
  total: number;
  items: OrderItem[];
  shippingAddress?: {
    fullName: string;
    street: string;
    city: string;
    county: string;
    postalCode: string;
    country: string;
  };
  paymentInfo?: {
    method: string;
    transactionId?: string;
  };
}

export const getOrderConfirmationTemplate = (
  customerName: string,
  order: Order
) => {
  const orderNumber = `#${order.id.slice(-8).toUpperCase()}`;
  
  const orderItemsHtml = order.items
    .map(
      (item) => `
        <div class="order-item">
          <div class="item-details">
            <div class="item-name">${item.product.name}</div>
            <div class="item-meta">Quantity: ${item.quantity} × KES ${item.product.price.toLocaleString()}</div>
          </div>
          <div class="item-price">KES ${item.price.toLocaleString()}</div>
        </div>
      `
    )
    .join('');

  const shippingAddressHtml = order.shippingAddress
    ? `
        <div style="margin: 30px 0;">
          <h3 style="color: var(--marketplace-charcoal); margin-bottom: 15px;">
            📍 Shipping Address
          </h3>
          <div style="background-color: var(--marketplace-beige); padding: 20px; border-radius: 8px; line-height: 1.6;">
            <strong>${order.shippingAddress.fullName}</strong><br>
            ${order.shippingAddress.street}<br>
            ${order.shippingAddress.city}, ${order.shippingAddress.county}<br>
            ${order.shippingAddress.postalCode}<br>
            ${order.shippingAddress.country}
          </div>
        </div>
      `
    : '';

  const paymentInfoHtml = order.paymentInfo
    ? `
        <div style="margin: 30px 0;">
          <h3 style="color: var(--marketplace-charcoal); margin-bottom: 15px;">
            💳 Payment Information
          </h3>
          <div style="background-color: var(--marketplace-beige); padding: 20px; border-radius: 8px;">
            <div style="margin-bottom: 8px;">
              <strong>Payment Method:</strong> ${order.paymentInfo.method}
            </div>
            ${order.paymentInfo.transactionId ? `
              <div>
                <strong>Transaction ID:</strong> ${order.paymentInfo.transactionId}
              </div>
            ` : ''}
          </div>
        </div>
      `
    : '';

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="font-size: 48px; margin-bottom: 10px;">🎉</div>
      <h1 style="color: var(--marketplace-charcoal); font-family: 'DM Serif Display', serif; font-size: 28px; margin-bottom: 10px;">
        Order Confirmed!
      </h1>
      <p style="color: var(--marketplace-medium-gray); font-size: 16px;">
        Thank you for your order, ${customerName}. We're excited to get your items to you!
      </p>
    </div>

    <div style="background-color: var(--marketplace-warm); color: white; padding: 20px; border-radius: 8px; text-align: center; margin: 30px 0;">
      <h2 style="margin: 0; font-size: 24px;">Order ${orderNumber}</h2>
      <p style="margin: 8px 0 0 0; opacity: 0.9;">Placed on ${new Date().toLocaleDateString('en-KE', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })}</p>
    </div>

    <div class="order-summary">
      <h3 style="color: var(--marketplace-charcoal); margin-bottom: 20px; display: flex; align-items: center;">
        🛍️ Order Summary
      </h3>
      ${orderItemsHtml}
      <div class="order-item total-row">
        <div class="item-details">
          <div class="item-name">Total</div>
        </div>
        <div class="item-price" style="font-size: 20px;">KES ${order.total.toLocaleString()}</div>
      </div>
    </div>

    ${shippingAddressHtml}
    ${paymentInfoHtml}

    <div style="text-align: center; margin: 40px 0;">
      <a href="#" class="btn">
        📱 Track Your Order
      </a>
      <br>
      <a href="#" class="btn btn-secondary" style="margin-left: 10px;">
        📞 Contact Seller
      </a>
    </div>

    <div style="background-color: var(--marketplace-beige); padding: 20px; border-radius: 8px; margin: 30px 0;">
      <h4 style="color: var(--marketplace-charcoal); margin-bottom: 15px;">📋 What's Next?</h4>
      <ul style="color: var(--marketplace-medium-gray); padding-left: 20px; line-height: 1.8;">
        <li>We'll send you updates as your order is processed</li>
        <li>Your seller will prepare your items for shipping</li>
        <li>You'll receive tracking information once shipped</li>
        <li>Estimated delivery: 3-7 business days</li>
      </ul>
    </div>

    <div style="text-align: center; margin: 30px 0; padding: 20px; border: 2px dashed var(--marketplace-warm); border-radius: 8px;">
      <h4 style="color: var(--marketplace-warm); margin-bottom: 10px;">🎁 Thank You for Supporting Local Sellers!</h4>
      <p style="color: var(--marketplace-medium-gray); margin: 0;">
        Your purchase helps support entrepreneurs and communities across Africa.
      </p>
    </div>
  `;

  return getEmailBaseTemplate(content, `Order Confirmation - ${orderNumber}`);
};
