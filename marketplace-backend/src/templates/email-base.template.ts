// Base email template with marketplace branding
export const getEmailBaseTemplate = (content: string, title: string) => `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        /* Marketplace Theme Colors */
        :root {
            --marketplace-ivory: #fefcf8;
            --marketplace-charcoal: #2c2c2c;
            --marketplace-beige: #f5f2ed;
            --marketplace-warm: #D2691E;
            --marketplace-indigo: #4B0082;
            --marketplace-dark-gray: #4a453f;
            --marketplace-medium-gray: #8b8680;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--marketplace-charcoal);
            background-color: var(--marketplace-beige);
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: var(--marketplace-ivory);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .email-header {
            background: linear-gradient(135deg, var(--marketplace-warm) 0%, var(--marketplace-indigo) 100%);
            padding: 30px 40px;
            text-align: center;
        }
        
        .logo {
            color: white;
            font-size: 28px;
            font-weight: 700;
            font-family: 'DM Serif Display', serif;
            margin-bottom: 8px;
        }
        
        .tagline {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 400;
        }
        
        .email-content {
            padding: 40px;
        }
        
        .email-footer {
            background-color: var(--marketplace-beige);
            padding: 30px 40px;
            text-align: center;
            border-top: 1px solid #e5e5e5;
        }
        
        .footer-text {
            color: var(--marketplace-medium-gray);
            font-size: 12px;
            line-height: 1.5;
        }
        
        .footer-links {
            margin-top: 15px;
        }
        
        .footer-links a {
            color: var(--marketplace-warm);
            text-decoration: none;
            margin: 0 10px;
            font-size: 12px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: var(--marketplace-warm);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
            transition: background-color 0.3s ease;
        }
        
        .btn:hover {
            background-color: #B8621B;
        }
        
        .btn-secondary {
            background-color: transparent;
            color: var(--marketplace-warm);
            border: 2px solid var(--marketplace-warm);
        }
        
        .btn-secondary:hover {
            background-color: var(--marketplace-warm);
            color: white;
        }
        
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-pending {
            background-color: #FEF3C7;
            color: #92400E;
        }
        
        .status-paid {
            background-color: #D1FAE5;
            color: #065F46;
        }
        
        .status-shipped {
            background-color: #DBEAFE;
            color: #1E40AF;
        }
        
        .status-fulfilled {
            background-color: #D1FAE5;
            color: #065F46;
        }
        
        .status-cancelled {
            background-color: #FEE2E2;
            color: #991B1B;
        }
        
        .order-summary {
            background-color: var(--marketplace-beige);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e5e5e5;
        }
        
        .order-item:last-child {
            border-bottom: none;
        }
        
        .item-details {
            flex: 1;
        }
        
        .item-name {
            font-weight: 600;
            color: var(--marketplace-charcoal);
        }
        
        .item-meta {
            font-size: 14px;
            color: var(--marketplace-medium-gray);
        }
        
        .item-price {
            font-weight: 600;
            color: var(--marketplace-warm);
        }
        
        .total-row {
            font-weight: 700;
            font-size: 18px;
            color: var(--marketplace-charcoal);
            border-top: 2px solid var(--marketplace-warm);
            padding-top: 15px;
            margin-top: 15px;
        }
        
        .icon {
            width: 20px;
            height: 20px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 8px;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .email-header,
            .email-content,
            .email-footer {
                padding: 20px;
            }
            
            .order-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <div class="logo">🌍 Global Marketplace</div>
            <div class="tagline">Connecting Communities, Empowering Commerce</div>
        </div>
        
        <div class="email-content">
            ${content}
        </div>
        
        <div class="email-footer">
            <div class="footer-text">
                <strong>Global Marketplace</strong><br>
                Your trusted platform for authentic African products and global commerce.<br>
                This email was sent to you because you have an account with us.
            </div>
            <div class="footer-links">
                <a href="#">Help Center</a>
                <a href="#">Contact Support</a>
                <a href="#">Privacy Policy</a>
                <a href="#">Unsubscribe</a>
            </div>
        </div>
    </div>
</body>
</html>
`;
