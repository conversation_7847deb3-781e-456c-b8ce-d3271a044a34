import { getEmailBaseTemplate } from "./email-base.template";

type OrderStatus = "pending" | "paid" | "shipped" | "fulfilled" | "cancelled";

interface StatusConfig {
  icon: string;
  title: string;
  message: string;
  badgeClass: string;
  nextSteps: string[];
}

const statusConfigs: Record<OrderStatus, StatusConfig> = {
  pending: {
    icon: "⏳",
    title: "Order Received",
    message: "We've received your order and it's being processed.",
    badgeClass: "status-pending",
    nextSteps: [
      "Payment confirmation is pending",
      "Seller will be notified once payment is confirmed",
      "You'll receive updates as your order progresses",
    ],
  },
  paid: {
    icon: "✅",
    title: "Payment Confirmed",
    message:
      "Great news! Your payment has been confirmed and your order is being prepared.",
    badgeClass: "status-paid",
    nextSteps: [
      "Your seller has been notified and will prepare your items",
      "Items will be carefully packaged for shipping",
      "You'll receive tracking information once shipped",
    ],
  },
  shipped: {
    icon: "🚚",
    title: "Order Shipped",
    message:
      "Your order is on its way! Your items have been shipped and are heading to you.",
    badgeClass: "status-shipped",
    nextSteps: [
      "Track your package using the tracking number below",
      "Estimated delivery: 3-7 business days",
      "You'll receive a notification when delivered",
    ],
  },
  fulfilled: {
    icon: "🎉",
    title: "Order Delivered",
    message: "Fantastic! Your order has been successfully delivered.",
    badgeClass: "status-fulfilled",
    nextSteps: [
      "We hope you love your purchase!",
      "Consider leaving a review to help other buyers",
      "Contact the seller if you have any questions",
    ],
  },
  cancelled: {
    icon: "❌",
    title: "Order Cancelled",
    message:
      "Your order has been cancelled. If you were charged, a refund will be processed.",
    badgeClass: "status-cancelled",
    nextSteps: [
      "Refunds typically process within 3-5 business days",
      "You'll receive a separate email confirmation for any refund",
      "Contact support if you have questions about this cancellation",
    ],
  },
};

export const getOrderStatusUpdateTemplate = (
  customerName: string,
  orderId: string,
  orderNumber: string,
  status: OrderStatus,
  trackingNumber?: string
) => {
  const config = statusConfigs[status];

  // Add error handling for undefined config
  if (!config) {
    console.error(
      `Invalid order status: ${status}. Available statuses:`,
      Object.keys(statusConfigs)
    );
    throw new Error(
      `Invalid order status: ${status}. Must be one of: ${Object.keys(statusConfigs).join(", ")}`
    );
  }

  const trackingSection =
    trackingNumber && status === "shipped"
      ? `
    <div style="background-color: var(--marketplace-beige); padding: 20px; border-radius: 8px; margin: 30px 0; text-align: center;">
      <h3 style="color: var(--marketplace-charcoal); margin-bottom: 15px;">📦 Tracking Information</h3>
      <div style="background-color: white; padding: 15px; border-radius: 6px; border: 2px solid var(--marketplace-warm);">
        <div style="font-size: 18px; font-weight: 600; color: var(--marketplace-warm); margin-bottom: 5px;">
          Tracking Number
        </div>
        <div style="font-size: 24px; font-weight: 700; color: var(--marketplace-charcoal); letter-spacing: 2px;">
          ${trackingNumber}
        </div>
      </div>
      <a href="#" class="btn" style="margin-top: 20px;">
        🔍 Track Package
      </a>
    </div>
  `
      : "";

  const progressSteps = [
    { name: "Order Placed", status: "pending", completed: true },
    {
      name: "Payment Confirmed",
      status: "paid",
      completed: ["paid", "shipped", "fulfilled"].includes(status),
    },
    {
      name: "Shipped",
      status: "shipped",
      completed: ["shipped", "fulfilled"].includes(status),
    },
    {
      name: "Delivered",
      status: "fulfilled",
      completed: status === "fulfilled",
    },
  ];

  const progressHtml =
    status !== "cancelled"
      ? `
    <div style="margin: 30px 0;">
      <h3 style="color: var(--marketplace-charcoal); margin-bottom: 20px; text-align: center;">📈 Order Progress</h3>
      <div style="display: flex; justify-content: space-between; align-items: center; position: relative; margin: 20px 0;">
        ${progressSteps
          .map(
            (step, index) => `
          <div style="text-align: center; flex: 1; position: relative;">
            <div style="
              width: 40px; 
              height: 40px; 
              border-radius: 50%; 
              margin: 0 auto 10px; 
              display: flex; 
              align-items: center; 
              justify-content: center;
              background-color: ${step.completed ? "var(--marketplace-warm)" : "#e5e5e5"};
              color: ${step.completed ? "white" : "var(--marketplace-medium-gray)"};
              font-weight: 600;
              font-size: 18px;
            ">
              ${step.completed ? "✓" : index + 1}
            </div>
            <div style="
              font-size: 12px; 
              color: ${step.completed ? "var(--marketplace-charcoal)" : "var(--marketplace-medium-gray)"};
              font-weight: ${step.completed ? "600" : "400"};
            ">
              ${step.name}
            </div>
            ${
              index < progressSteps.length - 1
                ? `
              <div style="
                position: absolute;
                top: 20px;
                left: 60%;
                width: 80%;
                height: 2px;
                background-color: ${step.completed && progressSteps[index + 1].completed ? "var(--marketplace-warm)" : "#e5e5e5"};
                z-index: -1;
              "></div>
            `
                : ""
            }
          </div>
        `
          )
          .join("")}
      </div>
    </div>
  `
      : "";

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="font-size: 48px; margin-bottom: 10px;">${config.icon}</div>
      <h1 style="color: var(--marketplace-charcoal); font-family: 'DM Serif Display', serif; font-size: 28px; margin-bottom: 10px;">
        ${config.title}
      </h1>
      <p style="color: var(--marketplace-medium-gray); font-size: 16px;">
        Hi ${customerName}, we have an update on your order!
      </p>
    </div>

    <div style="background-color: var(--marketplace-warm); color: white; padding: 20px; border-radius: 8px; text-align: center; margin: 30px 0;">
      <h2 style="margin: 0 0 10px 0; font-size: 20px;">Order ${orderNumber}</h2>
      <span class="${config.badgeClass}" style="background-color: rgba(255,255,255,0.2); color: white;">
        ${status.toUpperCase()}
      </span>
    </div>

    <div style="background-color: var(--marketplace-beige); padding: 25px; border-radius: 8px; margin: 30px 0; text-align: center;">
      <p style="color: var(--marketplace-charcoal); font-size: 18px; margin: 0; line-height: 1.6;">
        ${config.message}
      </p>
    </div>

    ${progressHtml}
    ${trackingSection}

    <div style="background-color: var(--marketplace-beige); padding: 20px; border-radius: 8px; margin: 30px 0;">
      <h4 style="color: var(--marketplace-charcoal); margin-bottom: 15px;">📋 What's Next?</h4>
      <ul style="color: var(--marketplace-medium-gray); padding-left: 20px; line-height: 1.8; margin: 0;">
        ${config.nextSteps.map((step) => `<li>${step}</li>`).join("")}
      </ul>
    </div>

    <div style="text-align: center; margin: 40px 0;">
      <a href="#" class="btn">
        📱 View Order Details
      </a>
      <br>
      <a href="#" class="btn btn-secondary" style="margin-left: 10px;">
        💬 Contact Support
      </a>
    </div>

    <div style="text-align: center; margin: 30px 0; padding: 20px; border: 1px solid #e5e5e5; border-radius: 8px;">
      <p style="color: var(--marketplace-medium-gray); margin: 0; font-size: 14px;">
        Questions about your order? We're here to help!<br>
        Reply to this email or contact our support team.
      </p>
    </div>
  `;

  return getEmailBaseTemplate(content, `Order Update - ${orderNumber}`);
};
