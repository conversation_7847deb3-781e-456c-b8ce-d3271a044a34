<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Status Update</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #4a69bd;
      color: white;
      padding: 20px;
      text-align: center;
      border-radius: 5px 5px 0 0;
    }
    .content {
      background-color: #f9f9f9;
      padding: 20px;
      border-left: 1px solid #ddd;
      border-right: 1px solid #ddd;
    }
    .footer {
      background-color: #f1f1f1;
      padding: 15px;
      text-align: center;
      font-size: 12px;
      color: #666;
      border-radius: 0 0 5px 5px;
      border: 1px solid #ddd;
    }
    .status {
      display: inline-block;
      padding: 5px 10px;
      border-radius: 3px;
      font-weight: bold;
      text-transform: uppercase;
    }
    .status-pending {
      background-color: #ffeaa7;
      color: #d35400;
    }
    .status-paid {
      background-color: #55efc4;
      color: #00b894;
    }
    .status-shipped {
      background-color: #74b9ff;
      color: #0984e3;
    }
    .status-fulfilled {
      background-color: #a29bfe;
      color: #6c5ce7;
    }
    .status-cancelled {
      background-color: #fab1a0;
      color: #d63031;
    }
    .product-info {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      padding: 15px;
      border-radius: 5px;
      margin: 15px 0;
    }
    .button {
      display: inline-block;
      background-color: #4a69bd;
      color: white;
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 5px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Order Status Update</h1>
    </div>
    <div class="content">
      <p>Hello {{name}},</p>
      <p>We're writing to inform you that the status of your order has been updated.</p>
      
      <div class="product-info">
        <p>Product: <strong>{{productTitle}}</strong></p>
        <p>New Status: <span class="status status-{{status}}">{{status}}</span></p>
        <p>Order ID: <strong>{{orderId}}</strong></p>
      </div>
      
      {{#if trackingNumber}}
      <p>Your tracking number is: <strong>{{trackingNumber}}</strong></p>
      <p>You can track your shipment using the button below:</p>
      <a href="{{trackingUrl}}" class="button">Track Shipment</a>
      {{/if}}
      
      <p>You can view your complete order details by clicking the button below:</p>
      <a href="{{orderUrl}}" class="button">View Order Details</a>
      
      <p>If you have any questions about your order, please don't hesitate to contact our customer service team.</p>
      
      <p>Thank you for shopping with us!</p>
      <p>The Marketplace Team</p>
    </div>
    <div class="footer">
      <p>&copy; {{year}} Marketplace. All rights reserved.</p>
      <p>If you have any questions, please contact our support <NAME_EMAIL></p>
    </div>
  </div>
</body>
</html>