import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
} from "typeorm";
import { Product } from "./Product.entity";
import { User } from "./User.entity";

export enum InventoryStatus {
  IN_STOCK = "in_stock",
  LOW_STOCK = "low_stock",
  OUT_OF_STOCK = "out_of_stock",
  DISCONTINUED = "discontinued",
}

export enum StockMovementType {
  PURCHASE = "purchase",
  SALE = "sale",
  ADJUSTMENT = "adjustment",
  RETURN = "return",
  DAMAGE = "damage",
  RESTOCK = "restock",
}

@Entity()
export class Inventory {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @ManyToOne(() => Product, { eager: true })
  product!: Product;

  @Column({ type: "int", default: 0 })
  currentStock!: number;

  @Column({ type: "int", default: 0 })
  reservedStock!: number;

  @Column({ type: "int", default: 5 })
  lowStockThreshold!: number;

  @Column({ type: "int", default: 0 })
  reorderPoint!: number;

  @Column({ type: "int", default: 0 })
  reorderQuantity!: number;

  @Column({
    type: "enum",
    enum: InventoryStatus,
    default: InventoryStatus.IN_STOCK,
  })
  status!: InventoryStatus;

  @Column({ type: "varchar", length: 50, nullable: true })
  sku!: string;

  @Column({ type: "varchar", length: 100, nullable: true })
  location!: string;

  @Column({ type: "decimal", precision: 10, scale: 2, nullable: true })
  costPrice!: number;

  @OneToMany(() => StockMovement, (movement) => movement.inventory)
  stockMovements!: StockMovement[];

  @ManyToOne(() => User)
  seller!: User;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Computed properties
  get availableStock(): number {
    return Math.max(0, this.currentStock - this.reservedStock);
  }

  get isLowStock(): boolean {
    return this.currentStock <= this.lowStockThreshold;
  }

  get isOutOfStock(): boolean {
    return this.currentStock <= 0;
  }

  get needsReorder(): boolean {
    return this.currentStock <= this.reorderPoint;
  }
}

@Entity()
export class StockMovement {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @ManyToOne(() => Inventory, (inventory) => inventory.stockMovements)
  inventory!: Inventory;

  @Column({
    type: "enum",
    enum: StockMovementType,
  })
  type!: StockMovementType;

  @Column({ type: "int" })
  quantity!: number;

  @Column({ type: "int" })
  previousStock!: number;

  @Column({ type: "int" })
  newStock!: number;

  @Column({ type: "varchar", length: 255, nullable: true })
  reason!: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  reference!: string; // Order ID, adjustment ID, etc.

  @ManyToOne(() => User)
  performedBy!: User;

  @CreateDateColumn()
  createdAt!: Date;
}
