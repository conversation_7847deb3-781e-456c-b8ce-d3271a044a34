import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
} from "typeorm";
import { User } from "./User.entity";
import { Order } from "./Order.entity";

export type DisputeStatus = "open" | "investigating" | "resolved" | "closed";
export type DisputeType = "product_issue" | "delivery_issue" | "payment_issue" | "other";

@Entity()
export class Dispute {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @Column()
  title!: string;

  @Column("text")
  description!: string;

  @Column({
    type: "enum",
    enum: ["product_issue", "delivery_issue", "payment_issue", "other"],
    default: "other",
  })
  type!: DisputeType;

  @Column({
    type: "enum",
    enum: ["open", "investigating", "resolved", "closed"],
    default: "open",
  })
  status!: DisputeStatus;

  @Column({ type: "text", nullable: true })
  resolution?: string;

  @Column({ type: "uuid", nullable: true })
  resolvedBy?: string;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: "reportedBy" })
  reporter!: User;

  @Column()
  reportedBy!: string;

  @ManyToOne(() => Order, { eager: true })
  @JoinColumn({ name: "orderId" })
  order!: Order;

  @Column()
  orderId!: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
