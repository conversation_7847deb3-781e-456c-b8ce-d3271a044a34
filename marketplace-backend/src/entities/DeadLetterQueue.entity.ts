import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";

/**
 * Dead Letter Queue Entity
 *
 * Stores failed email attempts that require manual intervention
 * or special handling after all automatic retry attempts have been exhausted.
 */

export type EmailType =
  | "order_confirmation"
  | "order_status_update"
  | "otp_verification"
  | "password_reset"
  | "welcome"
  | "general";

export type FailureReason =
  | "smtp_error"
  | "invalid_email"
  | "rate_limit"
  | "timeout"
  | "authentication_failed"
  | "service_unavailable"
  | "unknown_error";

export type DLQStatus =
  | "pending"
  | "processing"
  | "resolved"
  | "failed_permanently"
  | "cancelled";

@Entity("dead_letter_queue")
@Index(["status", "createdAt"])
@Index(["emailType", "status"])
@Index(["failureReason", "createdAt"])
export class DeadLetterQueue {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @Column({ type: "varchar", length: 50 })
  @Index()
  emailType!: EmailType;

  @Column({ type: "varchar", length: 255 })
  recipientEmail!: string;

  @Column({ type: "varchar", length: 500 })
  subject!: string;

  @Column({ type: "text" })
  htmlContent!: string;

  @Column({ type: "text", nullable: true })
  textContent?: string;

  @Column({ type: "json", nullable: true })
  emailData?: Record<string, any>; // Original email data for reconstruction

  @Column({ type: "varchar", length: 50 })
  @Index()
  failureReason!: FailureReason;

  @Column({ type: "text" })
  errorMessage!: string;

  @Column({ type: "text", nullable: true })
  stackTrace?: string;

  @Column({ type: "int", default: 0 })
  retryAttempts!: number;

  @Column({ type: "int", default: 3 })
  maxRetryAttempts!: number;

  @Column({ type: "timestamp", nullable: true })
  lastRetryAt?: Date;

  @Column({ type: "timestamp", nullable: true })
  nextRetryAt?: Date;

  @Column({ type: "varchar", length: 20, default: "pending" })
  @Index()
  status!: DLQStatus;

  @Column({ type: "varchar", length: 100, nullable: true })
  processingNode?: string; // Which server/instance is processing this

  @Column({ type: "uuid", nullable: true })
  @Index()
  userId?: string; // Associated user for tracking

  @Column({ type: "uuid", nullable: true })
  @Index()
  orderId?: string; // Associated order for order-related emails

  @Column({ type: "text", nullable: true })
  adminNotes?: string; // Notes added by admin during manual processing

  @Column({ type: "varchar", length: 100, nullable: true })
  resolvedBy?: string; // Admin user who resolved this item

  @Column({ type: "timestamp", nullable: true })
  resolvedAt?: Date;

  @Column({ type: "int", default: 1 })
  priority!: number; // 1 = high, 2 = medium, 3 = low

  @Column({ type: "json", nullable: true })
  metadata?: Record<string, any>; // Additional context data

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Helper methods
  canRetry(): boolean {
    return (
      this.retryAttempts < this.maxRetryAttempts &&
      this.status === "pending" &&
      (!this.nextRetryAt || this.nextRetryAt <= new Date())
    );
  }

  incrementRetryAttempt(): void {
    this.retryAttempts += 1;
    this.lastRetryAt = new Date();

    // Calculate next retry time with exponential backoff
    const backoffMinutes = Math.pow(2, this.retryAttempts) * 5; // 5, 10, 20, 40 minutes
    this.nextRetryAt = new Date(Date.now() + backoffMinutes * 60 * 1000);

    if (this.retryAttempts >= this.maxRetryAttempts) {
      this.status = "failed_permanently";
      this.nextRetryAt = undefined;
    }
  }

  markAsProcessing(processingNode: string): void {
    this.status = "processing";
    this.processingNode = processingNode;
  }

  markAsResolved(resolvedBy: string, adminNotes?: string): void {
    this.status = "resolved";
    this.resolvedBy = resolvedBy;
    this.resolvedAt = new Date();
    if (adminNotes) {
      this.adminNotes = adminNotes;
    }
  }

  markAsCancelled(cancelledBy: string, reason?: string): void {
    this.status = "cancelled";
    this.resolvedBy = cancelledBy;
    this.resolvedAt = new Date();
    if (reason) {
      this.adminNotes = `Cancelled: ${reason}`;
    }
  }

  getRetryDelay(): number {
    if (!this.nextRetryAt) return 0;
    return Math.max(0, this.nextRetryAt.getTime() - Date.now());
  }

  isStale(staleThresholdMinutes: number = 30): boolean {
    if (this.status !== "processing") return false;

    const staleThreshold = new Date(
      Date.now() - staleThresholdMinutes * 60 * 1000
    );
    return this.updatedAt < staleThreshold;
  }

  getPriorityLabel(): string {
    switch (this.priority) {
      case 1:
        return "High";
      case 2:
        return "Medium";
      case 3:
        return "Low";
      default:
        return "Unknown";
    }
  }

  getAgeInHours(): number {
    return Math.floor(
      (Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60)
    );
  }

  toSummary(): {
    id: string;
    emailType: EmailType;
    recipientEmail: string;
    subject: string;
    failureReason: FailureReason;
    status: DLQStatus;
    retryAttempts: number;
    priority: number;
    ageInHours: number;
    canRetry: boolean;
  } {
    return {
      id: this.id,
      emailType: this.emailType,
      recipientEmail: this.recipientEmail,
      subject: this.subject,
      failureReason: this.failureReason,
      status: this.status,
      retryAttempts: this.retryAttempts,
      priority: this.priority,
      ageInHours: this.getAgeInHours(),
      canRetry: this.canRetry(),
    };
  }
}
