import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from "typeorm";

@Entity()
export class MonitoringMetric {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @CreateDateColumn()
  timestamp!: Date;

  @Column({ type: "varchar", length: 32 })
  type!: "performance" | "timeout";

  @Column({ type: "varchar", length: 16 })
  method!: string;

  @Column({ type: "varchar", length: 255 })
  endpoint!: string;

  @Column({ type: "int", nullable: true })
  statusCode?: number;

  @Column({ type: "int", nullable: true })
  duration?: number;

  @Column({ type: "varchar", length: 255, nullable: true })
  userAgent?: string;

  @Column({ type: "varchar", length: 64, nullable: true })
  ip?: string;

  @Column({ type: "jsonb", nullable: true })
  extra?: Record<string, any>;
}
