import { Request, Response, NextFunction } from "express";
import logger from "../utils/logger";
import { timeoutAnalyzer } from "../utils/timeoutAnalyzer";

/**
 * Global Timeout Middleware
 *
 * Provides configurable timeout protection for routes to prevent
 * hanging requests and improve system reliability.
 */

interface TimeoutOptions {
  timeoutMs?: number;
  errorMessage?: string;
  logTimeout?: boolean;
}

/**
 * Create a timeout middleware with configurable options
 */
export const createTimeoutMiddleware = (options: TimeoutOptions = {}) => {
  const {
    timeoutMs = 30000, // Default 30 seconds
    errorMessage = "Request timeout. Please try again.",
    logTimeout = true,
  } = options;

  return (req: Request, res: Response, next: NextFunction): void => {
    const startTime = Date.now();

    // Create timeout handler
    const timeout = setTimeout(() => {
      if (!res.headersSent) {
        const duration = Date.now() - startTime;

        // Record timeout for analysis
        timeoutAnalyzer.recordTimeout(
          req.originalUrl,
          req.method,
          duration,
          req.get("User-Agent"),
          req.ip
        );

        // Log timeout if enabled
        if (logTimeout) {
          logger.warn("Request timeout", {
            url: req.originalUrl,
            method: req.method,
            duration: `${duration}ms`,
            timeout: `${timeoutMs}ms`,
            ip: req.ip,
            userAgent: req.get("User-Agent"),
          });
        }

        // Send timeout response
        res.status(408).json({
          error: "Request Timeout",
          message: errorMessage,
          timeout: timeoutMs,
          timestamp: new Date().toISOString(),
        });
      }
    }, timeoutMs);

    // Cleanup timeout on response completion
    const cleanup = () => {
      clearTimeout(timeout);
    };

    res.on("finish", cleanup);
    res.on("close", cleanup);
    res.on("error", cleanup);

    next();
  };
};

/**
 * Predefined timeout middlewares for common use cases
 */

/**
 * Quick timeout for fast operations (5 seconds)
 */
export const quickTimeout = createTimeoutMiddleware({
  timeoutMs: 5000,
  errorMessage: "Request took too long. Please try again.",
});

/**
 * Standard timeout for normal operations (15 seconds)
 */
export const standardTimeout = createTimeoutMiddleware({
  timeoutMs: 15000,
  errorMessage: "Request timeout. Please try again.",
});

/**
 * Extended timeout for complex operations (30 seconds)
 */
export const extendedTimeout = createTimeoutMiddleware({
  timeoutMs: 30000,
  errorMessage:
    "Request timeout. The operation may still be processing. Please check back later.",
});

/**
 * Long timeout for heavy operations (60 seconds)
 */
export const longTimeout = createTimeoutMiddleware({
  timeoutMs: 60000,
  errorMessage:
    "Request timeout. This operation may take longer than expected. Please check back later.",
});

/**
 * Global timeout middleware for all routes
 * Use this as a fallback for routes without specific timeout protection
 */
export const globalTimeout = createTimeoutMiddleware({
  timeoutMs: 45000, // 45 seconds global timeout
  errorMessage:
    "Request timeout. Please try again or contact support if the issue persists.",
  logTimeout: true,
});

/**
 * Timeout middleware specifically for order operations
 * These typically involve email sending and can be slower
 */
export const orderTimeout = createTimeoutMiddleware({
  timeoutMs: 20000, // 20 seconds for order operations
  errorMessage:
    "Order operation timeout. Your order may have been processed. Please check your order status.",
  logTimeout: true,
});

/**
 * Timeout middleware for file upload operations
 * File uploads can take longer due to file size and processing
 */
export const uploadTimeout = createTimeoutMiddleware({
  timeoutMs: 120000, // 2 minutes for file uploads
  errorMessage:
    "File upload timeout. Please try with a smaller file or check your connection.",
  logTimeout: true,
});

/**
 * Timeout middleware for admin operations
 * Admin operations might involve complex queries and data processing
 */
export const adminTimeout = createTimeoutMiddleware({
  timeoutMs: 30000, // 30 seconds for admin operations
  errorMessage:
    "Admin operation timeout. Please try again or contact system administrator.",
  logTimeout: true,
});

/**
 * Timeout middleware for analytics and reporting
 * These operations can be resource-intensive
 */
export const analyticsTimeout = createTimeoutMiddleware({
  timeoutMs: 60000, // 1 minute for analytics
  errorMessage:
    "Analytics operation timeout. Please try again or contact support.",
  logTimeout: true,
});
