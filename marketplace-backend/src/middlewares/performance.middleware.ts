import { Request, Response, NextFunction } from "express";
import logger from "../utils/logger";
import { timeoutAnalyzer } from "../utils/timeoutAnalyzer";

/**
 * Performance Tracking Middleware
 *
 * Monitors request performance, detects slow requests, and integrates
 * with timeout analysis for comprehensive performance monitoring.
 */

interface PerformanceMetrics {
  method: string;
  url: string;
  statusCode: number;
  duration: number;
  userAgent?: string;
  ip?: string;
  timestamp: number;
}

class PerformanceTracker {
  private metrics: PerformanceMetrics[] = [];
  private readonly MAX_METRICS = 1000; // Prevent memory leaks
  private readonly SLOW_REQUEST_THRESHOLD = 5000; // 5 seconds
  private readonly CRITICAL_REQUEST_THRESHOLD = 10000; // 10 seconds

  /**
   * Record performance metrics
   */
  recordMetrics(metrics: PerformanceMetrics): void {
    this.metrics.push(metrics);

    // Keep only recent metrics to prevent memory leaks
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }

    // Check for slow requests
    if (metrics.duration > this.SLOW_REQUEST_THRESHOLD) {
      this.handleSlowRequest(metrics);
    }
  }

  /**
   * Handle slow request detection
   */
  private handleSlowRequest(metrics: PerformanceMetrics): void {
    const logLevel =
      metrics.duration > this.CRITICAL_REQUEST_THRESHOLD ? "error" : "warn";
    const severity =
      metrics.duration > this.CRITICAL_REQUEST_THRESHOLD ? "CRITICAL" : "SLOW";

    logger[logLevel](`${severity} REQUEST DETECTED`, {
      url: metrics.url,
      method: metrics.method,
      duration: `${metrics.duration.toFixed(2)}ms`,
      threshold: `${this.SLOW_REQUEST_THRESHOLD}ms`,
      statusCode: metrics.statusCode,
      userAgent: metrics.userAgent,
      ip: metrics.ip,
      timestamp: new Date(metrics.timestamp).toISOString(),
    });

    // Record timeout for analysis if it's critical
    if (metrics.duration > this.CRITICAL_REQUEST_THRESHOLD) {
      timeoutAnalyzer.recordTimeout(
        metrics.url,
        metrics.method,
        metrics.duration,
        metrics.userAgent,
        metrics.ip
      );
    }
  }

  /**
   * Get performance statistics
   */
  getStats(): {
    totalRequests: number;
    avgResponseTime: number;
    slowRequests: number;
    criticalRequests: number;
    byEndpoint: Record<string, { count: number; avgDuration: number }>;
  } {
    const slowRequests = this.metrics.filter(
      (m) => m.duration > this.SLOW_REQUEST_THRESHOLD
    );
    const criticalRequests = this.metrics.filter(
      (m) => m.duration > this.CRITICAL_REQUEST_THRESHOLD
    );

    const avgResponseTime =
      this.metrics.length > 0
        ? this.metrics.reduce((sum, m) => sum + m.duration, 0) /
          this.metrics.length
        : 0;

    const byEndpoint = this.metrics.reduce(
      (acc, metric) => {
        const key = `${metric.method} ${metric.url}`;
        if (!acc[key]) {
          acc[key] = { count: 0, avgDuration: 0, totalDuration: 0 };
        }
        acc[key].count++;
        acc[key].totalDuration += metric.duration;
        acc[key].avgDuration = acc[key].totalDuration / acc[key].count;
        return acc;
      },
      {} as Record<
        string,
        { count: number; avgDuration: number; totalDuration: number }
      >
    );

    // Remove totalDuration from output
    const cleanByEndpoint = Object.entries(byEndpoint).reduce(
      (acc, [key, value]) => {
        acc[key] = {
          count: value.count,
          avgDuration: Math.round(value.avgDuration),
        };
        return acc;
      },
      {} as Record<string, { count: number; avgDuration: number }>
    );

    return {
      totalRequests: this.metrics.length,
      avgResponseTime: Math.round(avgResponseTime),
      slowRequests: slowRequests.length,
      criticalRequests: criticalRequests.length,
      byEndpoint: cleanByEndpoint,
    };
  }

  /**
   * Clear old metrics
   */
  cleanup(): void {
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    this.metrics = this.metrics.filter((m) => m.timestamp > oneHourAgo);

    logger.info("Performance tracker cleanup completed", {
      remainingMetrics: this.metrics.length,
    });
  }

  /**
   * Reset all metrics
   */
  reset(): void {
    this.metrics = [];
    logger.info("Performance tracker reset completed");
  }
}

// Create singleton instance
const performanceTracker = new PerformanceTracker();

/**
 * Performance tracking middleware
 */
export const performanceMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const startTime = process.hrtime.bigint();
  const startTimestamp = Date.now();

  // Log request received
  logger.http(`Request received: ${req.method} ${req.originalUrl}`, {
    ip: req.ip,
    userAgent: req.get("User-Agent") || "unknown",
    query: req.query,
    params: req.params,
  });

  // Track response completion
  res.on("finish", () => {
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1000000; // Convert to ms
    const { statusCode } = res;

    // Record performance metrics
    const metrics: PerformanceMetrics = {
      method: req.method,
      url: req.originalUrl,
      statusCode,
      duration,
      userAgent: req.get("User-Agent"),
      ip: req.ip,
      timestamp: startTimestamp,
    };

    performanceTracker.recordMetrics(metrics);

    // Log response completion
    const logLevel = statusCode >= 400 ? "warn" : "http";
    logger[logLevel](
      `Response sent: ${req.method} ${req.originalUrl} ${statusCode}`,
      {
        statusCode,
        responseTime: `${duration.toFixed(2)}ms`,
        ip: req.ip,
        userAgent: req.get("User-Agent") || "unknown",
      }
    );
  });

  next();
};

/**
 * Get performance tracker instance for external access
 */
export const getPerformanceTracker = () => performanceTracker;

// Cleanup old metrics every hour
setInterval(
  () => {
    performanceTracker.cleanup();
  },
  60 * 60 * 1000
);
