import { Router } from "express";
import {
  checkoutController,
  processPaymentController,
  getMyOrders,
  getSoldOrders,
  updateOrderItemStatus,
  getDefaultShippingAddressController,
  getOrderByIdController,
  getAdminOrdersController,
} from "../controllers/order.controller";
import { authenticate, authRbac } from "../middlewares/auth.middleware";
import { orderTimeout } from "../middlewares/timeout.middleware";

export const orderRoutes = Router();

// All routes require authentication
orderRoutes.use(authenticate);

// Order creation and payment
orderRoutes.post("/checkout", checkoutController);
orderRoutes.post("/:orderId/payment", processPaymentController);

// Order retrieval
orderRoutes.get("/my", getMyOrders); // Buyer view
orderRoutes.get("/sold", getSoldOrders); // Seller view
orderRoutes.get("/shipping-address", getDefaultShippingAddressController); // Get default shipping address
orderRoutes.get("/:orderId", getOrderByIdController); // Get single order by ID

// Order management with specialized timeout for order operations
orderRoutes.patch(
  "/:orderItemId/status",
  orderTimeout, // 20 seconds timeout for order operations
  updateOrderItemStatus
); // Fulfillment status update

// Admin routes
orderRoutes.get("/admin/all", authRbac("admin"), getAdminOrdersController); // Admin view all orders
