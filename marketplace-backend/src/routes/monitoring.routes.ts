import { Router, Request, Response } from "express";
import { emailMetrics } from "../utils/emailMetrics";
import { emailRetryService } from "../utils/emailRetryService";
import { performanceTracker } from "../utils/performanceTracker";
import { timeoutAnalyzer } from "../utils/timeoutAnalyzer";
import { deadLetterQueueService } from "../services/deadLetterQueue.service";
import { dlqProcessorJob } from "../jobs/dlqProcessor.job";
import { authenticate, authRbac } from "../middlewares/auth.middleware";
import logger from "../utils/logger";

export const monitoringRoutes = Router();

/**
 * Email system health and metrics endpoints
 * Requires admin authentication for security
 */

// Get email metrics
monitoringRoutes.get("/email/metrics", authenticate, authRbac(["admin"]), (req: Request, res: Response) => {
  try {
    const metrics = emailMetrics.getMetrics();
    const performanceByType = emailMetrics.getPerformanceByType();
    const healthStatus = emailMetrics.getHealthStatus();

    res.json({
      status: "success",
      data: {
        metrics,
        performanceByType,
        healthStatus,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error: any) {
    logger.error("Failed to get email metrics", { error: error.message });
    res.status(500).json({
      status: "error",
      message: "Failed to retrieve email metrics",
    });
  }
});

// Get recent email attempts
monitoringRoutes.get("/email/attempts", authenticate, authRbac(["admin"]), (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 50;
    const attempts = emailMetrics.getRecentAttempts(limit);

    res.json({
      status: "success",
      data: {
        attempts,
        count: attempts.length,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error: any) {
    logger.error("Failed to get recent email attempts", { error: error.message });
    res.status(500).json({
      status: "error",
      message: "Failed to retrieve email attempts",
    });
  }
});

// Get dead letter queue status
monitoringRoutes.get("/email/dead-letter-queue", authenticate, authRbac(["admin"]), (req: Request, res: Response) => {
  try {
    const queue = emailRetryService.getDeadLetterQueue();
    const stats = emailRetryService.getDeadLetterStats();
    const healthStatus = emailRetryService.getHealthStatus();

    res.json({
      status: "success",
      data: {
        queue: queue.slice(0, 20), // Limit to first 20 for performance
        stats,
        healthStatus,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error: any) {
    logger.error("Failed to get dead letter queue", { error: error.message });
    res.status(500).json({
      status: "error",
      message: "Failed to retrieve dead letter queue",
    });
  }
});

// Retry emails from dead letter queue
monitoringRoutes.post("/email/retry-failed", authenticate, authRbac(["admin"]), async (req: Request, res: Response) => {
  try {
    const maxRetries = parseInt(req.body.maxRetries) || 10;
    
    // Import sendEmail function for retry
    const { sendEmail } = await import("../utils/mailer");
    
    const result = await emailRetryService.retryFromDeadLetterQueue(
      async (to: string, subject: string, html: string) => {
        return sendEmail(to, subject, html, "retry");
      },
      maxRetries
    );

    logger.info("Manual retry of failed emails completed", {
      adminId: req.user?.id,
      result,
    });

    res.json({
      status: "success",
      message: "Email retry completed",
      data: result,
    });
  } catch (error: any) {
    logger.error("Failed to retry emails from dead letter queue", { 
      error: error.message,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: "Failed to retry emails",
      error: error.message,
    });
  }
});

// Clear dead letter queue
monitoringRoutes.delete("/email/dead-letter-queue", authenticate, authRbac(["admin"]), (req: Request, res: Response) => {
  try {
    const clearedCount = emailRetryService.clearDeadLetterQueue();

    logger.info("Dead letter queue cleared", {
      adminId: req.user?.id,
      clearedCount,
    });

    res.json({
      status: "success",
      message: `Cleared ${clearedCount} failed emails from queue`,
      data: { clearedCount },
    });
  } catch (error: any) {
    logger.error("Failed to clear dead letter queue", { 
      error: error.message,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: "Failed to clear dead letter queue",
    });
  }
});

// Reset email metrics
monitoringRoutes.post("/email/reset-metrics", authenticate, authRbac(["admin"]), (req: Request, res: Response) => {
  try {
    const previousMetrics = emailMetrics.getMetrics();
    emailMetrics.resetMetrics();

    logger.info("Email metrics reset", {
      adminId: req.user?.id,
      previousMetrics,
    });

    res.json({
      status: "success",
      message: "Email metrics reset successfully",
      data: { previousMetrics },
    });
  } catch (error: any) {
    logger.error("Failed to reset email metrics", { 
      error: error.message,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: "Failed to reset email metrics",
    });
  }
});

// Get performance metrics
monitoringRoutes.get("/performance/metrics", authenticate, authRbac(["admin"]), (req: Request, res: Response) => {
  try {
    const timeWindow = parseInt(req.query.timeWindow as string) || undefined;
    const stats = performanceTracker.getStats(timeWindow);
    const healthStatus = performanceTracker.getHealthStatus();
    const endpointAnalysis = performanceTracker.getEndpointAnalysis();

    res.json({
      status: "success",
      data: {
        stats,
        healthStatus,
        endpointAnalysis: Object.entries(endpointAnalysis)
          .sort(([, a], [, b]) => b.avgDuration - a.avgDuration)
          .slice(0, 20), // Top 20 slowest endpoints
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error: any) {
    logger.error("Failed to get performance metrics", { error: error.message });
    res.status(500).json({
      status: "error",
      message: "Failed to retrieve performance metrics",
    });
  }
});

// Get timeout analytics
monitoringRoutes.get("/timeouts/analytics", authenticate, authRbac(["admin"]), (req: Request, res: Response) => {
  try {
    const stats = timeoutAnalyzer.getStats();
    const timeoutsByType = timeoutAnalyzer.getTimeoutsByType();
    const trends = timeoutAnalyzer.getTimeoutTrends();
    const healthStatus = timeoutAnalyzer.getHealthStatus();

    res.json({
      status: "success",
      data: {
        stats,
        timeoutsByType,
        trends,
        healthStatus,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error: any) {
    logger.error("Failed to get timeout analytics", { error: error.message });
    res.status(500).json({
      status: "error",
      message: "Failed to retrieve timeout analytics",
    });
  }
});

// Clear old performance data
monitoringRoutes.post("/performance/cleanup", authenticate, authRbac(["admin"]), (req: Request, res: Response) => {
  try {
    const olderThanHours = parseInt(req.body.olderThanHours) || 24;
    const olderThanMs = olderThanHours * 60 * 60 * 1000;

    const clearedMetrics = performanceTracker.clearOldMetrics(olderThanMs);
    const clearedTimeouts = timeoutAnalyzer.clearOldTimeouts(olderThanMs);

    logger.info("Performance data cleanup completed", {
      adminId: req.user?.id,
      clearedMetrics,
      clearedTimeouts,
      olderThanHours,
    });

    res.json({
      status: "success",
      message: "Performance data cleanup completed",
      data: { clearedMetrics, clearedTimeouts },
    });
  } catch (error: any) {
    logger.error("Failed to cleanup performance data", {
      error: error.message,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: "Failed to cleanup performance data",
    });
  }
});

// Overall system health check
monitoringRoutes.get("/health", async (req: Request, res: Response) => {
  try {
    const emailHealth = emailMetrics.getHealthStatus();
    const retryServiceHealth = emailRetryService.getHealthStatus();
    const performanceHealth = performanceTracker.getHealthStatus();
    const timeoutHealth = timeoutAnalyzer.getHealthStatus();
    const dlqHealth = await deadLetterQueueService.getHealthStatus();

    const allStatuses = [emailHealth.status, retryServiceHealth.status, performanceHealth.status, timeoutHealth.status, dlqHealth.status];
    const overallStatus =
      allStatuses.includes('critical') ? 'critical' :
      allStatuses.includes('warning') ? 'warning' :
      'healthy';

    res.json({
      status: "success",
      data: {
        overall: {
          status: overallStatus,
          timestamp: new Date().toISOString(),
        },
        email: emailHealth,
        retryService: retryServiceHealth,
        performance: performanceHealth,
        timeouts: timeoutHealth,
        deadLetterQueue: dlqHealth,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
      },
    });
  } catch (error: any) {
    logger.error("Failed to get system health", { error: error.message });
    res.status(500).json({
      status: "error",
      message: "Failed to retrieve system health",
    });
  }
});

// Dead Letter Queue endpoints

// Get DLQ statistics
monitoringRoutes.get("/dlq/stats", authenticate, authRbac(["admin"]), async (req: Request, res: Response) => {
  try {
    const stats = await deadLetterQueueService.getStats();
    const healthStatus = await deadLetterQueueService.getHealthStatus();

    res.json({
      status: "success",
      data: {
        stats,
        healthStatus,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error: any) {
    logger.error("Failed to get DLQ stats", {
      error: error.message,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: "Failed to get DLQ statistics",
    });
  }
});

// Get DLQ entries with pagination and filtering
monitoringRoutes.get("/dlq/entries", authenticate, authRbac(["admin"]), async (req: Request, res: Response) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      emailType,
      failureReason,
      priority,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = req.query;

    const result = await deadLetterQueueService.getEntries({
      page: parseInt(page as string),
      limit: parseInt(limit as string),
      status: status as any,
      emailType: emailType as any,
      failureReason: failureReason as any,
      priority: priority ? parseInt(priority as string) : undefined,
      sortBy: sortBy as any,
      sortOrder: sortOrder as any,
    });

    res.json({
      status: "success",
      data: result,
    });
  } catch (error: any) {
    logger.error("Failed to get DLQ entries", {
      error: error.message,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: "Failed to get DLQ entries",
    });
  }
});

// Get specific DLQ entry
monitoringRoutes.get("/dlq/entries/:id", authenticate, authRbac(["admin"]), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const entry = await deadLetterQueueService.getEntry(id);

    if (!entry) {
      return res.status(404).json({
        status: "error",
        message: "DLQ entry not found",
      });
    }

    res.json({
      status: "success",
      data: entry,
    });
  } catch (error: any) {
    logger.error("Failed to get DLQ entry", {
      error: error.message,
      entryId: req.params.id,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: "Failed to get DLQ entry",
    });
  }
});

// Manually retry DLQ entry
monitoringRoutes.post("/dlq/entries/:id/retry", authenticate, authRbac(["admin"]), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const adminUserId = req.user?.id;

    const success = await deadLetterQueueService.retryEntry(id, adminUserId);

    res.json({
      status: "success",
      data: {
        success,
        message: success ? "Email retry successful" : "Email retry failed",
      },
    });
  } catch (error: any) {
    logger.error("Failed to retry DLQ entry", {
      error: error.message,
      entryId: req.params.id,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: "Failed to retry DLQ entry",
    });
  }
});

// Mark DLQ entry as resolved
monitoringRoutes.post("/dlq/entries/:id/resolve", authenticate, authRbac(["admin"]), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { notes } = req.body;
    const adminUserId = req.user?.id;

    if (!adminUserId) {
      return res.status(401).json({
        status: "error",
        message: "Admin user ID required",
      });
    }

    await deadLetterQueueService.resolveEntry(id, adminUserId, notes);

    res.json({
      status: "success",
      message: "DLQ entry marked as resolved",
    });
  } catch (error: any) {
    logger.error("Failed to resolve DLQ entry", {
      error: error.message,
      entryId: req.params.id,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: "Failed to resolve DLQ entry",
    });
  }
});

// Cancel DLQ entry
monitoringRoutes.post("/dlq/entries/:id/cancel", authenticate, authRbac(["admin"]), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const adminUserId = req.user?.id;

    if (!adminUserId) {
      return res.status(401).json({
        status: "error",
        message: "Admin user ID required",
      });
    }

    await deadLetterQueueService.cancelEntry(id, adminUserId, reason);

    res.json({
      status: "success",
      message: "DLQ entry cancelled",
    });
  } catch (error: any) {
    logger.error("Failed to cancel DLQ entry", {
      error: error.message,
      entryId: req.params.id,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: "Failed to cancel DLQ entry",
    });
  }
});

// Process DLQ entries manually
monitoringRoutes.post("/dlq/process", authenticate, authRbac(["admin"]), async (req: Request, res: Response) => {
  try {
    const result = await dlqProcessorJob.processNow();

    res.json({
      status: "success",
      data: result,
      message: "DLQ processing completed",
    });
  } catch (error: any) {
    logger.error("Failed to process DLQ manually", {
      error: error.message,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: error.message || "Failed to process DLQ",
    });
  }
});

// Get DLQ processor status
monitoringRoutes.get("/dlq/processor/status", authenticate, authRbac(["admin"]), (req: Request, res: Response) => {
  try {
    const status = dlqProcessorJob.getStatus();

    res.json({
      status: "success",
      data: status,
    });
  } catch (error: any) {
    logger.error("Failed to get DLQ processor status", {
      error: error.message,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: "Failed to get DLQ processor status",
    });
  }
});

// Cleanup old DLQ entries
monitoringRoutes.post("/dlq/cleanup", authenticate, authRbac(["admin"]), async (req: Request, res: Response) => {
  try {
    const { olderThanDays = 30 } = req.body;
    const deletedCount = await deadLetterQueueService.cleanup(olderThanDays);

    res.json({
      status: "success",
      data: {
        deletedCount,
        olderThanDays,
      },
      message: `Cleaned up ${deletedCount} old DLQ entries`,
    });
  } catch (error: any) {
    logger.error("Failed to cleanup DLQ entries", {
      error: error.message,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: "Failed to cleanup DLQ entries",
    });
  }
});

// Reset stale DLQ entries
monitoringRoutes.post("/dlq/reset-stale", authenticate, authRbac(["admin"]), async (req: Request, res: Response) => {
  try {
    const resetCount = await deadLetterQueueService.resetStaleEntries();

    res.json({
      status: "success",
      data: {
        resetCount,
      },
      message: `Reset ${resetCount} stale DLQ entries`,
    });
  } catch (error: any) {
    logger.error("Failed to reset stale DLQ entries", {
      error: error.message,
      adminId: req.user?.id,
    });
    res.status(500).json({
      status: "error",
      message: "Failed to reset stale DLQ entries",
    });
  }
});
