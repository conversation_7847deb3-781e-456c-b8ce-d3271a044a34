import { Router, Request, Response } from "express";
import { emailMetrics } from "../utils/emailMetrics";
import { emailRetryService } from "../utils/emailRetryService";
import { performanceTracker } from "../utils/performanceTracker";
import { timeoutAnalyzer } from "../utils/timeoutAnalyzer";
import { deadLetterQueueService } from "../services/deadLetterQueue.service";
import { dlqProcessorJob } from "../jobs/dlqProcessor.job";
import { authenticate, authRbac } from "../middlewares/auth.middleware";
import logger from "../utils/logger";

export const monitoringRoutes = Router();

/**
 * @swagger
 * tags:
 *   - name: Monitoring
 *     description: System monitoring and health check endpoints
 *   - name: Email Monitoring
 *     description: Email service monitoring and metrics
 *   - name: Performance Monitoring
 *     description: Performance tracking and analytics
 *   - name: Dead Letter Queue
 *     description: Dead Letter Queue management and monitoring
 */

/**
 * Email system health and metrics endpoints
 * Requires admin authentication for security
 */

/**
 * @swagger
 * /api/monitoring/email/metrics:
 *   get:
 *     summary: Get email service metrics
 *     description: Retrieve comprehensive email service metrics including send rates, performance, and health status
 *     tags: [Email Monitoring]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Email metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     metrics:
 *                       type: object
 *                       properties:
 *                         totalSent:
 *                           type: number
 *                           example: 1250
 *                         totalFailed:
 *                           type: number
 *                           example: 15
 *                         successRate:
 *                           type: number
 *                           example: 98.8
 *                         avgResponseTime:
 *                           type: number
 *                           example: 245.5
 *                         lastHourSent:
 *                           type: number
 *                           example: 45
 *                         lastHourFailed:
 *                           type: number
 *                           example: 2
 *                     performanceByType:
 *                       type: object
 *                       additionalProperties:
 *                         type: object
 *                         properties:
 *                           count:
 *                             type: number
 *                           avgDuration:
 *                             type: number
 *                           successRate:
 *                             type: number
 *                           lastSent:
 *                             type: string
 *                             format: date-time
 *                     healthStatus:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           enum: [healthy, warning, critical]
 *                         details:
 *                           type: string
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       403:
 *         description: Forbidden - Admin access required
 *       500:
 *         description: Internal server error
 */
// Get email metrics
monitoringRoutes.get(
  "/email/metrics",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  (req: Request, res: Response) => {
    try {
      const metrics = emailMetrics.getMetrics();
      const performanceByType = emailMetrics.getPerformanceByType();
      const healthStatus = emailMetrics.getHealthStatus();

      res.json({
        status: "success",
        data: {
          metrics,
          performanceByType,
          healthStatus,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error: any) {
      logger.error("Failed to get email metrics", { error: error.message });
      res.status(500).json({
        status: "error",
        message: "Failed to retrieve email metrics",
      });
    }
  }
);

// Get recent email attempts
monitoringRoutes.get(
  "/email/attempts",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  (req: Request, res: Response) => {
    try {
      const limit = parseInt(req.query.limit as string) || 50;
      const attempts = emailMetrics.getRecentAttempts(limit);

      res.json({
        status: "success",
        data: {
          attempts,
          count: attempts.length,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error: any) {
      logger.error("Failed to get recent email attempts", {
        error: error.message,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to retrieve email attempts",
      });
    }
  }
);

// Get dead letter queue status
monitoringRoutes.get(
  "/email/dead-letter-queue",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  (req: Request, res: Response) => {
    try {
      const queue = emailRetryService.getDeadLetterQueue();
      const stats = emailRetryService.getDeadLetterStats();
      const healthStatus = emailRetryService.getHealthStatus();

      res.json({
        status: "success",
        data: {
          queue: queue.slice(0, 20), // Limit to first 20 for performance
          stats,
          healthStatus,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error: any) {
      logger.error("Failed to get dead letter queue", { error: error.message });
      res.status(500).json({
        status: "error",
        message: "Failed to retrieve dead letter queue",
      });
    }
  }
);

// Retry emails from dead letter queue
monitoringRoutes.post(
  "/email/retry-failed",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  async (req: Request, res: Response) => {
    try {
      const maxRetries = parseInt(req.body.maxRetries) || 10;

      // Import sendEmail function for retry
      const { sendEmail } = await import("../utils/mailer");

      const result = await emailRetryService.retryFromDeadLetterQueue(
        async (to: string, subject: string, html: string) => {
          return sendEmail(to, subject, html, "retry");
        },
        maxRetries
      );

      logger.info("Manual retry of failed emails completed", {
        adminId: req.user?.id,
        result,
      });

      res.json({
        status: "success",
        message: "Email retry completed",
        data: result,
      });
    } catch (error: any) {
      logger.error("Failed to retry emails from dead letter queue", {
        error: error.message,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to retry emails",
        error: error.message,
      });
    }
  }
);

// Clear dead letter queue
monitoringRoutes.delete(
  "/email/dead-letter-queue",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  (req: Request, res: Response) => {
    try {
      const clearedCount = emailRetryService.clearDeadLetterQueue();

      logger.info("Dead letter queue cleared", {
        adminId: req.user?.id,
        clearedCount,
      });

      res.json({
        status: "success",
        message: `Cleared ${clearedCount} failed emails from queue`,
        data: { clearedCount },
      });
    } catch (error: any) {
      logger.error("Failed to clear dead letter queue", {
        error: error.message,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to clear dead letter queue",
      });
    }
  }
);

// Reset email metrics
monitoringRoutes.post(
  "/email/reset-metrics",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  (req: Request, res: Response) => {
    try {
      const previousMetrics = emailMetrics.getMetrics();
      emailMetrics.resetMetrics();

      logger.info("Email metrics reset", {
        adminId: req.user?.id,
        previousMetrics,
      });

      res.json({
        status: "success",
        message: "Email metrics reset successfully",
        data: { previousMetrics },
      });
    } catch (error: any) {
      logger.error("Failed to reset email metrics", {
        error: error.message,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to reset email metrics",
      });
    }
  }
);

/**
 * @swagger
 * /api/monitoring/performance/metrics:
 *   get:
 *     summary: Get performance metrics
 *     description: Retrieve system performance metrics including response times, request counts, and endpoint analysis
 *     tags: [Performance Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeWindow
 *         schema:
 *           type: integer
 *           example: 3600
 *         description: Time window in seconds for metrics calculation (default is all time)
 *     responses:
 *       200:
 *         description: Performance metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     stats:
 *                       type: object
 *                       properties:
 *                         totalRequests:
 *                           type: number
 *                           example: 15420
 *                         avgResponseTime:
 *                           type: number
 *                           example: 125.5
 *                         minResponseTime:
 *                           type: number
 *                           example: 15
 *                         maxResponseTime:
 *                           type: number
 *                           example: 2500
 *                         slowRequests:
 *                           type: number
 *                           example: 45
 *                         verySlowRequests:
 *                           type: number
 *                           example: 8
 *                         requestsPerMinute:
 *                           type: number
 *                           example: 25.7
 *                     healthStatus:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           enum: [healthy, warning, critical]
 *                         details:
 *                           type: string
 *                     endpointAnalysis:
 *                       type: array
 *                       items:
 *                         type: array
 *                         items:
 *                           oneOf:
 *                             - type: string
 *                             - type: object
 *                       description: Top 20 slowest endpoints with performance data
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       403:
 *         description: Forbidden - Admin access required
 *       500:
 *         description: Internal server error
 */
// Get performance metrics
monitoringRoutes.get(
  "/performance/metrics",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  (req: Request, res: Response) => {
    try {
      const timeWindow = parseInt(req.query.timeWindow as string) || undefined;
      const stats = performanceTracker.getStats(timeWindow);
      const healthStatus = performanceTracker.getHealthStatus();
      const endpointAnalysis = performanceTracker.getEndpointAnalysis();

      res.json({
        status: "success",
        data: {
          stats,
          healthStatus,
          endpointAnalysis: Object.entries(endpointAnalysis)
            .sort(([, a], [, b]) => b.avgDuration - a.avgDuration)
            .slice(0, 20), // Top 20 slowest endpoints
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error: any) {
      logger.error("Failed to get performance metrics", {
        error: error.message,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to retrieve performance metrics",
      });
    }
  }
);

/**
 * @swagger
 * /api/monitoring/timeouts/analytics:
 *   get:
 *     summary: Get timeout analytics
 *     description: Retrieve timeout analytics including timeout rates, patterns, and trends
 *     tags: [Performance Monitoring]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Timeout analytics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     stats:
 *                       type: object
 *                       properties:
 *                         totalTimeouts:
 *                           type: number
 *                           example: 125
 *                         timeoutRate:
 *                           type: number
 *                           example: 0.8
 *                         avgTimeoutDuration:
 *                           type: number
 *                           example: 30000
 *                         timeoutsLastHour:
 *                           type: number
 *                           example: 5
 *                     timeoutsByType:
 *                       type: object
 *                       additionalProperties:
 *                         type: object
 *                         properties:
 *                           count:
 *                             type: number
 *                           avgDuration:
 *                             type: number
 *                           lastOccurred:
 *                             type: string
 *                             format: date-time
 *                     trends:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *                           count:
 *                             type: number
 *                           type:
 *                             type: string
 *                     healthStatus:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           enum: [healthy, warning, critical]
 *                         details:
 *                           type: string
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       403:
 *         description: Forbidden - Admin access required
 *       500:
 *         description: Internal server error
 */
// Get timeout analytics
monitoringRoutes.get(
  "/timeouts/analytics",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  (req: Request, res: Response) => {
    try {
      const stats = timeoutAnalyzer.getStats();
      const timeoutsByType = timeoutAnalyzer.getTimeoutsByType();
      const trends = timeoutAnalyzer.getTimeoutTrends();
      const healthStatus = timeoutAnalyzer.getHealthStatus();

      res.json({
        status: "success",
        data: {
          stats,
          timeoutsByType,
          trends,
          healthStatus,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error: any) {
      logger.error("Failed to get timeout analytics", { error: error.message });
      res.status(500).json({
        status: "error",
        message: "Failed to retrieve timeout analytics",
      });
    }
  }
);

// Clear old performance data
monitoringRoutes.post(
  "/performance/cleanup",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  (req: Request, res: Response) => {
    try {
      const olderThanHours = parseInt(req.body.olderThanHours) || 24;
      const olderThanMs = olderThanHours * 60 * 60 * 1000;

      const clearedMetrics = performanceTracker.clearOldMetrics(olderThanMs);
      const clearedTimeouts = timeoutAnalyzer.clearOldTimeouts(olderThanMs);

      logger.info("Performance data cleanup completed", {
        adminId: req.user?.id,
        clearedMetrics,
        clearedTimeouts,
        olderThanHours,
      });

      res.json({
        status: "success",
        message: "Performance data cleanup completed",
        data: { clearedMetrics, clearedTimeouts },
      });
    } catch (error: any) {
      logger.error("Failed to cleanup performance data", {
        error: error.message,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to cleanup performance data",
      });
    }
  }
);

/**
 * @swagger
 * /api/monitoring/health:
 *   get:
 *     summary: Get overall system health
 *     description: Retrieve comprehensive system health status including all monitoring subsystems
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: System health retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     overall:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           enum: [healthy, warning, critical]
 *                         timestamp:
 *                           type: string
 *                           format: date-time
 *                     email:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           enum: [healthy, warning, critical]
 *                         details:
 *                           type: string
 *                         metrics:
 *                           type: object
 *                           properties:
 *                             totalSent:
 *                               type: number
 *                             totalFailed:
 *                               type: number
 *                             successRate:
 *                               type: number
 *                     retryService:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           enum: [healthy, warning, critical]
 *                         details:
 *                           type: string
 *                     performance:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           enum: [healthy, warning, critical]
 *                         details:
 *                           type: string
 *                     timeouts:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           enum: [healthy, warning, critical]
 *                         details:
 *                           type: string
 *                     deadLetterQueue:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           enum: [healthy, warning, critical]
 *                         details:
 *                           type: string
 *                     uptime:
 *                       type: number
 *                       description: Server uptime in seconds
 *                     memory:
 *                       type: object
 *                       properties:
 *                         rss:
 *                           type: number
 *                         heapTotal:
 *                           type: number
 *                         heapUsed:
 *                           type: number
 *                         external:
 *                           type: number
 *                         arrayBuffers:
 *                           type: number
 *       500:
 *         description: Internal server error
 */
// Overall system health check
monitoringRoutes.get("/health", async (req: Request, res: Response) => {
  try {
    const emailHealth = emailMetrics.getHealthStatus();
    const retryServiceHealth = emailRetryService.getHealthStatus();
    const performanceHealth = performanceTracker.getHealthStatus();
    const timeoutHealth = timeoutAnalyzer.getHealthStatus();
    const dlqHealth = await deadLetterQueueService.getHealthStatus();

    const allStatuses = [
      emailHealth.status,
      retryServiceHealth.status,
      performanceHealth.status,
      timeoutHealth.status,
      dlqHealth.status,
    ];
    const overallStatus = allStatuses.includes("critical")
      ? "critical"
      : allStatuses.includes("warning")
        ? "warning"
        : "healthy";

    res.json({
      status: "success",
      data: {
        overall: {
          status: overallStatus,
          timestamp: new Date().toISOString(),
        },
        email: emailHealth,
        retryService: retryServiceHealth,
        performance: performanceHealth,
        timeouts: timeoutHealth,
        deadLetterQueue: dlqHealth,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
      },
    });
  } catch (error: any) {
    logger.error("Failed to get system health", { error: error.message });
    res.status(500).json({
      status: "error",
      message: "Failed to retrieve system health",
    });
  }
});

// Dead Letter Queue endpoints

/**
 * @swagger
 * /api/monitoring/dlq/stats:
 *   get:
 *     summary: Get Dead Letter Queue statistics
 *     description: Retrieve comprehensive DLQ statistics including entry counts, health status, and analytics
 *     tags: [Dead Letter Queue]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: DLQ statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     stats:
 *                       type: object
 *                       properties:
 *                         totalEntries:
 *                           type: number
 *                           example: 245
 *                         pendingEntries:
 *                           type: number
 *                           example: 12
 *                         processingEntries:
 *                           type: number
 *                           example: 3
 *                         resolvedEntries:
 *                           type: number
 *                           example: 220
 *                         failedPermanently:
 *                           type: number
 *                           example: 8
 *                         cancelledEntries:
 *                           type: number
 *                           example: 2
 *                         avgRetryAttempts:
 *                           type: number
 *                           example: 2.5
 *                         oldestPendingAge:
 *                           type: number
 *                           example: 3600000
 *                         entriesLast24h:
 *                           type: number
 *                           example: 15
 *                         successfulRetriesLast24h:
 *                           type: number
 *                           example: 12
 *                         byEmailType:
 *                           type: object
 *                           additionalProperties:
 *                             type: number
 *                         byFailureReason:
 *                           type: object
 *                           additionalProperties:
 *                             type: number
 *                         byPriority:
 *                           type: object
 *                           additionalProperties:
 *                             type: number
 *                     healthStatus:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           enum: [healthy, warning, critical]
 *                         details:
 *                           type: string
 *                         thresholds:
 *                           type: object
 *                           properties:
 *                             warningThreshold:
 *                               type: number
 *                             criticalThreshold:
 *                               type: number
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       403:
 *         description: Forbidden - Admin access required
 *       500:
 *         description: Internal server error
 */
// Get DLQ statistics
monitoringRoutes.get(
  "/dlq/stats",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  async (req: Request, res: Response) => {
    try {
      const stats = await deadLetterQueueService.getStats();
      const healthStatus = await deadLetterQueueService.getHealthStatus();

      res.json({
        status: "success",
        data: {
          stats,
          healthStatus,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error: any) {
      logger.error("Failed to get DLQ stats", {
        error: error.message,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to get DLQ statistics",
      });
    }
  }
);

/**
 * @swagger
 * /api/monitoring/dlq/entries:
 *   get:
 *     summary: Get Dead Letter Queue entries
 *     description: Retrieve DLQ entries with pagination and filtering options
 *     tags: [Dead Letter Queue]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of entries per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, processing, resolved, failed_permanently, cancelled]
 *         description: Filter by entry status
 *       - in: query
 *         name: emailType
 *         schema:
 *           type: string
 *         description: Filter by email type
 *       - in: query
 *         name: failureReason
 *         schema:
 *           type: string
 *         description: Filter by failure reason
 *       - in: query
 *         name: priority
 *         schema:
 *           type: integer
 *         description: Filter by priority level
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [createdAt, retryAttempts, priority, lastRetryAt]
 *           default: createdAt
 *         description: Sort field
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: DESC
 *         description: Sort order
 *     responses:
 *       200:
 *         description: DLQ entries retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     entries:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           emailType:
 *                             type: string
 *                           recipientEmail:
 *                             type: string
 *                           subject:
 *                             type: string
 *                           failureReason:
 *                             type: string
 *                           priority:
 *                             type: number
 *                           retryAttempts:
 *                             type: number
 *                           maxRetryAttempts:
 *                             type: number
 *                           status:
 *                             type: string
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                           lastRetryAt:
 *                             type: string
 *                             format: date-time
 *                           nextRetryAt:
 *                             type: string
 *                             format: date-time
 *                           processingNode:
 *                             type: string
 *                           userId:
 *                             type: string
 *                           errorDetails:
 *                             type: string
 *                     total:
 *                       type: number
 *                       description: Total number of entries matching filters
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       403:
 *         description: Forbidden - Admin access required
 *       500:
 *         description: Internal server error
 */
// Get DLQ entries with pagination and filtering
monitoringRoutes.get(
  "/dlq/entries",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  async (req: Request, res: Response) => {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        emailType,
        failureReason,
        priority,
        sortBy = "createdAt",
        sortOrder = "DESC",
      } = req.query;

      const result = await deadLetterQueueService.getEntries({
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        status: status as any,
        emailType: emailType as any,
        failureReason: failureReason as any,
        priority: priority ? parseInt(priority as string) : undefined,
        sortBy: sortBy as any,
        sortOrder: sortOrder as any,
      });

      res.json({
        status: "success",
        data: result,
      });
    } catch (error: any) {
      logger.error("Failed to get DLQ entries", {
        error: error.message,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to get DLQ entries",
      });
    }
  }
);

// Get specific DLQ entry
monitoringRoutes.get(
  "/dlq/entries/:id",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const entry = await deadLetterQueueService.getEntry(id);

      if (!entry) {
        return res.status(404).json({
          status: "error",
          message: "DLQ entry not found",
        });
      }

      res.json({
        status: "success",
        data: entry,
      });
    } catch (error: any) {
      logger.error("Failed to get DLQ entry", {
        error: error.message,
        entryId: req.params.id,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to get DLQ entry",
      });
    }
  }
);

// Manually retry DLQ entry
monitoringRoutes.post(
  "/dlq/entries/:id/retry",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const adminUserId = req.user?.id;

      const success = await deadLetterQueueService.retryEntry(id, adminUserId);

      res.json({
        status: "success",
        data: {
          success,
          message: success ? "Email retry successful" : "Email retry failed",
        },
      });
    } catch (error: any) {
      logger.error("Failed to retry DLQ entry", {
        error: error.message,
        entryId: req.params.id,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to retry DLQ entry",
      });
    }
  }
);

// Mark DLQ entry as resolved
monitoringRoutes.post(
  "/dlq/entries/:id/resolve",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { notes } = req.body;
      const adminUserId = req.user?.id;

      if (!adminUserId) {
        return res.status(401).json({
          status: "error",
          message: "Admin user ID required",
        });
      }

      await deadLetterQueueService.resolveEntry(id, adminUserId, notes);

      res.json({
        status: "success",
        message: "DLQ entry marked as resolved",
      });
    } catch (error: any) {
      logger.error("Failed to resolve DLQ entry", {
        error: error.message,
        entryId: req.params.id,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to resolve DLQ entry",
      });
    }
  }
);

// Cancel DLQ entry
monitoringRoutes.post(
  "/dlq/entries/:id/cancel",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      const adminUserId = req.user?.id;

      if (!adminUserId) {
        return res.status(401).json({
          status: "error",
          message: "Admin user ID required",
        });
      }

      await deadLetterQueueService.cancelEntry(id, adminUserId, reason);

      res.json({
        status: "success",
        message: "DLQ entry cancelled",
      });
    } catch (error: any) {
      logger.error("Failed to cancel DLQ entry", {
        error: error.message,
        entryId: req.params.id,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to cancel DLQ entry",
      });
    }
  }
);

// Process DLQ entries manually
monitoringRoutes.post(
  "/dlq/process",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  async (req: Request, res: Response) => {
    try {
      const result = await dlqProcessorJob.processNow();

      res.json({
        status: "success",
        data: result,
        message: "DLQ processing completed",
      });
    } catch (error: any) {
      logger.error("Failed to process DLQ manually", {
        error: error.message,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: error.message || "Failed to process DLQ",
      });
    }
  }
);

// Get DLQ processor status
monitoringRoutes.get(
  "/dlq/processor/status",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  (req: Request, res: Response) => {
    try {
      const status = dlqProcessorJob.getStatus();

      res.json({
        status: "success",
        data: status,
      });
    } catch (error: any) {
      logger.error("Failed to get DLQ processor status", {
        error: error.message,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to get DLQ processor status",
      });
    }
  }
);

// Cleanup old DLQ entries
monitoringRoutes.post(
  "/dlq/cleanup",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  async (req: Request, res: Response) => {
    try {
      const { olderThanDays = 30 } = req.body;
      const deletedCount = await deadLetterQueueService.cleanup(olderThanDays);

      res.json({
        status: "success",
        data: {
          deletedCount,
          olderThanDays,
        },
        message: `Cleaned up ${deletedCount} old DLQ entries`,
      });
    } catch (error: any) {
      logger.error("Failed to cleanup DLQ entries", {
        error: error.message,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to cleanup DLQ entries",
      });
    }
  }
);

// Reset stale DLQ entries
monitoringRoutes.post(
  "/dlq/reset-stale",
  authenticate,
  authRbac(["admin", "technical_admin"]),
  async (req: Request, res: Response) => {
    try {
      const resetCount = await deadLetterQueueService.resetStaleEntries();

      res.json({
        status: "success",
        data: {
          resetCount,
        },
        message: `Reset ${resetCount} stale DLQ entries`,
      });
    } catch (error: any) {
      logger.error("Failed to reset stale DLQ entries", {
        error: error.message,
        adminId: req.user?.id,
      });
      res.status(500).json({
        status: "error",
        message: "Failed to reset stale DLQ entries",
      });
    }
  }
);
