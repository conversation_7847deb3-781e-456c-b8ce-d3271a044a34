import { Router } from "express";
import {
  getAdminDashboardStats,
  getTopProductsController,
  getRevenueChartData,
  getUserGrowthChartData,
  getSellerDashboardStats,
  getComprehensiveAnalytics,
} from "../controllers/analytics.controller";
import {
  getRecentActivitiesController,
  getActivitySummaryController,
} from "../controllers/activity.controller";
import { authenticate, authRbac } from "../middlewares/auth.middleware";

export const analyticsRoutes = Router();

// Admin analytics routes
analyticsRoutes.get(
  "/admin/dashboard",
  authenticate,
  authRbac("admin"),
  getAdminDashboardStats
);

analyticsRoutes.get(
  "/admin/top-products",
  authenticate,
  authRbac("admin"),
  getTopProductsController
);

analyticsRoutes.get(
  "/admin/revenue-chart",
  authenticate,
  authRbac("admin"),
  getRevenueChartData
);

analyticsRoutes.get(
  "/admin/user-growth-chart",
  authenticate,
  authRbac("admin"),
  getUserGrowthChartData
);

analyticsRoutes.get(
  "/admin/comprehensive",
  authenticate,
  authRbac("admin"),
  getComprehensiveAnalytics
);

// Seller analytics routes
analyticsRoutes.get(
  "/seller/dashboard",
  authenticate,
  authRbac("seller"),
  getSellerDashboardStats
);

// Activity routes
analyticsRoutes.get(
  "/admin/activities",
  authenticate,
  authRbac("admin"),
  getRecentActivitiesController
);

analyticsRoutes.get(
  "/admin/activity-summary",
  authenticate,
  authRbac("admin"),
  getActivitySummaryController
);

// General analytics routes (for authenticated users)
analyticsRoutes.get("/top-products", authenticate, getTopProductsController);
