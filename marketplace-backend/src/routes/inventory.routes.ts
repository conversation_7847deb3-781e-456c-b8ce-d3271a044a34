import { Router } from "express";
import {
  createInventory,
  updateStock,
  getSellerInventory,
  getInventoryByProduct,
  getLowStockItems,
  getInventoryStats,
  bulkUpdateStock,
  adjustStock,
  updateInventory,
} from "../controllers/inventory.controller";
import { authenticate } from "../middlewares/auth.middleware";
import { requireRole } from "../middlewares/role.middleware";

const router = Router();

// All routes require authentication
router.use(authenticate);

/**
 * @swagger
 * /api/inventory:
 *   post:
 *     summary: Create inventory for a product
 *     tags: [Inventory]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - productId
 *               - currentStock
 *             properties:
 *               productId:
 *                 type: string
 *               currentStock:
 *                 type: integer
 *               lowStockThreshold:
 *                 type: integer
 *               reorderPoint:
 *                 type: integer
 *               reorderQuantity:
 *                 type: integer
 *               sku:
 *                 type: string
 *               location:
 *                 type: string
 *               costPrice:
 *                 type: number
 *     responses:
 *       201:
 *         description: Inventory created successfully
 *       400:
 *         description: Bad request
 */
router.post("/", requireRole(["seller", "admin"]), createInventory);

/**
 * @swagger
 * /api/inventory/seller:
 *   get:
 *     summary: Get seller's inventory
 *     tags: [Inventory]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [in_stock, low_stock, out_of_stock, discontinued]
 *       - in: query
 *         name: lowStock
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: outOfStock
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Inventory retrieved successfully
 */
router.get("/seller", requireRole(["seller", "admin"]), getSellerInventory);

/**
 * @swagger
 * /api/inventory/stats:
 *   get:
 *     summary: Get inventory statistics
 *     tags: [Inventory]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 */
router.get("/stats", requireRole(["seller", "admin"]), getInventoryStats);

/**
 * @swagger
 * /api/inventory/low-stock:
 *   get:
 *     summary: Get low stock items
 *     tags: [Inventory]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Low stock items retrieved successfully
 */
router.get("/low-stock", requireRole(["seller", "admin"]), getLowStockItems);

/**
 * @swagger
 * /api/inventory/product/{productId}:
 *   get:
 *     summary: Get inventory by product ID
 *     tags: [Inventory]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: productId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Inventory retrieved successfully
 *       404:
 *         description: Inventory not found
 */
router.get("/product/:productId", getInventoryByProduct);

/**
 * @swagger
 * /api/inventory/{inventoryId}/stock:
 *   put:
 *     summary: Update stock levels
 *     tags: [Inventory]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: inventoryId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - quantity
 *               - type
 *             properties:
 *               quantity:
 *                 type: integer
 *               type:
 *                 type: string
 *                 enum: [purchase, sale, adjustment, return, damage, restock]
 *               reason:
 *                 type: string
 *               reference:
 *                 type: string
 *     responses:
 *       200:
 *         description: Stock updated successfully
 *       400:
 *         description: Bad request
 */
router.put(
  "/:inventoryId/stock",
  requireRole(["seller", "admin"]),
  updateStock
);

/**
 * @swagger
 * /api/inventory/{inventoryId}/adjust:
 *   put:
 *     summary: Adjust stock to specific quantity
 *     tags: [Inventory]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: inventoryId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - newQuantity
 *             properties:
 *               newQuantity:
 *                 type: integer
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Stock adjusted successfully
 *       400:
 *         description: Bad request
 */
router.put(
  "/:inventoryId/adjust",
  requireRole(["seller", "admin"]),
  adjustStock
);

/**
 * @swagger
 * /api/inventory/{inventoryId}:
 *   put:
 *     summary: Update inventory details
 *     tags: [Inventory]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: inventoryId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               newQuantity:
 *                 type: integer
 *               lowStockThreshold:
 *                 type: integer
 *               reorderPoint:
 *                 type: integer
 *               reorderQuantity:
 *                 type: integer
 *               sku:
 *                 type: string
 *               location:
 *                 type: string
 *               costPrice:
 *                 type: number
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Inventory updated successfully
 *       400:
 *         description: Bad request
 */
router.put("/:inventoryId", requireRole(["seller", "admin"]), updateInventory);

/**
 * @swagger
 * /api/inventory/bulk-update:
 *   put:
 *     summary: Bulk update multiple inventory items
 *     tags: [Inventory]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - updates
 *             properties:
 *               updates:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     inventoryId:
 *                       type: string
 *                     quantity:
 *                       type: integer
 *                     type:
 *                       type: string
 *                     reason:
 *                       type: string
 *                     reference:
 *                       type: string
 *     responses:
 *       200:
 *         description: Bulk update completed
 */
router.put("/bulk-update", requireRole(["seller", "admin"]), bulkUpdateStock);

export { router as inventoryRoutes };
export default router;
