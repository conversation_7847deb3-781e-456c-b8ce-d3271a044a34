import { Router } from "express";
import { authenticate, authRbac } from "../../middlewares/auth.middleware";
import {
  getAllInventoryController,
  getInventoryStatsController,
  getLowStockAlertsController,
  bulkUpdateInventoryController,
  getInventoryByIdController,
  updateInventoryStatusController,
  getInventoryHistoryController,
  exportInventoryReportController,
  getInventoryAnalyticsController,
  autoRestockController,
} from "../../controllers/admin/inventory.admin.controller";

export const adminInventoryRouter = Router();

// All routes require admin authentication
adminInventoryRouter.use(authenticate, authRbac("admin"));

/**
 * @swagger
 * /api/admin/inventory:
 *   get:
 *     summary: Get all inventory items (admin view)
 *     tags: [Admin Inventory]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [in_stock, low_stock, out_of_stock, discontinued]
 *         description: Filter by status
 *       - in: query
 *         name: sellerId
 *         schema:
 *           type: string
 *         description: Filter by seller
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *     responses:
 *       200:
 *         description: Inventory items retrieved successfully
 */
adminInventoryRouter.get("/", getAllInventoryController);

/**
 * @swagger
 * /api/admin/inventory/stats:
 *   get:
 *     summary: Get comprehensive inventory statistics
 *     tags: [Admin Inventory]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 */
adminInventoryRouter.get("/stats", getInventoryStatsController);

/**
 * @swagger
 * /api/admin/inventory/low-stock-alerts:
 *   get:
 *     summary: Get low stock alerts
 *     tags: [Admin Inventory]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Low stock alerts retrieved successfully
 */
adminInventoryRouter.get("/low-stock-alerts", getLowStockAlertsController);

/**
 * @swagger
 * /api/admin/inventory/analytics:
 *   get:
 *     summary: Get inventory analytics data
 *     tags: [Admin Inventory]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Analytics data retrieved successfully
 */
adminInventoryRouter.get("/analytics", getInventoryAnalyticsController);

/**
 * @swagger
 * /api/admin/inventory/{id}:
 *   get:
 *     summary: Get inventory item by ID
 *     tags: [Admin Inventory]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Inventory item retrieved successfully
 */
adminInventoryRouter.get("/:id", getInventoryByIdController);

/**
 * @swagger
 * /api/admin/inventory/{id}/history:
 *   get:
 *     summary: Get inventory movement history
 *     tags: [Admin Inventory]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Inventory history retrieved successfully
 */
adminInventoryRouter.get("/:id/history", getInventoryHistoryController);

/**
 * @swagger
 * /api/admin/inventory/bulk-update:
 *   put:
 *     summary: Bulk update inventory items
 *     tags: [Admin Inventory]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               updates:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     inventoryId:
 *                       type: string
 *                     quantity:
 *                       type: integer
 *                     lowStockThreshold:
 *                       type: integer
 *                     reorderPoint:
 *                       type: integer
 *                     status:
 *                       type: string
 *                     reason:
 *                       type: string
 *     responses:
 *       200:
 *         description: Bulk update completed successfully
 */
adminInventoryRouter.put("/bulk-update", bulkUpdateInventoryController);

/**
 * @swagger
 * /api/admin/inventory/{id}/status:
 *   patch:
 *     summary: Update inventory status
 *     tags: [Admin Inventory]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [in_stock, low_stock, out_of_stock, discontinued]
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Status updated successfully
 */
adminInventoryRouter.patch("/:id/status", updateInventoryStatusController);

/**
 * @swagger
 * /api/admin/inventory/auto-restock:
 *   post:
 *     summary: Trigger auto-restock for low stock items
 *     tags: [Admin Inventory]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               inventoryIds:
 *                 type: array
 *                 items:
 *                   type: string
 *               restockQuantity:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Auto-restock completed successfully
 */
adminInventoryRouter.post("/auto-restock", autoRestockController);

/**
 * @swagger
 * /api/admin/inventory/export:
 *   get:
 *     summary: Export inventory report
 *     tags: [Admin Inventory]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [csv, excel, pdf]
 *         description: Export format
 *     responses:
 *       200:
 *         description: Report exported successfully
 */
adminInventoryRouter.get("/export", exportInventoryReportController);
