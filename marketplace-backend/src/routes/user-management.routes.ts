import { Router } from "express";
import {
  getAllUsersController,
  getUserStatsController,
  getUserByIdController,
  updateUserRoleController,
  toggleUserStatusController,
  deleteUserController,
  getRecentUserActivitiesController,
} from "../controllers/user-management.controller";
import { authenticate, authRbac } from "../middlewares/auth.middleware";

export const userManagementRoutes = Router();

// Admin-only user management routes
userManagementRoutes.get(
  "/admin/users",
  authenticate,
  authRbac("admin"),
  getAllUsersController
);

userManagementRoutes.get(
  "/admin/users/stats",
  authenticate,
  authRbac("admin"),
  getUserStatsController
);

userManagementRoutes.get(
  "/admin/users/activities",
  authenticate,
  authRbac("admin"),
  getRecentUserActivitiesController
);

userManagementRoutes.get(
  "/admin/users/:userId",
  authenticate,
  authRbac("admin"),
  getUserByIdController
);

userManagementRoutes.put(
  "/admin/users/:userId/role",
  authenticate,
  authRbac("admin"),
  updateUserRoleController
);

userManagementRoutes.put(
  "/admin/users/:userId/status",
  authenticate,
  authRbac("admin"),
  toggleUserStatusController
);

userManagementRoutes.delete(
  "/admin/users/:userId",
  authenticate,
  authRbac("admin"),
  deleteUserController
);
