import { Router, Request, Response } from "express";
import { authenticate, authRbac } from "../middlewares/auth.middleware";
import { timeoutAnalyzer } from "../utils/timeoutAnalyzer";
import { getPerformanceTracker } from "../middlewares/performance.middleware";
import { emailRetryService } from "../utils/emailRetryService";
import { emailMetrics } from "../utils/emailMetrics";
import logger from "../utils/logger";
import { Parser as CsvParser } from "json2csv";

export const healthRoutes = Router();

/**
 * Simple health check endpoint
 * Returns 200 OK with minimal response for connectivity testing
 */
healthRoutes.get("/", (_req: Request, res: Response) => {
  res.status(200).json({
    status: "ok",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

/**
 * HEAD request for even lighter connectivity testing
 */
healthRoutes.head("/", (_req: Request, res: Response) => {
  res.status(200).end();
});

/**
 * Detailed health check with system metrics
 * Accessible to all authenticated users
 */
healthRoutes.get("/detailed", authenticate, (req: Request, res: Response) => {
  try {
    const performanceStats = getPerformanceTracker().getStats();
    const emailHealth = emailRetryService.getHealthStatus();
    const emailStats = emailMetrics.getMetrics();

    const healthStatus = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      performance: {
        totalRequests: performanceStats.totalRequests,
        avgResponseTime: `${performanceStats.avgResponseTime}ms`,
        slowRequests: performanceStats.slowRequests,
        criticalRequests: performanceStats.criticalRequests,
      },
      email: {
        status: emailHealth.status,
        message: emailHealth.message,
        sent: emailStats.sent,
        failed: emailStats.failed,
        avgResponseTime: `${emailStats.avgResponseTime.toFixed(2)}ms`,
      },
      system: {
        memory: process.memoryUsage(),
        nodeVersion: process.version,
        platform: process.platform,
      },
    };

    // Determine overall health status
    if (
      emailHealth.status === "critical" ||
      performanceStats.criticalRequests > 0
    ) {
      healthStatus.status = "degraded";
    }

    res.status(200).json(healthStatus);
  } catch (error) {
    logger.error("Health check failed", { error });
    res.status(500).json({
      status: "error",
      message: "Health check failed",
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * Performance metrics endpoint
 * Admin only - contains detailed performance data
 */
healthRoutes.get(
  "/metrics",
  authenticate,
  authRbac("admin"),
  (req: Request, res: Response) => {
    try {
      const performanceStats = getPerformanceTracker().getStats();
      const timeoutStats = timeoutAnalyzer.getStats();
      const emailHealth = emailRetryService.getHealthStatus();
      const emailStats = emailMetrics.getMetrics();

      const metrics = {
        timestamp: new Date().toISOString(),
        performance: performanceStats,
        timeouts: timeoutStats,
        email: {
          health: emailHealth,
          metrics: emailStats,
          performanceByType: emailMetrics.getPerformanceByType(),
          deadLetterQueue: emailRetryService.getDeadLetterStats(),
        },
        system: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          nodeVersion: process.version,
          platform: process.platform,
        },
      };

      res.status(200).json(metrics);
    } catch (error) {
      logger.error("Metrics collection failed", { error });
      res.status(500).json({
        error: "Metrics collection failed",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      });
    }
  }
);

/**
 * Timeout analysis endpoint
 * Admin only - contains detailed timeout patterns
 */
healthRoutes.get(
  "/timeouts",
  authenticate,
  authRbac("admin"),
  (req: Request, res: Response) => {
    try {
      const timeoutStats = timeoutAnalyzer.getStats();
      const timeoutsByType = timeoutAnalyzer.getTimeoutsByType();

      const analysis = {
        timestamp: new Date().toISOString(),
        summary: timeoutStats,
        byType: timeoutsByType,
        recommendations: generateTimeoutRecommendations(
          timeoutStats,
          timeoutsByType
        ),
      };

      res.status(200).json(analysis);
    } catch (error) {
      logger.error("Timeout analysis failed", { error });
      res.status(500).json({
        error: "Timeout analysis failed",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      });
    }
  }
);

/**
 * Email service health endpoint
 * Admin only - contains detailed email service metrics
 */
healthRoutes.get(
  "/email",
  authenticate,
  authRbac("admin"),
  (req: Request, res: Response) => {
    try {
      const emailHealth = emailRetryService.getHealthStatus();
      const emailStats = emailMetrics.getMetrics();
      const performanceByType = emailMetrics.getPerformanceByType();
      const deadLetterStats = emailRetryService.getDeadLetterStats();

      const emailReport = {
        timestamp: new Date().toISOString(),
        health: emailHealth,
        metrics: emailStats,
        performanceByType,
        deadLetterQueue: deadLetterStats,
        recommendations: generateEmailRecommendations(emailHealth, emailStats),
      };

      res.status(200).json(emailReport);
    } catch (error) {
      logger.error("Email health check failed", { error });
      res.status(500).json({
        error: "Email health check failed",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      });
    }
  }
);

/**
 * Reset metrics endpoint (for testing/debugging)
 * Admin only - resets all monitoring data
 */
healthRoutes.post(
  "/reset",
  authenticate,
  authRbac("admin"),
  (req: Request, res: Response) => {
    try {
      // Reset all monitoring systems
      timeoutAnalyzer.reset();
      getPerformanceTracker().reset();
      emailMetrics.resetMetrics();

      logger.info("All monitoring metrics reset by admin", {
        adminId: req.user?.id,
      });

      res.status(200).json({
        message: "All monitoring metrics reset successfully",
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error("Metrics reset failed", { error });
      res.status(500).json({
        error: "Metrics reset failed",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      });
    }
  }
);

/**
 * Export metrics endpoint (admin only)
 * Supports JSON (default) and CSV formats
 */
healthRoutes.get(
  "/export",
  authenticate,
  authRbac("admin"),
  (req: Request, res: Response) => {
    try {
      const format = (req.query.format as string)?.toLowerCase() || "json";
      const performanceStats = getPerformanceTracker().getStats();
      const timeoutStats = timeoutAnalyzer.getStats();

      const exportData = {
        timestamp: new Date().toISOString(),
        performance: performanceStats,
        timeouts: timeoutStats,
      };

      if (format === "csv") {
        // Flatten the data for CSV export
        const csvData = [
          {
            ...performanceStats,
            ...timeoutStats,
          },
        ];
        const fields = Object.keys(csvData[0]);
        const parser = new CsvParser({ fields });
        const csv = parser.parse(csvData);
        res.header("Content-Type", "text/csv");
        res.attachment(`metrics-export-${Date.now()}.csv`);
        res.send(csv);
      } else {
        res.header("Content-Type", "application/json");
        res.json(exportData);
      }
    } catch (error) {
      logger.error("Metrics export failed", { error });
      res.status(500).json({
        error: "Metrics export failed",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      });
    }
  }
);

/**
 * Generate timeout recommendations based on analysis
 */
function generateTimeoutRecommendations(
  timeoutStats: any,
  timeoutsByType: any
): string[] {
  const recommendations: string[] = [];

  if (timeoutStats.recentTimeouts > 5) {
    recommendations.push(
      "High timeout rate detected - check email service health"
    );
  }

  if (timeoutsByType.route > 0) {
    recommendations.push(
      "Route-level timeouts detected - consider increasing timeout values"
    );
  }

  if (timeoutsByType.controller > 0) {
    recommendations.push(
      "Controller timeouts detected - optimize database queries"
    );
  }

  if (timeoutsByType.database > 0) {
    recommendations.push(
      "Database timeouts detected - check database performance and connection pool"
    );
  }

  if (recommendations.length === 0) {
    recommendations.push("No timeout issues detected - system performing well");
  }

  return recommendations;
}

/**
 * Generate email service recommendations
 */
function generateEmailRecommendations(
  emailHealth: any,
  emailStats: any
): string[] {
  const recommendations: string[] = [];

  if (emailHealth.status === "critical") {
    recommendations.push(
      "Email service is critical - immediate attention required"
    );
  }

  if (emailHealth.status === "warning") {
    recommendations.push(
      "Email service showing warning signs - monitor closely"
    );
  }

  if (emailStats.failed > emailStats.sent * 0.1) {
    recommendations.push(
      "High email failure rate - check email service configuration"
    );
  }

  if (emailStats.avgResponseTime > 5000) {
    recommendations.push(
      "Slow email response times - consider optimizing email service"
    );
  }

  if (recommendations.length === 0) {
    recommendations.push("Email service performing well - no issues detected");
  }

  return recommendations;
}
