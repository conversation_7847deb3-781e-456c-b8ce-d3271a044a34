import swaggerJSD<PERSON> from "swagger-jsdoc";
import swaggerUi from "swagger-ui-express";

const options: swaggerJSDoc.Options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "Marketplace API",
      version: "1.0.0",
      description:
        "API documentation for the e-commerce marketplace application",
    },
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
        },
      },
      schemas: {
        Product: {
          type: "object",
          properties: {
            id: { type: "string" },
            title: { type: "string" },
            description: { type: "string" },
            price: { type: "number" },
            category: { type: "string" },
            images: {
              type: "array",
              items: { type: "string" },
            },
            createdAt: { type: "string", format: "date-time" },
          },
        },
        AuthUser: {
          type: "object",
          properties: {
            id: { type: "string" },
            name: { type: "string" },
            email: { type: "string" },
            role: { type: "string" },
            token: { type: "string" },
          },
        },
        HealthStatus: {
          type: "string",
          enum: ["healthy", "warning", "critical"],
          description: "Health status indicator",
        },
        ApiResponse: {
          type: "object",
          properties: {
            status: {
              type: "string",
              enum: ["success", "error"],
            },
            message: {
              type: "string",
            },
            data: {
              type: "object",
            },
          },
        },
        ErrorResponse: {
          type: "object",
          properties: {
            status: {
              type: "string",
              enum: ["error"],
            },
            message: {
              type: "string",
            },
            error: {
              type: "string",
            },
          },
        },
        DLQEntry: {
          type: "object",
          properties: {
            id: { type: "string" },
            emailType: { type: "string" },
            recipientEmail: { type: "string" },
            subject: { type: "string" },
            failureReason: { type: "string" },
            priority: { type: "number" },
            retryAttempts: { type: "number" },
            maxRetryAttempts: { type: "number" },
            status: {
              type: "string",
              enum: [
                "pending",
                "processing",
                "resolved",
                "failed_permanently",
                "cancelled",
              ],
            },
            createdAt: { type: "string", format: "date-time" },
            lastRetryAt: { type: "string", format: "date-time" },
            nextRetryAt: { type: "string", format: "date-time" },
            processingNode: { type: "string" },
            userId: { type: "string" },
            errorDetails: { type: "string" },
          },
        },
      },
    },
    security: [{ bearerAuth: [] }],

    servers: [
      {
        url: process.env.API_URL || "http://localhost:5000/api",
        description: "Development server",
      },
    ],
  },
  apis: [
    "./src/routes/*.ts",
    "./src/controllers/*.ts",
    "./src/services/*.ts",
    "./src/docs/*.ts",
  ],
};

const swaggerSpec = swaggerJSDoc(options);

export { swaggerUi, swaggerSpec };
