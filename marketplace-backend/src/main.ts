import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import { AppDataSource } from "./config/db.config";
import path from "path";
import { swaggerUi, swaggerSpec } from "./config/swagger.config";
import {
  authRoutes,
  categoryRoutes,
  productRoutes,
  cartRoutes,
  adminRouter as orderAdminRoutes,
  adminInventoryRouter,
  conversationRoutes,
  healthRoutes,
  inventoryRoutes,
} from "./routes";
import { orderRoutes } from "./routes/order.routes";
import { analyticsRoutes } from "./routes/analytics.routes";
import { userManagementRoutes } from "./routes/user-management.routes";
import logger from "./utils/logger";
import {
  logRequest,
  logError,
  logUnhandledRejection,
  logUncaughtException,
} from "./middlewares/logger.middleware";
import { errorHandler } from "./middlewares/error.middleware";
import { messageRoutes } from "./routes/messages.routes";
import { monitoringRoutes } from "./routes/monitoring.routes";
import {
  generalLimiter,
  authLimiter,
  apiLimiter,
  healthLimiter,
  adminLimiter,
} from "./middleware/rateLimiter";
import { performanceMiddleware } from "./middlewares/performance.middleware";
import { performanceTracker } from "./utils/performanceTracker";
import { globalTimeout } from "./middlewares/timeout.middleware";
import { dlqProcessorJob } from "./jobs/dlqProcessor.job";

// Register global error handlers
process.on("unhandledRejection", logUnhandledRejection);
process.on("uncaughtException", logUncaughtException);

dotenv.config();

const app = express();
app.use(
  cors({
    origin: "*",
    methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
    allowedHeaders: [
      "Origin",
      "X-Requested-With",
      "Content-Type",
      "Accept",
      "Authorization",
    ],
    credentials: true,
  })
);
app.use(express.json());
app.use(performanceMiddleware);
app.use(performanceTracker.middleware); // High-resolution performance tracking
app.use(globalTimeout);
app.use(logRequest);

// API routes
app.use("/docs", swaggerUi.serve, swaggerUi.setup(swaggerSpec));
app.use("/api/health", healthLimiter, healthRoutes);
app.use("/api/auth", authLimiter, authRoutes);
app.use("/api/categories", apiLimiter, categoryRoutes);
app.use("/api/cart", apiLimiter, cartRoutes);
app.use("/api/products", generalLimiter, productRoutes);
// Serve static files with CORS headers
app.use(
  "/uploads",
  (_req, res, next) => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Methods", "GET");
    res.header(
      "Access-Control-Allow-Headers",
      "Origin, X-Requested-With, Content-Type, Accept"
    );
    next();
  },
  express.static(path.join(__dirname, "../uploads"))
);
app.use("/api/orders", apiLimiter, orderRoutes);
app.use("/api/admin/orders", adminLimiter, orderAdminRoutes);
app.use("/api/admin/inventory", adminLimiter, adminInventoryRouter);
app.use("/api/analytics", adminLimiter, analyticsRoutes);
app.use("/api", apiLimiter, userManagementRoutes);
app.use("/api/conversations", apiLimiter, conversationRoutes);
app.use("/api/messages", apiLimiter, messageRoutes);
app.use("/api/inventory", apiLimiter, inventoryRoutes);
app.use("/api/monitoring", adminLimiter, monitoringRoutes);

app.use(logError);
// @ts-ignore
app.use(errorHandler);

const PORT = process.env.PORT || 5000;

AppDataSource.initialize()
  .then(() => {
    logger.info("Database connected successfully");

    // Start DLQ processor jobs
    dlqProcessorJob.start();

    app.listen(PORT, () => {
      logger.info(`Server running on http://localhost:${PORT}`);
      logger.info("Performance monitoring and timeout protection enabled");
      logger.info("Dead Letter Queue processor jobs started");
    });
  })
  .catch((err) => {
    logger.error("Database connection failed", {
      error: err.message,
      stack: err.stack,
    });
  });

export default app;
