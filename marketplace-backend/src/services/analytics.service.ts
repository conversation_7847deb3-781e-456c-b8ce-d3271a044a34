import { AppDataSource } from "../config/db.config";
import { User, Product, Order, OrderItem, Dispute } from "../entities";
import { Between, MoreThan } from "typeorm";

export interface DashboardStats {
  totalUsers: number;
  totalProducts: number;
  totalOrders: number;
  totalRevenue: number;
  newUsersToday: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
  ordersToday: number;
  ordersThisWeek: number;
  ordersThisMonth: number;
  revenueToday: number;
  revenueThisWeek: number;
  revenueThisMonth: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  averageOrderValue: number;
  activeDisputes: number;
}

export interface TopProduct {
  id: string;
  title: string;
  totalSales: number;
  totalRevenue: number;
  orderCount: number;
}

export interface RevenueData {
  date: string;
  revenue: number;
  orders: number;
}

export interface UserGrowthData {
  date: string;
  newUsers: number;
  totalUsers: number;
}

export interface SellerStats {
  totalProducts: number;
  totalOrders: number;
  totalRevenue: number;
  pendingOrders: number;
  shippedOrders: number;
  deliveredOrders: number;
  averageOrderValue: number;
  topProducts: TopProduct[];
}

// Get comprehensive dashboard statistics
export const getDashboardStats = async (): Promise<DashboardStats> => {
  const userRepo = AppDataSource.getRepository(User);
  const productRepo = AppDataSource.getRepository(Product);
  const orderRepo = AppDataSource.getRepository(Order);
  const disputeRepo = AppDataSource.getRepository(Dispute);

  const now = new Date();
  const startOfToday = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate()
  );
  const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  // Basic counts
  const [
    totalUsers,
    totalProducts,
    totalOrders,
    newUsersToday,
    newUsersThisWeek,
    newUsersThisMonth,
    ordersToday,
    ordersThisWeek,
    ordersThisMonth,
    pendingOrders,
    completedOrders,
    cancelledOrders,
    activeDisputes,
  ] = await Promise.all([
    userRepo.count(),
    productRepo.count(),
    orderRepo.count(),
    userRepo.count({ where: { createdAt: MoreThan(startOfToday) } }),
    userRepo.count({ where: { createdAt: MoreThan(startOfWeek) } }),
    userRepo.count({ where: { createdAt: MoreThan(startOfMonth) } }),
    orderRepo.count({ where: { createdAt: MoreThan(startOfToday) } }),
    orderRepo.count({ where: { createdAt: MoreThan(startOfWeek) } }),
    orderRepo.count({ where: { createdAt: MoreThan(startOfMonth) } }),
    orderRepo.count({ where: { status: "pending" } }),
    orderRepo.count({ where: { status: "fulfilled" } }),
    orderRepo.count({ where: { status: "cancelled" } }),
    disputeRepo.count({ where: { status: "open" } }),
  ]);

  // Revenue calculations - only count fulfilled orders
  const fulfilledOrders = await orderRepo.find({
    where: { status: "fulfilled" },
  });
  const totalRevenue = fulfilledOrders.reduce(
    (sum, order) => sum + order.total,
    0
  );

  const fulfilledOrdersToday = await orderRepo.find({
    where: {
      createdAt: MoreThan(startOfToday),
      status: "fulfilled",
    },
  });
  const revenueToday = fulfilledOrdersToday.reduce(
    (sum, order) => sum + order.total,
    0
  );

  const fulfilledOrdersThisWeek = await orderRepo.find({
    where: {
      createdAt: MoreThan(startOfWeek),
      status: "fulfilled",
    },
  });
  const revenueThisWeek = fulfilledOrdersThisWeek.reduce(
    (sum, order) => sum + order.total,
    0
  );

  const fulfilledOrdersThisMonth = await orderRepo.find({
    where: {
      createdAt: MoreThan(startOfMonth),
      status: "fulfilled",
    },
  });
  const revenueThisMonth = fulfilledOrdersThisMonth.reduce(
    (sum, order) => sum + order.total,
    0
  );

  const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

  return {
    totalUsers,
    totalProducts,
    totalOrders,
    totalRevenue,
    newUsersToday,
    newUsersThisWeek,
    newUsersThisMonth,
    ordersToday,
    ordersThisWeek,
    ordersThisMonth,
    revenueToday,
    revenueThisWeek,
    revenueThisMonth,
    pendingOrders,
    completedOrders,
    cancelledOrders,
    averageOrderValue,
    activeDisputes,
  };
};

// Get top performing products
export const getTopProducts = async (
  limit: number = 10
): Promise<TopProduct[]> => {
  const orderItemRepo = AppDataSource.getRepository(OrderItem);

  const topProducts = await orderItemRepo
    .createQueryBuilder("orderItem")
    .leftJoinAndSelect("orderItem.product", "product")
    .select([
      "product.id as id",
      "product.title as title",
      "SUM(orderItem.quantity) as totalSales",
      "SUM(orderItem.totalPrice) as totalRevenue",
      "COUNT(DISTINCT orderItem.id) as orderCount",
    ])
    .groupBy("product.id, product.title")
    .orderBy("totalRevenue", "DESC")
    .limit(limit)
    .getRawMany();

  return topProducts.map((item) => ({
    id: item.id,
    title: item.title,
    totalSales: parseInt(item.totalSales) || 0,
    totalRevenue: parseFloat(item.totalRevenue) || 0,
    orderCount: parseInt(item.orderCount) || 0,
  }));
};

// Get revenue data for charts (last 30 days) - only fulfilled orders
export const getRevenueData = async (
  days: number = 30
): Promise<RevenueData[]> => {
  const orderRepo = AppDataSource.getRepository(Order);
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  const orders = await orderRepo.find({
    where: {
      createdAt: MoreThan(startDate),
      status: "fulfilled",
    },
    order: { createdAt: "ASC" },
  });

  // Group by date
  const revenueByDate: { [key: string]: { revenue: number; orders: number } } =
    {};

  orders.forEach((order) => {
    const date = order.createdAt.toISOString().split("T")[0];
    if (!revenueByDate[date]) {
      revenueByDate[date] = { revenue: 0, orders: 0 };
    }
    revenueByDate[date].revenue += order.total;
    revenueByDate[date].orders += 1;
  });

  // Fill in missing dates with zero values
  const result: RevenueData[] = [];
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split("T")[0];

    result.push({
      date: dateStr,
      revenue: revenueByDate[dateStr]?.revenue || 0,
      orders: revenueByDate[dateStr]?.orders || 0,
    });
  }

  return result;
};

// Get user growth data (last 30 days)
export const getUserGrowthData = async (
  days: number = 30
): Promise<UserGrowthData[]> => {
  const userRepo = AppDataSource.getRepository(User);
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  const users = await userRepo.find({
    where: { createdAt: MoreThan(startDate) },
    order: { createdAt: "ASC" },
  });

  // Group by date
  const usersByDate: { [key: string]: number } = {};
  users.forEach((user) => {
    const date = user.createdAt.toISOString().split("T")[0];
    usersByDate[date] = (usersByDate[date] || 0) + 1;
  });

  // Get total users up to each date
  const totalUsers = await userRepo.count();
  let runningTotal = totalUsers - users.length;

  const result: UserGrowthData[] = [];
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split("T")[0];

    const newUsers = usersByDate[dateStr] || 0;
    runningTotal += newUsers;

    result.push({
      date: dateStr,
      newUsers,
      totalUsers: runningTotal,
    });
  }

  return result;
};

// Get seller-specific statistics
export const getSellerStats = async (
  sellerId: string
): Promise<SellerStats> => {
  const productRepo = AppDataSource.getRepository(Product);
  const orderItemRepo = AppDataSource.getRepository(OrderItem);

  // Get seller's products
  const totalProducts = await productRepo.count({
    where: { seller: { id: sellerId } },
  });

  // Get seller's order items
  const orderItems = await orderItemRepo.find({
    where: { product: { seller: { id: sellerId } } },
    relations: ["product"],
  });

  const totalOrders = orderItems.length;
  const totalRevenue = orderItems.reduce(
    (sum, item) => sum + item.totalPrice,
    0
  );
  const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

  // Count orders by status
  const pendingOrders = orderItems.filter(
    (item) => item.status === "PENDING"
  ).length;
  const shippedOrders = orderItems.filter(
    (item) => item.status === "SHIPPED"
  ).length;
  const deliveredOrders = orderItems.filter(
    (item) => item.status === "DELIVERED"
  ).length;

  // Get top products for this seller
  const topProducts = await getTopProductsForSeller(sellerId, 5);

  return {
    totalProducts,
    totalOrders,
    totalRevenue,
    pendingOrders,
    shippedOrders,
    deliveredOrders,
    averageOrderValue,
    topProducts,
  };
};

// Get top products for a specific seller
export const getTopProductsForSeller = async (
  sellerId: string,
  limit: number = 5
): Promise<TopProduct[]> => {
  const orderItemRepo = AppDataSource.getRepository(OrderItem);

  const topProducts = await orderItemRepo
    .createQueryBuilder("orderItem")
    .leftJoinAndSelect("orderItem.product", "product")
    .where("product.sellerId = :sellerId", { sellerId })
    .select([
      "product.id as id",
      "product.title as title",
      "SUM(orderItem.quantity) as totalSales",
      "SUM(orderItem.totalPrice) as totalRevenue",
      "COUNT(DISTINCT orderItem.id) as orderCount",
    ])
    .groupBy("product.id, product.title")
    .orderBy("totalRevenue", "DESC")
    .limit(limit)
    .getRawMany();

  return topProducts.map((item) => ({
    id: item.id,
    title: item.title,
    totalSales: parseInt(item.totalSales) || 0,
    totalRevenue: parseFloat(item.totalRevenue) || 0,
    orderCount: parseInt(item.orderCount) || 0,
  }));
};
