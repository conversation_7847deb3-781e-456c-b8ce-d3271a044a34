import { AppDataSource } from "../config/db.config";
import { redisClient } from "../config/redis.config";
import { User } from "../entities/User.entity";
import { signAccessToken, signRefreshToken } from "../utils/jwt";
import bcrypt from "bcrypt";
import { sendOTPEmail } from "../utils/mailer";
import { canRequestOTP, deleteOTP, getOTP, setOTP } from "../utils/otp";
import logger from "../utils/logger";

export const registerUser = async (
  name: string,
  email: string,
  password: string,
  role: "buyer" | "seller" | "admin" = "buyer"
) => {
  const userRepo = AppDataSource.getRepository(User);
  const hashedPassword = await bcrypt.hash(password, 10);
  const normalizedEmail = email.trim().toLowerCase();
  const userExists = await userRepo.findOneBy({ email: normalizedEmail });

  if (userExists) {
    throw new Error("User with provided email already exists");
  }
  const newUser = userRepo.create({
    name,
    email: normalizedEmail,
    password: hashedPassword,
    role,
  });
  return await userRepo.save(newUser);
};

export const userLogin = async (email: string, password: string) => {
  const userRepo = AppDataSource.getRepository(User);
  const refreshTokenRepo = AppDataSource.getRepository("RefreshToken");
  const normalizedEmail = email.trim().toLowerCase();
  const userFound = await userRepo.findOne({
    where: { email: normalizedEmail },
    select: ["id", "email", "name", "password", "role", "isVerified"],
  });

  if (!userFound) {
    throw new Error("User not found");
  }

  const isPasswordValid = await bcrypt.compare(password, userFound.password);

  if (!isPasswordValid) {
    throw new Error("Invalid credentials");
  }

  const accessToken = signAccessToken(userFound.id, userFound.role);
  const refreshToken = signRefreshToken(userFound.id);

  // Save refresh token
  const newToken = refreshTokenRepo.create({
    user: userFound,
    token: refreshToken,
  });
  await refreshTokenRepo.save(newToken);

  return {
    accessToken,
    refreshToken,
    user: {
      id: userFound.id,
      name: userFound.name,
      email: userFound.email,
      role: userFound.role,
    },
  };
};

export const forgotPasswordService = async (email: string) => {
  const userRepo = AppDataSource.getRepository(User);
  const user = await userRepo.findOneBy({ email });

  if (!user) {
    logger.warn(`Password reset requested for non-existent email: ${email}`);
    throw new Error("User not found");
  }

  const canRequest = await canRequestOTP(email);
  if (!canRequest) {
    logger.warn(`Too many OTP requests for email: ${email}`);
    throw new Error("Too many OTP requests. Try again later.");
  }

  // Generate a 6-digit OTP
  const otp = Math.floor(100000 + Math.random() * 900000).toString();

  await setOTP(email, otp);

  // Send OTP email (fire-and-forget to avoid blocking password reset flow)
  sendOTPEmail(email, otp).catch((emailError: any) => {
    logger.warn(`Email sending failed, logging OTP for development: ${otp}`, {
      error: emailError.message,
      email,
    });
    console.log(`🔐 EMAIL FAILED - OTP for ${email}: ${otp}`);
  });

  logger.info(`Password reset OTP generated for: ${email}`);
};

export const resetPasswordService = async (
  email: string,
  otp: string,
  newPassword: string
) => {
  const storedOtp = await getOTP(email);
  if (!storedOtp || storedOtp !== otp) {
    throw new Error("Invalid or expired OTP");
  }

  const userRepo = AppDataSource.getRepository(User);
  const user = await userRepo.findOneBy({ email });
  if (!user) throw new Error("User not found");

  user.password = await bcrypt.hash(newPassword, 10);
  await userRepo.save(user);
  await deleteOTP(email);
};
