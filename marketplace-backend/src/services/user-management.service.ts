import { AppDataSource } from "../config/db.config";
import { User } from "../entities";
import { MoreThan, Like, In, ILike } from "typeorm";

export interface UserStats {
  totalUsers: number;
  totalAdmins: number;
  totalSellers: number;
  totalBuyers: number;
  newUsersToday: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
  activeUsers: number;
}

export interface UserWithStats extends User {
  orderCount?: number;
  totalSpent?: number;
  totalEarned?: number;
  lastOrderDate?: Date;
  status: "active" | "inactive" | "suspended";
}

// Get all users with optional filtering
export const getAllUsers = async (
  page: number = 1,
  limit: number = 20,
  search?: string,
  role?: string,
  status?: string
): Promise<{
  users: UserWithStats[];
  total: number;
  totalPages: number;
}> => {
  const userRepo = AppDataSource.getRepository(User);

  let whereConditions: any = {};

  // Add search filter (case-insensitive)
  if (search) {
    whereConditions = [
      { name: ILike(`%${search}%`) },
      { email: ILike(`%${search}%`) },
    ];
  }

  // Add role filter
  if (role && role !== "all") {
    if (Array.isArray(whereConditions)) {
      whereConditions = whereConditions.map((condition) => ({
        ...condition,
        role: role,
      }));
    } else {
      whereConditions.role = role;
    }
  }

  const [users, total] = await userRepo.findAndCount({
    where: whereConditions,
    order: { createdAt: "DESC" },
    skip: (page - 1) * limit,
    take: limit,
  });

  // Add user statistics and status
  const usersWithStats: UserWithStats[] = await Promise.all(
    users.map(async (user) => {
      // Calculate user status based on name prefix and last activity
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      let status: "active" | "inactive" | "suspended" = "active";
      if (user.name.startsWith("[SUSPENDED]")) {
        status = "suspended";
      } else if (user.updatedAt < thirtyDaysAgo) {
        status = "inactive";
      }

      return {
        ...user,
        orderCount: 0, // Will be populated with real order data
        totalSpent: 0,
        totalEarned: 0,
        lastOrderDate: undefined,
        status,
      };
    })
  );

  return {
    users: usersWithStats,
    total,
    totalPages: Math.ceil(total / limit),
  };
};

// Get user statistics
export const getUserStats = async (): Promise<UserStats> => {
  const userRepo = AppDataSource.getRepository(User);

  const now = new Date();
  const startOfToday = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate()
  );
  const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  const [
    totalUsers,
    totalAdmins,
    totalSellers,
    totalBuyers,
    newUsersToday,
    newUsersThisWeek,
    newUsersThisMonth,
    activeUsers,
  ] = await Promise.all([
    userRepo.count(),
    userRepo.count({ where: { role: "admin" } }),
    userRepo.count({ where: { role: "seller" } }),
    userRepo.count({ where: { role: "buyer" } }),
    userRepo.count({ where: { createdAt: MoreThan(startOfToday) } }),
    userRepo.count({ where: { createdAt: MoreThan(startOfWeek) } }),
    userRepo.count({ where: { createdAt: MoreThan(startOfMonth) } }),
    userRepo.count({ where: { updatedAt: MoreThan(thirtyDaysAgo) } }),
  ]);

  return {
    totalUsers,
    totalAdmins,
    totalSellers,
    totalBuyers,
    newUsersToday,
    newUsersThisWeek,
    newUsersThisMonth,
    activeUsers,
  };
};

// Get user by ID with detailed information
export const getUserById = async (
  userId: string
): Promise<UserWithStats | null> => {
  const userRepo = AppDataSource.getRepository(User);

  const user = await userRepo.findOne({
    where: { id: userId },
  });

  if (!user) {
    return null;
  }

  // Calculate user status
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  let status: "active" | "inactive" | "suspended" = "active";
  if (user.updatedAt < thirtyDaysAgo) {
    status = "inactive";
  }

  return {
    ...user,
    orderCount: 0, // Will be populated with real order data
    totalSpent: 0,
    totalEarned: 0,
    lastOrderDate: undefined,
    status,
  };
};

// Update user role
export const updateUserRole = async (
  userId: string,
  newRole: "buyer" | "seller" | "admin"
): Promise<User | null> => {
  const userRepo = AppDataSource.getRepository(User);

  const user = await userRepo.findOne({
    where: { id: userId },
  });

  if (!user) {
    return null;
  }

  user.role = newRole;
  user.updatedAt = new Date();

  return await userRepo.save(user);
};

// Suspend/unsuspend user - using a simple approach with user name modification
export const toggleUserStatus = async (
  userId: string,
  suspend: boolean
): Promise<User | null> => {
  const userRepo = AppDataSource.getRepository(User);

  const user = await userRepo.findOne({
    where: { id: userId },
  });

  if (!user) {
    return null;
  }

  // Simple approach: add/remove [SUSPENDED] prefix to name
  if (suspend) {
    if (!user.name.startsWith("[SUSPENDED]")) {
      user.name = `[SUSPENDED] ${user.name}`;
    }
  } else {
    user.name = user.name.replace("[SUSPENDED] ", "");
  }

  user.updatedAt = new Date();

  return await userRepo.save(user);
};

// Delete user (soft delete - in real system you'd have a deletedAt field)
export const deleteUser = async (userId: string): Promise<boolean> => {
  const userRepo = AppDataSource.getRepository(User);

  try {
    const result = await userRepo.delete(userId);
    return result.affected !== undefined && result.affected > 0;
  } catch (error) {
    console.error("Error deleting user:", error);
    return false;
  }
};

// Get recent user activities
export const getRecentUserActivities = async (limit: number = 10) => {
  const userRepo = AppDataSource.getRepository(User);

  const recentUsers = await userRepo.find({
    order: { createdAt: "DESC" },
    take: limit,
  });

  return recentUsers.map((user) => ({
    id: user.id,
    type: "user_registration",
    message: `New ${user.role} registered: ${user.name}`,
    time: user.createdAt,
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
    },
  }));
};
