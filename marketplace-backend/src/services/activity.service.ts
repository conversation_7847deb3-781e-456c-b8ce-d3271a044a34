import { AppDataSource } from "../config/db.config";
import { User, Product, Order, OrderItem } from "../entities";
import { MoreThan } from "typeorm";

export interface ActivityItem {
  id: string;
  type: "new_user" | "new_order" | "new_product" | "order_fulfilled" | "order_cancelled" | "high_value_order";
  message: string;
  time: string;
  severity: "info" | "success" | "warning" | "error";
  relatedId?: string;
  amount?: number;
}

// Get recent system activities
export const getRecentActivities = async (limit: number = 10): Promise<ActivityItem[]> => {
  const userRepo = AppDataSource.getRepository(User);
  const productRepo = AppDataSource.getRepository(Product);
  const orderRepo = AppDataSource.getRepository(Order);

  const activities: ActivityItem[] = [];

  try {
    // Get recent users (last 7 days)
    const recentUsers = await userRepo.find({
      where: { createdAt: MoreThan(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) },
      order: { createdAt: "DESC" },
      take: 5,
    });

    // Get recent orders (last 7 days)
    const recentOrders = await orderRepo.find({
      where: { createdAt: MoreThan(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) },
      order: { createdAt: "DESC" },
      take: 10,
      relations: ["buyer"],
    });

    // Get recent products (last 7 days)
    const recentProducts = await productRepo.find({
      where: { createdAt: MoreThan(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) },
      order: { createdAt: "DESC" },
      take: 5,
      relations: ["seller"],
    });

    // Add user activities
    recentUsers.forEach((user) => {
      activities.push({
        id: `user-${user.id}`,
        type: "new_user",
        message: `New ${user.role} registered: ${user.name}`,
        time: getTimeAgo(user.createdAt),
        severity: "info",
        relatedId: user.id,
      });
    });

    // Add product activities
    recentProducts.forEach((product) => {
      activities.push({
        id: `product-${product.id}`,
        type: "new_product",
        message: `New product listed: ${product.title} by ${product.seller.name}`,
        time: getTimeAgo(product.createdAt),
        severity: "info",
        relatedId: product.id,
      });
    });

    // Add order activities
    recentOrders.forEach((order) => {
      let activityType: ActivityItem["type"] = "new_order";
      let severity: ActivityItem["severity"] = "info";
      let message = "";

      switch (order.status) {
        case "pending":
          activityType = "new_order";
          severity = "info";
          message = `New order placed by ${order.buyer.name}: KES ${order.total.toLocaleString()}`;
          break;
        case "fulfilled":
          activityType = "order_fulfilled";
          severity = "success";
          message = `Order fulfilled for ${order.buyer.name}: KES ${order.total.toLocaleString()}`;
          break;
        case "cancelled":
          activityType = "order_cancelled";
          severity = "warning";
          message = `Order cancelled for ${order.buyer.name}: KES ${order.total.toLocaleString()}`;
          break;
        default:
          activityType = "new_order";
          severity = "info";
          message = `Order ${order.status} for ${order.buyer.name}: KES ${order.total.toLocaleString()}`;
      }

      // Check for high value orders (over KES 50,000)
      if (order.total > 50000) {
        activityType = "high_value_order";
        severity = "success";
        message = `High value order: KES ${order.total.toLocaleString()} by ${order.buyer.name}`;
      }

      activities.push({
        id: `order-${order.id}`,
        type: activityType,
        message,
        time: getTimeAgo(order.createdAt),
        severity,
        relatedId: order.id,
        amount: order.total,
      });
    });

    // Sort all activities by time (most recent first)
    activities.sort((a, b) => {
      const timeA = parseTimeAgo(a.time);
      const timeB = parseTimeAgo(b.time);
      return timeA - timeB;
    });

    return activities.slice(0, limit);
  } catch (error) {
    console.error("Error fetching recent activities:", error);
    return [];
  }
};

// Helper function to get time ago string
function getTimeAgo(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return "Just now";
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? "s" : ""} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? "s" : ""} ago`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? "s" : ""} ago`;
  } else {
    return date.toLocaleDateString();
  }
}

// Helper function to parse time ago for sorting
function parseTimeAgo(timeAgo: string): number {
  if (timeAgo === "Just now") return 0;
  
  const match = timeAgo.match(/(\d+)\s+(minute|hour|day)s?\s+ago/);
  if (!match) return 999999; // For date strings, put them last
  
  const value = parseInt(match[1]);
  const unit = match[2];
  
  switch (unit) {
    case "minute":
      return value;
    case "hour":
      return value * 60;
    case "day":
      return value * 60 * 24;
    default:
      return 999999;
  }
}

// Get activity summary for dashboard
export const getActivitySummary = async (): Promise<{
  newUsersToday: number;
  newOrdersToday: number;
  newProductsToday: number;
  highValueOrdersToday: number;
}> => {
  const userRepo = AppDataSource.getRepository(User);
  const productRepo = AppDataSource.getRepository(Product);
  const orderRepo = AppDataSource.getRepository(Order);

  const startOfToday = new Date();
  startOfToday.setHours(0, 0, 0, 0);

  try {
    const [newUsersToday, newOrdersToday, newProductsToday, highValueOrdersToday] = await Promise.all([
      userRepo.count({ where: { createdAt: MoreThan(startOfToday) } }),
      orderRepo.count({ where: { createdAt: MoreThan(startOfToday) } }),
      productRepo.count({ where: { createdAt: MoreThan(startOfToday) } }),
      orderRepo.count({ 
        where: { 
          createdAt: MoreThan(startOfToday),
          total: MoreThan(50000)
        } 
      }),
    ]);

    return {
      newUsersToday,
      newOrdersToday,
      newProductsToday,
      highValueOrdersToday,
    };
  } catch (error) {
    console.error("Error fetching activity summary:", error);
    return {
      newUsersToday: 0,
      newOrdersToday: 0,
      newProductsToday: 0,
      highValueOrdersToday: 0,
    };
  }
};
