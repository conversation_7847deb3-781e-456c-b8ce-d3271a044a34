import { AppDataSource } from "../../config/db.config";
import { Inventory, StockMovement, Product, User, StockMovementType } from "../../entities";
import inventoryService from "../inventory.service";
import logger from "../../utils/logger";

interface AdminInventoryFilters {
  page: number;
  limit: number;
  search?: string;
  status?: string;
  sellerId?: string;
  category?: string;
  sortBy: string;
  sortOrder: "asc" | "desc";
}

interface BulkUpdateItem {
  inventoryId: string;
  quantity?: number;
  lowStockThreshold?: number;
  reorderPoint?: number;
  reorderQuantity?: number;
  status?: string;
  reason?: string;
}

class AdminInventoryService {
  private inventoryRepo = AppDataSource.getRepository(Inventory);
  private stockMovementRepo = AppDataSource.getRepository(StockMovement);
  private productRepo = AppDataSource.getRepository(Product);
  private userRepo = AppDataSource.getRepository(User);

  async getAllInventory(filters: AdminInventoryFilters) {
    try {
      const {
        page,
        limit,
        search,
        status,
        sellerId,
        category,
        sortBy,
        sortOrder,
      } = filters;

      let query = this.inventoryRepo
        .createQueryBuilder("inventory")
        .leftJoinAndSelect("inventory.product", "product")
        .leftJoinAndSelect("product.seller", "seller")
        .leftJoinAndSelect("product.category", "productCategory");

      // Apply search filter
      if (search) {
        query = query.where(
          "(product.title ILIKE :search OR inventory.sku ILIKE :search OR seller.name ILIKE :search)",
          { search: `%${search}%` }
        );
      }

      // Apply status filter
      if (status && status !== "all") {
        query = query.andWhere("inventory.status = :status", { status });
      }

      // Apply seller filter
      if (sellerId) {
        query = query.andWhere("seller.id = :sellerId", { sellerId });
      }

      // Apply category filter
      if (category) {
        query = query.andWhere("productCategory.name = :category", { category });
      }

      // Apply sorting
      const validSortFields = ["createdAt", "currentStock", "product.title", "status"];
      const sortField = validSortFields.includes(sortBy) ? sortBy : "createdAt";
      query = query.orderBy(sortField, sortOrder.toUpperCase() as "ASC" | "DESC");

      // Apply pagination
      const offset = (page - 1) * limit;
      query = query.skip(offset).take(limit);

      const [inventories, total] = await query.getManyAndCount();

      return {
        inventories,
        total,
        page,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      };
    } catch (error) {
      logger.error("Failed to get all inventory", { error });
      throw error;
    }
  }

  async getComprehensiveStats() {
    try {
      const [
        totalProducts,
        inStock,
        lowStock,
        outOfStock,
        discontinued,
        totalValue,
        averageStockLevel,
        topCategories,
        recentMovements,
      ] = await Promise.all([
        this.inventoryRepo.count(),
        this.inventoryRepo.count({ where: { status: "in_stock" } }),
        this.inventoryRepo.count({ where: { status: "low_stock" } }),
        this.inventoryRepo.count({ where: { status: "out_of_stock" } }),
        this.inventoryRepo.count({ where: { status: "discontinued" } }),
        this.getTotalInventoryValue(),
        this.getAverageStockLevel(),
        this.getTopCategories(),
        this.getRecentStockMovements(10),
      ]);

      return {
        overview: {
          totalProducts,
          inStock,
          lowStock,
          outOfStock,
          discontinued,
          totalValue,
          averageStockLevel,
        },
        distribution: {
          inStockPercentage: (inStock / totalProducts) * 100,
          lowStockPercentage: (lowStock / totalProducts) * 100,
          outOfStockPercentage: (outOfStock / totalProducts) * 100,
          discontinuedPercentage: (discontinued / totalProducts) * 100,
        },
        topCategories,
        recentMovements,
      };
    } catch (error) {
      logger.error("Failed to get comprehensive stats", { error });
      throw error;
    }
  }

  async getLowStockAlerts(severity: string = "all") {
    try {
      let query = this.inventoryRepo
        .createQueryBuilder("inventory")
        .leftJoinAndSelect("inventory.product", "product")
        .leftJoinAndSelect("product.seller", "seller")
        .where("inventory.currentStock <= inventory.lowStockThreshold");

      if (severity === "critical") {
        query = query.andWhere("inventory.currentStock = 0");
      } else if (severity === "warning") {
        query = query.andWhere("inventory.currentStock > 0 AND inventory.currentStock <= inventory.reorderPoint");
      }

      const alerts = await query
        .orderBy("inventory.currentStock", "ASC")
        .getMany();

      return alerts.map(inventory => ({
        ...inventory,
        severity: inventory.currentStock === 0 ? "critical" : 
                 inventory.currentStock <= inventory.reorderPoint ? "warning" : "info",
        daysUntilStockout: this.calculateDaysUntilStockout(inventory),
        suggestedReorderQuantity: inventory.reorderQuantity || inventory.lowStockThreshold * 2,
      }));
    } catch (error) {
      logger.error("Failed to get low stock alerts", { error });
      throw error;
    }
  }

  async getInventoryAnalytics(period: string) {
    try {
      const days = this.parsePeriod(period);
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const [
        stockMovements,
        stockLevels,
        topMovingProducts,
        categoryPerformance,
      ] = await Promise.all([
        this.getStockMovementTrends(startDate),
        this.getStockLevelTrends(startDate),
        this.getTopMovingProducts(startDate, 10),
        this.getCategoryPerformance(startDate),
      ]);

      return {
        period,
        stockMovements,
        stockLevels,
        topMovingProducts,
        categoryPerformance,
      };
    } catch (error) {
      logger.error("Failed to get inventory analytics", { error });
      throw error;
    }
  }

  async getInventoryById(id: string) {
    try {
      const inventory = await this.inventoryRepo
        .createQueryBuilder("inventory")
        .leftJoinAndSelect("inventory.product", "product")
        .leftJoinAndSelect("product.seller", "seller")
        .leftJoinAndSelect("product.category", "category")
        .where("inventory.id = :id", { id })
        .getOne();

      if (!inventory) {
        return null;
      }

      // Get recent stock movements
      const recentMovements = await this.stockMovementRepo
        .createQueryBuilder("movement")
        .where("movement.inventoryId = :inventoryId", { inventoryId: id })
        .orderBy("movement.createdAt", "DESC")
        .limit(20)
        .getMany();

      return {
        ...inventory,
        recentMovements,
        needsAttention: inventory.currentStock <= inventory.lowStockThreshold,
        daysUntilStockout: this.calculateDaysUntilStockout(inventory),
      };
    } catch (error) {
      logger.error("Failed to get inventory by ID", { error });
      throw error;
    }
  }

  async getInventoryHistory(inventoryId: string, page: number, limit: number) {
    try {
      const offset = (page - 1) * limit;

      const [movements, total] = await this.stockMovementRepo
        .createQueryBuilder("movement")
        .leftJoinAndSelect("movement.performedBy", "user")
        .where("movement.inventoryId = :inventoryId", { inventoryId })
        .orderBy("movement.createdAt", "DESC")
        .skip(offset)
        .take(limit)
        .getManyAndCount();

      return {
        movements,
        total,
        page,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      logger.error("Failed to get inventory history", { error });
      throw error;
    }
  }

  async bulkUpdateInventory(updates: BulkUpdateItem[], adminId: string) {
    try {
      const results = [];

      for (const update of updates) {
        try {
          const inventory = await this.inventoryRepo.findOne({
            where: { id: update.inventoryId },
            relations: ["product"],
          });

          if (!inventory) {
            results.push({
              inventoryId: update.inventoryId,
              success: false,
              error: "Inventory not found",
            });
            continue;
          }

          // Update inventory fields
          if (update.quantity !== undefined) {
            const previousStock = inventory.currentStock;
            inventory.currentStock = update.quantity;

            // Create stock movement record
            await inventoryService.createStockMovement({
              inventoryId: inventory.id,
              quantity: update.quantity - previousStock,
              type: StockMovementType.ADJUSTMENT,
              reason: update.reason || "Bulk update by admin",
              performedBy: adminId,
            });
          }

          if (update.lowStockThreshold !== undefined) {
            inventory.lowStockThreshold = update.lowStockThreshold;
          }

          if (update.reorderPoint !== undefined) {
            inventory.reorderPoint = update.reorderPoint;
          }

          if (update.reorderQuantity !== undefined) {
            inventory.reorderQuantity = update.reorderQuantity;
          }

          if (update.status) {
            inventory.status = update.status as any;
          }

          // Update status based on current stock
          inventory.status = this.calculateStatus(inventory);

          await this.inventoryRepo.save(inventory);

          results.push({
            inventoryId: update.inventoryId,
            success: true,
            data: inventory,
          });
        } catch (error: any) {
          results.push({
            inventoryId: update.inventoryId,
            success: false,
            error: error.message,
          });
        }
      }

      return results;
    } catch (error) {
      logger.error("Failed to perform bulk update", { error });
      throw error;
    }
  }

  async updateInventoryStatus(
    inventoryId: string,
    status: string,
    reason: string,
    adminId: string
  ) {
    try {
      const inventory = await this.inventoryRepo.findOne({
        where: { id: inventoryId },
        relations: ["product"],
      });

      if (!inventory) {
        throw new Error("Inventory not found");
      }

      const previousStatus = inventory.status;
      inventory.status = status as any;

      await this.inventoryRepo.save(inventory);

      // Log the status change
      logger.info("Inventory status updated by admin", {
        inventoryId,
        previousStatus,
        newStatus: status,
        adminId,
        reason,
      });

      return inventory;
    } catch (error) {
      logger.error("Failed to update inventory status", { error });
      throw error;
    }
  }

  async autoRestock(inventoryIds: string[], restockQuantity: number, adminId: string) {
    try {
      const results = [];

      for (const inventoryId of inventoryIds) {
        try {
          const inventory = await this.inventoryRepo.findOne({
            where: { id: inventoryId },
            relations: ["product"],
          });

          if (!inventory) {
            results.push({
              inventoryId,
              success: false,
              error: "Inventory not found",
            });
            continue;
          }

          const quantity = restockQuantity || inventory.reorderQuantity || inventory.lowStockThreshold * 2;

          await inventoryService.updateStock({
            inventoryId,
            quantity,
            type: StockMovementType.RESTOCK,
            reason: "Auto-restock triggered by admin",
            performedBy: adminId,
          });

          results.push({
            inventoryId,
            success: true,
            restockedQuantity: quantity,
          });
        } catch (error: any) {
          results.push({
            inventoryId,
            success: false,
            error: error.message,
          });
        }
      }

      return results;
    } catch (error) {
      logger.error("Failed to perform auto-restock", { error });
      throw error;
    }
  }

  async exportInventoryReport(format: string) {
    try {
      const inventories = await this.inventoryRepo
        .createQueryBuilder("inventory")
        .leftJoinAndSelect("inventory.product", "product")
        .leftJoinAndSelect("product.seller", "seller")
        .leftJoinAndSelect("product.category", "category")
        .getMany();

      switch (format) {
        case "csv":
          return this.generateCSVReport(inventories);
        case "excel":
          return this.generateExcelReport(inventories);
        case "pdf":
          return this.generatePDFReport(inventories);
        default:
          throw new Error("Unsupported export format");
      }
    } catch (error) {
      logger.error("Failed to export inventory report", { error });
      throw error;
    }
  }

  // Helper methods
  private async getTotalInventoryValue(): Promise<number> {
    const result = await this.inventoryRepo
      .createQueryBuilder("inventory")
      .select("SUM(inventory.currentStock * COALESCE(inventory.costPrice, 0))", "total")
      .getRawOne();
    return parseFloat(result.total) || 0;
  }

  private async getAverageStockLevel(): Promise<number> {
    const result = await this.inventoryRepo
      .createQueryBuilder("inventory")
      .select("AVG(inventory.currentStock)", "average")
      .getRawOne();
    return parseFloat(result.average) || 0;
  }

  private async getTopCategories() {
    return await this.inventoryRepo
      .createQueryBuilder("inventory")
      .leftJoin("inventory.product", "product")
      .leftJoin("product.category", "category")
      .select("category.name", "categoryName")
      .addSelect("COUNT(*)", "productCount")
      .addSelect("SUM(inventory.currentStock)", "totalStock")
      .groupBy("category.name")
      .orderBy("productCount", "DESC")
      .limit(5)
      .getRawMany();
  }

  private async getRecentStockMovements(limit: number) {
    return await this.stockMovementRepo
      .createQueryBuilder("movement")
      .leftJoinAndSelect("movement.inventory", "inventory")
      .leftJoinAndSelect("inventory.product", "product")
      .leftJoinAndSelect("movement.performedBy", "user")
      .orderBy("movement.createdAt", "DESC")
      .limit(limit)
      .getMany();
  }

  private calculateDaysUntilStockout(inventory: Inventory): number | null {
    // This is a simplified calculation
    // In a real system, you'd use historical sales data
    const dailyUsage = 1; // Placeholder
    if (dailyUsage <= 0) return null;
    return Math.floor(inventory.currentStock / dailyUsage);
  }

  private parsePeriod(period: string): number {
    const periodMap: { [key: string]: number } = {
      "7d": 7,
      "30d": 30,
      "90d": 90,
      "1y": 365,
    };
    return periodMap[period] || 30;
  }

  private async getStockMovementTrends(startDate: Date) {
    // Implementation for stock movement trends
    return [];
  }

  private async getStockLevelTrends(startDate: Date) {
    // Implementation for stock level trends
    return [];
  }

  private async getTopMovingProducts(startDate: Date, limit: number) {
    // Implementation for top moving products
    return [];
  }

  private async getCategoryPerformance(startDate: Date) {
    // Implementation for category performance
    return [];
  }

  private calculateStatus(inventory: Inventory): string {
    if (inventory.currentStock === 0) return "out_of_stock";
    if (inventory.currentStock <= inventory.lowStockThreshold) return "low_stock";
    return "in_stock";
  }

  private generateCSVReport(inventories: Inventory[]): string {
    const headers = [
      "ID", "Product", "SKU", "Category", "Seller", "Current Stock",
      "Low Stock Threshold", "Reorder Point", "Status", "Cost Price", "Total Value"
    ];

    const rows = inventories.map(inv => [
      inv.id,
      inv.product.title,
      inv.sku || "",
      inv.product.category?.name || "",
      inv.product.seller.name,
      inv.currentStock,
      inv.lowStockThreshold,
      inv.reorderPoint,
      inv.status,
      inv.costPrice || 0,
      (inv.currentStock * (inv.costPrice || 0)).toFixed(2)
    ]);

    return [headers, ...rows].map(row => row.join(",")).join("\n");
  }

  private generateExcelReport(inventories: Inventory[]): Buffer {
    // Implementation for Excel report generation
    // You would use a library like 'exceljs' here
    throw new Error("Excel export not implemented yet");
  }

  private generatePDFReport(inventories: Inventory[]): Buffer {
    // Implementation for PDF report generation
    // You would use a library like 'pdfkit' here
    throw new Error("PDF export not implemented yet");
  }
}

export const adminInventoryService = new AdminInventoryService();
