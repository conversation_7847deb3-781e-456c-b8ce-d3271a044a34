import {
  CartItem,
  OrderItem,
  User,
  Order,
  Product,
  Inventory,
  StockMovementType,
} from "../entities";
import { AppDataSource } from "../config/db.config";
import {
  sendOrderConfirmationEmail,
  sendOrderStatusUpdateEmail,
} from "../utils/mailer";
import logger from "../utils/logger";
import inventoryService from "./inventory.service";

interface ShippingAddress {
  fullName: string;
  phone: string;
  street: string;
  city: string;
  county: string;
  postalCode: string;
  country: string;
}

interface PaymentInfo {
  method: string;
  status: "pending" | "completed" | "failed" | "refunded";
  transactionId?: string;
  paidAt?: Date;
}

interface CheckoutData {
  shippingAddress: ShippingAddress;
  paymentMethod: string;
  notes?: string;
}

export const createOrderFromCart = async (
  userId: string,
  checkoutData?: CheckoutData
) => {
  // Use database transaction to ensure data consistency
  return await AppDataSource.transaction(async (transactionalEntityManager) => {
    const userRepo = transactionalEntityManager.getRepository(User);
    const orderRepo = transactionalEntityManager.getRepository(Order);
    const cartRepo = transactionalEntityManager.getRepository(CartItem);
    const orderItemRepo = transactionalEntityManager.getRepository(OrderItem);
    const productRepo = transactionalEntityManager.getRepository(Product);

    const user = await userRepo.findOne({ where: { id: userId } });

    if (!user) {
      throw new Error("User not found");
    }
    const cartItems = await cartRepo.find({
      where: { user: { id: userId } },
      relations: ["product"],
    });
    if (cartItems.length === 0) {
      throw new Error("Cart is empty");
    }

    const order = new Order();
    order.buyer = user;
    order.total = 0;
    order.status = "pending";

    // Add shipping address if provided and save as user's default
    if (checkoutData?.shippingAddress) {
      order.shippingAddress = checkoutData.shippingAddress;

      // Save as user's default shipping address for future orders
      user.defaultShippingAddress = checkoutData.shippingAddress;
      await userRepo.save(user);
    }

    // Add notes if provided
    if (checkoutData?.notes) {
      order.notes = checkoutData.notes;
    }

    // Initialize payment info
    if (checkoutData?.paymentMethod) {
      order.paymentInfo = {
        method: checkoutData.paymentMethod,
        status: "pending",
        transactionId: `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      };
    }

    const orderItems: OrderItem[] = [];
    let total = 0;

    // First pass: Check inventory availability for all items
    for (const cartItem of cartItems) {
      const product = cartItem.product;

      // Get inventory for this product
      const inventory = await inventoryService.getInventoryByProduct(
        product.id
      );

      if (!inventory) {
        // Fallback to basic product quantity check if no inventory system
        if (product.quantity < cartItem.quantity) {
          throw new Error(
            `Product "${product.title}" is out of stock or exceeds quantity available`
          );
        }
      } else {
        // Use inventory system for stock checking
        if (inventory.availableStock < cartItem.quantity) {
          throw new Error(
            `Product "${product.title}" has insufficient stock. Available: ${inventory.availableStock}, Requested: ${cartItem.quantity}`
          );
        }
      }
    }

    // Second pass: Create order items and deduct stock
    for (const cartItem of cartItems) {
      const product = cartItem.product;

      const orderItem = new OrderItem();
      orderItem.product = product;
      orderItem.quantity = cartItem.quantity;
      orderItem.priceAtOrder = product.price; // Price when order was placed
      orderItem.unitPrice = product.price;
      orderItem.totalPrice = product.price * cartItem.quantity;
      orderItem.order = order;

      total += orderItem.totalPrice;
      orderItems.push(orderItem);

      // Deduct stock using inventory system if available
      try {
        const inventory = await inventoryService.getInventoryByProduct(
          product.id
        );

        if (inventory) {
          // Use inventory system for stock deduction
          await inventoryService.updateStock({
            inventoryId: inventory.id,
            quantity: cartItem.quantity,
            type: StockMovementType.SALE,
            reason: `Order sale - Order #${order.id}`,
            reference: order.id,
            performedBy: userId,
          });
        } else {
          // Fallback to basic product quantity deduction
          product.quantity -= cartItem.quantity;
          await productRepo.save(product);
        }
      } catch (stockError) {
        logger.error("Failed to deduct stock for product", {
          productId: product.id,
          productTitle: product.title,
          requestedQuantity: cartItem.quantity,
          error: stockError,
        });
        throw new Error(
          `Failed to process stock for product "${product.title}": ${stockError instanceof Error ? stockError.message : "Unknown error"}`
        );
      }
    }

    order.total = total;
    await orderRepo.save(order);
    await orderItemRepo.save(orderItems);

    // Clear the cart only after everything else succeeds
    await cartRepo.delete({ user: { id: userId } });

    logger.info("Order created successfully", {
      orderId: order.id,
      userId,
      total: order.total,
      itemCount: orderItems.length,
    });

    // Send order confirmation email with new template (fire-and-forget)
    sendOrderConfirmationEmail(user.email, user.name, {
      id: order.id,
      total: order.total,
      items: orderItems.map((item) => ({
        product: {
          name: item.product.title,
          price: item.priceAtOrder,
        },
        quantity: item.quantity,
        price: item.totalPrice,
      })),
      shippingAddress: order.shippingAddress,
      paymentInfo: order.paymentInfo,
    }).catch((emailError) => {
      // Log email error but don't fail the order
      logger.error("Failed to send order confirmation email", {
        orderId: order.id,
        userEmail: user.email,
        error:
          emailError instanceof Error ? emailError.message : "Unknown error",
      });
    });

    return order;
  }); // Close the transaction
};

export const fetchBuyerOrders = async (buyerId: string) => {
  return await AppDataSource.getRepository(Order).find({
    where: { buyer: { id: buyerId } },
    relations: ["items", "items.product", "items.product.seller"],
    order: { createdAt: "DESC" },
  });
};

export const getOrderById = async (orderId: string, userId: string) => {
  const orderRepo = AppDataSource.getRepository(Order);
  const order = await orderRepo.findOne({
    where: {
      id: orderId,
      buyer: { id: userId }, // Ensure user can only access their own orders
    },
    relations: ["items", "items.product", "items.product.seller", "buyer"],
  });

  if (!order) {
    throw new Error("Order not found");
  }

  return order;
};

// Get order by ID for sellers (checks if seller owns any items in the order)
export const getOrderByIdForSeller = async (
  orderId: string,
  sellerId: string
) => {
  const orderRepo = AppDataSource.getRepository(Order);
  const order = await orderRepo.findOne({
    where: {
      id: orderId,
      items: {
        product: {
          seller: { id: sellerId },
        },
      },
    },
    relations: ["items", "items.product", "items.product.seller", "buyer"],
  });

  if (!order) {
    throw new Error("Order not found or you don't have access to this order");
  }

  return order;
};

export const fetchSellerOrders = async (sellerId: string) => {
  return await AppDataSource.getRepository(OrderItem)
    .createQueryBuilder("orderItem")
    .leftJoinAndSelect("orderItem.order", "order")
    .leftJoinAndSelect("order.buyer", "buyer")
    .leftJoinAndSelect("orderItem.product", "product")
    .where("product.sellerId = :sellerId", { sellerId })
    .orderBy("order.createdAt", "DESC")
    .getMany();
};

// Map OrderItem status to email template status
const mapOrderItemStatusToEmailStatus = (orderItemStatus: string): string => {
  const statusMap: Record<string, string> = {
    PENDING: "pending",
    SHIPPED: "shipped",
    DELIVERED: "fulfilled",
  };

  return statusMap[orderItemStatus] || "pending";
};

export const updateOrderItemStatus = async (
  orderItemId: string,
  newStatus: string
) => {
  try {
    const repo = AppDataSource.getRepository(OrderItem);
    const orderItem = await repo.findOne({
      where: { id: orderItemId },
      relations: ["order", "order.buyer", "product"],
    });

    if (!orderItem) throw new Error("Order item not found");

    // Validate status against actual OrderItem enum values
    const validStatuses = ["PENDING", "SHIPPED", "DELIVERED"];

    // Normalize status to uppercase to match database enum
    const normalizedStatus = newStatus.toUpperCase();

    if (!validStatuses.includes(normalizedStatus)) {
      throw new Error(
        `Invalid status: ${newStatus}. Must be one of: ${validStatuses.join(", ")} (case-insensitive)`
      );
    }
    logger.info("Updating order item status", {
      orderItemId,
      oldStatus: orderItem.status,
      originalStatus: newStatus,
      normalizedStatus,
      orderId: orderItem.order.id,
    });

    //@ts-ignore
    orderItem.status = normalizedStatus;
    await repo.save(orderItem);

    const buyer = orderItem.order.buyer;

    // Generate tracking number for shipped orders
    const trackingNumber =
      normalizedStatus === "SHIPPED"
        ? `TRK${Math.floor(100000 + Math.random() * 900000)}`
        : undefined;

    // Map OrderItem status to email template status
    const emailStatus = mapOrderItemStatusToEmailStatus(normalizedStatus);

    logger.info("Sending order status update email", {
      buyerEmail: buyer.email,
      orderId: orderItem.order.id,
      orderItemStatus: normalizedStatus,
      emailStatus,
      trackingNumber,
    });

    // Send email asynchronously to avoid blocking the response
    sendOrderStatusUpdateEmail(
      buyer.email,
      buyer.name,
      orderItem.order.id,
      orderItem.product.title,
      //@ts-ignore
      emailStatus,
      trackingNumber
    ).catch((emailError) => {
      // Log email error but don't fail the status update
      logger.error("Failed to send order status update email", {
        orderItemId,
        orderId: orderItem.order.id,
        buyerEmail: buyer.email,
        error:
          emailError instanceof Error ? emailError.message : "Unknown error",
      });
    });

    logger.info("Order item status updated successfully", {
      orderItemId,
      newStatus: normalizedStatus,
      orderId: orderItem.order.id,
    });

    return orderItem;
  } catch (error) {
    logger.error("Failed to update order item status", {
      orderItemId,
      newStatus,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
    });
    throw error;
  }
};

// Mock payment processing function
export const processPayment = async (
  orderId: string,
  paymentMethod: string
): Promise<{ success: boolean; transactionId?: string; error?: string }> => {
  // Simulate payment processing delay
  await new Promise((resolve) => setTimeout(resolve, 2000));

  // Mock payment success/failure (90% success rate)
  const isSuccess = Math.random() > 0.1;

  const orderRepo = AppDataSource.getRepository(Order);
  const order = await orderRepo.findOne({ where: { id: orderId } });

  if (!order) {
    return { success: false, error: "Order not found" };
  }

  if (isSuccess) {
    // Update payment status to completed
    //@ts-ignore
    order.paymentInfo = {
      ...order.paymentInfo,
      status: "completed",
      paidAt: new Date(),
    };
    order.status = "paid";

    await orderRepo.save(order);

    logger.info("Payment processed successfully", {
      orderId,
      transactionId: order.paymentInfo?.transactionId,
    });

    return {
      success: true,
      transactionId: order.paymentInfo?.transactionId,
    };
  } else {
    // Update payment status to failed
    //@ts-ignore
    order.paymentInfo = {
      ...order.paymentInfo,
      status: "failed",
    };

    await orderRepo.save(order);

    logger.warn("Payment processing failed", { orderId });

    return {
      success: false,
      error: "Payment processing failed. Please try again.",
    };
  }
};

// Update order status with enhanced logic
export const updateOrderStatus = async (
  orderId: string,
  newStatus: "pending" | "paid" | "shipped" | "fulfilled" | "cancelled",
  trackingNumber?: string
) => {
  const orderRepo = AppDataSource.getRepository(Order);
  const order = await orderRepo.findOne({
    where: { id: orderId },
    relations: ["buyer", "items", "items.product", "items.product.seller"],
  });

  if (!order) throw new Error("Order not found");

  const oldStatus = order.status;
  order.status = newStatus;

  // Add tracking number if provided
  if (trackingNumber && newStatus === "shipped") {
    order.trackingNumber = trackingNumber;
  }

  // Auto-generate tracking number for shipped orders if not provided
  if (newStatus === "shipped" && !order.trackingNumber) {
    order.trackingNumber = `TRK${Math.floor(100000 + Math.random() * 900000)}`;
  }

  await orderRepo.save(order);

  // Send status update email to buyer (fire-and-forget to avoid blocking)
  sendOrderStatusUpdateEmail(
    order.buyer.email,
    order.buyer.name,
    order.id,
    `Order #${order.id.slice(-8).toUpperCase()}`,
    newStatus,
    order.trackingNumber
  ).catch((emailError) => {
    // Log email error but don't fail the status update
    logger.error("Failed to send order status update email", {
      orderId,
      buyerEmail: order.buyer.email,
      newStatus,
      error: emailError instanceof Error ? emailError.message : "Unknown error",
    });
  });

  logger.info("Order status updated", {
    orderId,
    oldStatus,
    newStatus,
    trackingNumber: order.trackingNumber,
  });

  return order;
};

// Get user's default shipping address
export const getUserDefaultShippingAddress = async (userId: string) => {
  const userRepo = AppDataSource.getRepository(User);
  const user = await userRepo.findOne({
    where: { id: userId },
    select: ["id", "defaultShippingAddress"],
  });

  return user?.defaultShippingAddress || null;
};

// Get all orders for admin
export const fetchAllOrdersForAdmin = async () => {
  return await AppDataSource.getRepository(Order).find({
    relations: ["items", "items.product", "items.product.seller", "buyer"],
    order: { createdAt: "DESC" },
  });
};
