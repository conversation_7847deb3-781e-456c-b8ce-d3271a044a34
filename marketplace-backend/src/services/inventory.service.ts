import { AppDataSource } from "../config/db.config";
import {
  Inventory,
  StockMovement,
  InventoryStatus,
  StockMovementType,
} from "../entities/Inventory.entity";
import { Product } from "../entities/Product.entity";
import { User } from "../entities/User.entity";
import { Repository } from "typeorm";

export interface CreateInventoryPayload {
  productId: string;
  currentStock: number;
  lowStockThreshold?: number;
  reorderPoint?: number;
  reorderQuantity?: number;
  sku?: string;
  location?: string;
  costPrice?: number;
  sellerId: string;
}

export interface UpdateStockPayload {
  inventoryId: string;
  quantity: number;
  type: StockMovementType;
  reason?: string;
  reference?: string;
  performedBy: string;
}

export interface InventoryFilters {
  sellerId?: string;
  status?: InventoryStatus;
  lowStock?: boolean;
  outOfStock?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

class InventoryService {
  private inventoryRepo: Repository<Inventory>;
  private stockMovementRepo: Repository<StockMovement>;
  private productRepo: Repository<Product>;
  private userRepo: Repository<User>;

  constructor() {
    this.inventoryRepo = AppDataSource.getRepository(Inventory);
    this.stockMovementRepo = AppDataSource.getRepository(StockMovement);
    this.productRepo = AppDataSource.getRepository(Product);
    this.userRepo = AppDataSource.getRepository(User);
  }

  async createInventory(payload: CreateInventoryPayload): Promise<Inventory> {
    const product = await this.productRepo.findOne({
      where: { id: payload.productId },
    });

    if (!product) {
      throw new Error("Product not found");
    }

    const seller = await this.userRepo.findOne({
      where: { id: payload.sellerId },
    });

    if (!seller) {
      throw new Error("Seller not found");
    }

    // Check if inventory already exists for this product
    const existingInventory = await this.inventoryRepo.findOne({
      where: { product: { id: payload.productId } },
    });

    if (existingInventory) {
      throw new Error("Inventory already exists for this product");
    }

    const inventory = this.inventoryRepo.create({
      product,
      seller,
      currentStock: payload.currentStock,
      lowStockThreshold: payload.lowStockThreshold || 5,
      reorderPoint: payload.reorderPoint || 0,
      reorderQuantity: payload.reorderQuantity || 0,
      sku: payload.sku,
      location: payload.location,
      costPrice: payload.costPrice,
      status: this.calculateStatus(
        payload.currentStock,
        payload.lowStockThreshold || 5
      ),
    });

    const savedInventory = await this.inventoryRepo.save(inventory);

    // Create initial stock movement
    await this.createStockMovement({
      inventoryId: savedInventory.id,
      quantity: payload.currentStock,
      type: StockMovementType.PURCHASE,
      reason: "Initial stock",
      performedBy: payload.sellerId,
    });

    return savedInventory;
  }

  async updateStock(payload: UpdateStockPayload): Promise<Inventory> {
    const inventory = await this.inventoryRepo.findOne({
      where: { id: payload.inventoryId },
      relations: ["product", "seller"],
    });

    if (!inventory) {
      throw new Error("Inventory not found");
    }

    const previousStock = inventory.currentStock;
    let newStock: number;

    // Calculate new stock based on movement type
    switch (payload.type) {
      case StockMovementType.PURCHASE:
      case StockMovementType.RESTOCK:
      case StockMovementType.RETURN:
        newStock = previousStock + payload.quantity;
        break;
      case StockMovementType.SALE:
      case StockMovementType.DAMAGE:
        newStock = Math.max(0, previousStock - payload.quantity);
        break;
      case StockMovementType.ADJUSTMENT:
        newStock = payload.quantity; // Direct adjustment
        break;
      default:
        throw new Error("Invalid stock movement type");
    }

    // Update inventory
    inventory.currentStock = newStock;
    inventory.status = this.calculateStatus(
      newStock,
      inventory.lowStockThreshold
    );

    const updatedInventory = await this.inventoryRepo.save(inventory);

    // Create stock movement record
    await this.createStockMovement({
      ...payload,
      quantity: Math.abs(payload.quantity),
    });

    return updatedInventory;
  }

  async getInventoryByProduct(productId: string): Promise<Inventory | null> {
    return this.inventoryRepo.findOne({
      where: { product: { id: productId } },
      relations: ["product", "seller", "stockMovements"],
    });
  }

  async getSellerInventory(
    sellerId: string,
    filters: InventoryFilters = {}
  ): Promise<{
    inventories: Inventory[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const page = filters.page || 1;
    const limit = filters.limit || 20;
    const skip = (page - 1) * limit;

    const queryBuilder = this.inventoryRepo
      .createQueryBuilder("inventory")
      .leftJoinAndSelect("inventory.product", "product")
      .leftJoinAndSelect("inventory.seller", "seller")
      .where("seller.id = :sellerId", { sellerId });

    // Apply filters
    if (filters.status) {
      queryBuilder.andWhere("inventory.status = :status", {
        status: filters.status,
      });
    }

    if (filters.lowStock) {
      queryBuilder.andWhere(
        "inventory.currentStock <= inventory.lowStockThreshold"
      );
    }

    if (filters.outOfStock) {
      queryBuilder.andWhere("inventory.currentStock = 0");
    }

    if (filters.search) {
      queryBuilder.andWhere(
        "(product.title ILIKE :search OR inventory.sku ILIKE :search)",
        { search: `%${filters.search}%` }
      );
    }

    const [inventories, total] = await queryBuilder
      .orderBy("inventory.updatedAt", "DESC")
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return {
      inventories,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getLowStockItems(sellerId?: string): Promise<Inventory[]> {
    const queryBuilder = this.inventoryRepo
      .createQueryBuilder("inventory")
      .leftJoinAndSelect("inventory.product", "product")
      .leftJoinAndSelect("inventory.seller", "seller")
      .where("inventory.currentStock <= inventory.lowStockThreshold");

    if (sellerId) {
      queryBuilder.andWhere("seller.id = :sellerId", { sellerId });
    }

    return queryBuilder.orderBy("inventory.currentStock", "ASC").getMany();
  }

  async getInventoryStats(sellerId?: string): Promise<{
    totalProducts: number;
    inStock: number;
    lowStock: number;
    outOfStock: number;
    totalValue: number;
  }> {
    const queryBuilder = this.inventoryRepo
      .createQueryBuilder("inventory")
      .leftJoinAndSelect("inventory.product", "product");

    if (sellerId) {
      queryBuilder
        .leftJoinAndSelect("inventory.seller", "seller")
        .where("seller.id = :sellerId", { sellerId });
    }

    const inventories = await queryBuilder.getMany();

    const stats = {
      totalProducts: inventories.length,
      inStock: inventories.filter(
        (inv) => inv.status === InventoryStatus.IN_STOCK
      ).length,
      lowStock: inventories.filter(
        (inv) => inv.status === InventoryStatus.LOW_STOCK
      ).length,
      outOfStock: inventories.filter(
        (inv) => inv.status === InventoryStatus.OUT_OF_STOCK
      ).length,
      totalValue: inventories.reduce((sum, inv) => {
        return sum + inv.currentStock * inv.product.price;
      }, 0),
    };

    return stats;
  }

  private async createStockMovement(
    payload: UpdateStockPayload
  ): Promise<StockMovement> {
    const inventory = await this.inventoryRepo.findOne({
      where: { id: payload.inventoryId },
    });

    const user = await this.userRepo.findOne({
      where: { id: payload.performedBy },
    });

    if (!inventory || !user) {
      throw new Error("Inventory or user not found");
    }

    const movement = this.stockMovementRepo.create({
      inventory,
      type: payload.type,
      quantity: payload.quantity,
      previousStock: inventory.currentStock,
      newStock: inventory.currentStock, // Will be updated after stock calculation
      reason: payload.reason,
      reference: payload.reference,
      performedBy: user,
    });

    return this.stockMovementRepo.save(movement);
  }

  private calculateStatus(
    currentStock: number,
    lowStockThreshold: number
  ): InventoryStatus {
    if (currentStock === 0) {
      return InventoryStatus.OUT_OF_STOCK;
    } else if (currentStock <= lowStockThreshold) {
      return InventoryStatus.LOW_STOCK;
    } else {
      return InventoryStatus.IN_STOCK;
    }
  }

  async updateInventoryDetails(params: {
    inventoryId: string;
    newQuantity?: number;
    lowStockThreshold?: number;
    reorderPoint?: number;
    reorderQuantity?: number;
    sku?: string;
    location?: string;
    costPrice?: number;
    reason?: string;
    performedBy: string;
  }): Promise<Inventory> {
    const inventory = await this.inventoryRepo.findOne({
      where: { id: params.inventoryId },
      relations: ["product", "seller"],
    });

    if (!inventory) {
      throw new Error("Inventory not found");
    }

    const oldQuantity = inventory.currentStock;
    const updates: Partial<Inventory> = {};

    // Update fields if provided
    if (params.newQuantity !== undefined) {
      updates.currentStock = params.newQuantity;
    }
    if (params.lowStockThreshold !== undefined) {
      updates.lowStockThreshold = params.lowStockThreshold;
    }
    if (params.reorderPoint !== undefined) {
      updates.reorderPoint = params.reorderPoint;
    }
    if (params.reorderQuantity !== undefined) {
      updates.reorderQuantity = params.reorderQuantity;
    }
    if (params.sku !== undefined) {
      updates.sku = params.sku;
    }
    if (params.location !== undefined) {
      updates.location = params.location;
    }
    if (params.costPrice !== undefined) {
      updates.costPrice = params.costPrice;
    }

    // Update status based on new quantity and threshold
    if (params.newQuantity !== undefined) {
      const threshold = params.lowStockThreshold || inventory.lowStockThreshold;
      updates.status = this.calculateStatus(params.newQuantity, threshold);
    }

    // Apply updates
    Object.assign(inventory, updates);
    await this.inventoryRepo.save(inventory);

    // Create stock movement record if quantity changed
    if (
      params.newQuantity !== undefined &&
      params.newQuantity !== oldQuantity
    ) {
      const user = await AppDataSource.getRepository(User).findOne({
        where: { id: params.performedBy },
      });

      if (user) {
        const movement = this.stockMovementRepo.create({
          inventory,
          type: StockMovementType.ADJUSTMENT,
          quantity: Math.abs(params.newQuantity - oldQuantity),
          previousStock: oldQuantity,
          newStock: params.newQuantity,
          reason: params.reason || "Inventory details updated",
          performedBy: user,
        });

        await this.stockMovementRepo.save(movement);
      }
    }

    return inventory;
  }
}

export default new InventoryService();
