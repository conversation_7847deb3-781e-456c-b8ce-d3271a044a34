import { Repository } from "typeorm";
import { AppDataSource } from "../config/db.config";
import {
  DeadLetterQueue,
  EmailType,
  FailureReason,
  DLQStatus,
} from "../entities/DeadLetterQueue.entity";
import logger from "../utils/logger";
import { sendEmail } from "../utils/mailer";

/**
 * Dead Letter Queue Service
 *
 * Manages failed email attempts that require manual intervention
 * or special handling after automatic retry attempts are exhausted.
 */

interface CreateDLQEntryParams {
  emailType: EmailType;
  recipientEmail: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  emailData?: Record<string, unknown>;
  failureReason: FailureReason;
  errorMessage: string;
  stackTrace?: string;
  userId?: string;
  orderId?: string;
  priority?: number;
  maxRetryAttempts?: number;
  metadata?: Record<string, unknown>;
}

interface DLQStats {
  totalEntries: number;
  pendingEntries: number;
  processingEntries: number;
  resolvedEntries: number;
  failedPermanentlyEntries: number;
  cancelledEntries: number;
  byEmailType: Record<EmailType, number>;
  byFailureReason: Record<FailureReason, number>;
  avgAgeHours: number;
  oldestEntryHours: number;
}

class DeadLetterQueueService {
  private dlqRepository: Repository<DeadLetterQueue>;
  private readonly PROCESSING_NODE = `${process.env.NODE_ENV || "development"}-${process.pid}`;
  private readonly STALE_THRESHOLD_MINUTES = 30;
  private readonly BATCH_SIZE = 10;

  constructor() {
    this.dlqRepository = AppDataSource.getRepository(DeadLetterQueue);
  }

  /**
   * Add a failed email to the dead letter queue
   */
  async addToQueue(params: CreateDLQEntryParams): Promise<DeadLetterQueue> {
    try {
      const dlqEntry = this.dlqRepository.create({
        emailType: params.emailType,
        recipientEmail: params.recipientEmail,
        subject: params.subject,
        htmlContent: params.htmlContent,
        textContent: params.textContent,
        emailData: params.emailData,
        failureReason: params.failureReason,
        errorMessage: params.errorMessage,
        stackTrace: params.stackTrace,
        userId: params.userId,
        orderId: params.orderId,
        priority: params.priority || 2, // Default to medium priority
        maxRetryAttempts: params.maxRetryAttempts || 3,
        metadata: params.metadata,
        status: "pending",
      });

      const savedEntry = await this.dlqRepository.save(dlqEntry);

      logger.warn("Email added to dead letter queue", {
        dlqId: savedEntry.id,
        emailType: params.emailType,
        recipientEmail: params.recipientEmail,
        failureReason: params.failureReason,
        errorMessage: params.errorMessage,
        priority: savedEntry.priority,
      });

      return savedEntry;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      logger.error("Failed to add email to dead letter queue", {
        error: errorMessage,
        emailType: params.emailType,
        recipientEmail: params.recipientEmail,
      });
      throw error;
    }
  }

  /**
   * Process pending entries that are ready for retry
   */
  async processRetryableEntries(): Promise<{
    processed: number;
    successful: number;
    failed: number;
  }> {
    try {
      // Find entries ready for retry
      const retryableEntries = await this.dlqRepository
        .createQueryBuilder("dlq")
        .where("dlq.status = :status", { status: "pending" })
        .andWhere("dlq.retryAttempts < dlq.maxRetryAttempts")
        .andWhere("(dlq.nextRetryAt IS NULL OR dlq.nextRetryAt <= :now)", {
          now: new Date(),
        })
        .orderBy("dlq.priority", "ASC")
        .addOrderBy("dlq.createdAt", "ASC")
        .limit(this.BATCH_SIZE)
        .getMany();

      let successful = 0;
      let failed = 0;

      for (const entry of retryableEntries) {
        try {
          // Mark as processing
          entry.markAsProcessing(this.PROCESSING_NODE);
          await this.dlqRepository.save(entry);

          // Attempt to send email
          await sendEmail(
            entry.recipientEmail,
            entry.subject,
            entry.htmlContent,
            entry.textContent
          );

          // Success - mark as resolved
          entry.markAsResolved("system", "Automatically resolved via retry");
          await this.dlqRepository.save(entry);
          successful++;

          logger.info("DLQ entry successfully retried", {
            dlqId: entry.id,
            emailType: entry.emailType,
            retryAttempt: entry.retryAttempts + 1,
          });
        } catch (error) {
          // Failed - increment retry attempt
          entry.incrementRetryAttempt();
          await this.dlqRepository.save(entry);
          failed++;

          const errorMessage =
            error instanceof Error ? error.message : "Unknown error";
          logger.warn("DLQ entry retry failed", {
            dlqId: entry.id,
            emailType: entry.emailType,
            retryAttempt: entry.retryAttempts,
            error: errorMessage,
            willRetryAgain: entry.canRetry(),
          });
        }
      }

      if (retryableEntries.length > 0) {
        logger.info("DLQ processing completed", {
          processed: retryableEntries.length,
          successful,
          failed,
        });
      }

      return {
        processed: retryableEntries.length,
        successful,
        failed,
      };
    } catch (error: any) {
      logger.error("Failed to process DLQ entries", { error: error.message });
      throw error;
    }
  }

  /**
   * Get DLQ statistics
   */
  async getStats(): Promise<DLQStats> {
    try {
      const [
        totalEntries,
        pendingEntries,
        processingEntries,
        resolvedEntries,
        failedPermanentlyEntries,
        cancelledEntries,
        allEntries,
      ] = await Promise.all([
        this.dlqRepository.count(),
        this.dlqRepository.count({ where: { status: "pending" } }),
        this.dlqRepository.count({ where: { status: "processing" } }),
        this.dlqRepository.count({ where: { status: "resolved" } }),
        this.dlqRepository.count({ where: { status: "failed_permanently" } }),
        this.dlqRepository.count({ where: { status: "cancelled" } }),
        this.dlqRepository.find({
          select: ["emailType", "failureReason", "createdAt"],
        }),
      ]);

      // Calculate aggregations
      const byEmailType: Record<string, number> = {};
      const byFailureReason: Record<string, number> = {};
      let totalAgeHours = 0;
      let oldestEntryHours = 0;

      const now = Date.now();
      allEntries.forEach((entry) => {
        byEmailType[entry.emailType] = (byEmailType[entry.emailType] || 0) + 1;
        byFailureReason[entry.failureReason] =
          (byFailureReason[entry.failureReason] || 0) + 1;

        const ageHours = (now - entry.createdAt.getTime()) / (1000 * 60 * 60);
        totalAgeHours += ageHours;
        oldestEntryHours = Math.max(oldestEntryHours, ageHours);
      });

      return {
        totalEntries,
        pendingEntries,
        processingEntries,
        resolvedEntries,
        failedPermanentlyEntries,
        cancelledEntries,
        byEmailType: byEmailType as Record<EmailType, number>,
        byFailureReason: byFailureReason as Record<FailureReason, number>,
        avgAgeHours:
          allEntries.length > 0 ? totalAgeHours / allEntries.length : 0,
        oldestEntryHours,
      };
    } catch (error: any) {
      logger.error("Failed to get DLQ stats", { error: error.message });
      throw error;
    }
  }

  /**
   * Get entries with pagination and filtering
   */
  async getEntries(
    options: {
      page?: number;
      limit?: number;
      status?: DLQStatus;
      emailType?: EmailType;
      failureReason?: FailureReason;
      priority?: number;
      sortBy?: "createdAt" | "priority" | "retryAttempts";
      sortOrder?: "ASC" | "DESC";
    } = {}
  ): Promise<{
    entries: DeadLetterQueue[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      page = 1,
      limit = 20,
      status,
      emailType,
      failureReason,
      priority,
      sortBy = "createdAt",
      sortOrder = "DESC",
    } = options;

    try {
      const queryBuilder = this.dlqRepository.createQueryBuilder("dlq");

      // Apply filters
      if (status) queryBuilder.andWhere("dlq.status = :status", { status });
      if (emailType)
        queryBuilder.andWhere("dlq.emailType = :emailType", { emailType });
      if (failureReason)
        queryBuilder.andWhere("dlq.failureReason = :failureReason", {
          failureReason,
        });
      if (priority)
        queryBuilder.andWhere("dlq.priority = :priority", { priority });

      // Apply sorting
      queryBuilder.orderBy(`dlq.${sortBy}`, sortOrder);

      // Apply pagination
      const offset = (page - 1) * limit;
      queryBuilder.skip(offset).take(limit);

      const [entries, total] = await queryBuilder.getManyAndCount();

      return {
        entries,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error: any) {
      logger.error("Failed to get DLQ entries", { error: error.message });
      throw error;
    }
  }

  /**
   * Manually retry a specific DLQ entry
   */
  async retryEntry(id: string, adminUserId?: string): Promise<boolean> {
    try {
      const entry = await this.dlqRepository.findOne({ where: { id } });
      if (!entry) {
        throw new Error(`DLQ entry not found: ${id}`);
      }

      if (!entry.canRetry()) {
        throw new Error(`DLQ entry cannot be retried: ${id}`);
      }

      // Mark as processing
      entry.markAsProcessing(this.PROCESSING_NODE);
      await this.dlqRepository.save(entry);

      try {
        // Attempt to send email
        await sendEmail(
          entry.recipientEmail,
          entry.subject,
          entry.htmlContent,
          entry.textContent
        );

        // Success - mark as resolved
        entry.markAsResolved(
          adminUserId || "manual-retry",
          "Manually retried successfully"
        );
        await this.dlqRepository.save(entry);

        logger.info("DLQ entry manually retried successfully", {
          dlqId: entry.id,
          adminUserId,
          emailType: entry.emailType,
        });

        return true;
      } catch (error: any) {
        // Failed - increment retry attempt
        entry.incrementRetryAttempt();
        await this.dlqRepository.save(entry);

        logger.warn("DLQ entry manual retry failed", {
          dlqId: entry.id,
          adminUserId,
          error: error.message,
        });

        return false;
      }
    } catch (error: any) {
      logger.error("Failed to manually retry DLQ entry", {
        dlqId: id,
        adminUserId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Mark entry as resolved without sending
   */
  async resolveEntry(
    id: string,
    adminUserId: string,
    notes?: string
  ): Promise<void> {
    try {
      const entry = await this.dlqRepository.findOne({ where: { id } });
      if (!entry) {
        throw new Error(`DLQ entry not found: ${id}`);
      }

      entry.markAsResolved(adminUserId, notes);
      await this.dlqRepository.save(entry);

      logger.info("DLQ entry marked as resolved", {
        dlqId: entry.id,
        adminUserId,
        notes,
      });
    } catch (error: any) {
      logger.error("Failed to resolve DLQ entry", {
        dlqId: id,
        adminUserId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Cancel entry
   */
  async cancelEntry(
    id: string,
    adminUserId: string,
    reason?: string
  ): Promise<void> {
    try {
      const entry = await this.dlqRepository.findOne({ where: { id } });
      if (!entry) {
        throw new Error(`DLQ entry not found: ${id}`);
      }

      entry.markAsCancelled(adminUserId, reason);
      await this.dlqRepository.save(entry);

      logger.info("DLQ entry cancelled", {
        dlqId: entry.id,
        adminUserId,
        reason,
      });
    } catch (error: any) {
      logger.error("Failed to cancel DLQ entry", {
        dlqId: id,
        adminUserId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Clean up old resolved/cancelled entries
   */
  async cleanup(olderThanDays: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date(
        Date.now() - olderThanDays * 24 * 60 * 60 * 1000
      );

      const result = await this.dlqRepository
        .createQueryBuilder()
        .delete()
        .where("status IN (:...statuses)", {
          statuses: ["resolved", "cancelled"],
        })
        .andWhere("updatedAt < :cutoffDate", { cutoffDate })
        .execute();

      const deletedCount = result.affected || 0;

      if (deletedCount > 0) {
        logger.info("DLQ cleanup completed", {
          deletedCount,
          olderThanDays,
        });
      }

      return deletedCount;
    } catch (error: any) {
      logger.error("Failed to cleanup DLQ entries", { error: error.message });
      throw error;
    }
  }

  /**
   * Reset stale processing entries
   */
  async resetStaleEntries(): Promise<number> {
    try {
      const staleThreshold = new Date(
        Date.now() - this.STALE_THRESHOLD_MINUTES * 60 * 1000
      );

      const result = await this.dlqRepository
        .createQueryBuilder()
        .update()
        .set({
          status: "pending",
          processingNode: null,
        })
        .where("status = :status", { status: "processing" })
        .andWhere("updatedAt < :staleThreshold", { staleThreshold })
        .execute();

      const resetCount = result.affected || 0;

      if (resetCount > 0) {
        logger.warn("Reset stale DLQ processing entries", {
          resetCount,
          staleThresholdMinutes: this.STALE_THRESHOLD_MINUTES,
        });
      }

      return resetCount;
    } catch (error: any) {
      logger.error("Failed to reset stale DLQ entries", {
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Get entry by ID
   */
  async getEntry(id: string): Promise<DeadLetterQueue | null> {
    try {
      return await this.dlqRepository.findOne({ where: { id } });
    } catch (error: any) {
      logger.error("Failed to get DLQ entry", {
        dlqId: id,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<{
    status: "healthy" | "warning" | "critical";
    message: string;
    stats: {
      pendingEntries: number;
      oldestPendingHours: number;
      failedPermanentlyToday: number;
      processingEntries: number;
    };
  }> {
    try {
      const stats = await this.getStats();

      // Get oldest pending entry
      const oldestPending = await this.dlqRepository.findOne({
        where: { status: "pending" },
        order: { createdAt: "ASC" },
      });

      const oldestPendingHours = oldestPending
        ? (Date.now() - oldestPending.createdAt.getTime()) / (1000 * 60 * 60)
        : 0;

      // Get failed permanently today
      const todayStart = new Date();
      todayStart.setHours(0, 0, 0, 0);

      const failedPermanentlyToday = await this.dlqRepository.count({
        where: {
          status: "failed_permanently",
          updatedAt: { $gte: todayStart } as any,
        },
      });

      const healthStats = {
        pendingEntries: stats.pendingEntries,
        oldestPendingHours,
        failedPermanentlyToday,
        processingEntries: stats.processingEntries,
      };

      // Determine health status
      if (
        stats.pendingEntries > 100 ||
        oldestPendingHours > 24 ||
        failedPermanentlyToday > 50
      ) {
        return {
          status: "critical",
          message: "Critical issues in dead letter queue",
          stats: healthStats,
        };
      }

      if (
        stats.pendingEntries > 20 ||
        oldestPendingHours > 6 ||
        failedPermanentlyToday > 10
      ) {
        return {
          status: "warning",
          message: "Dead letter queue requires attention",
          stats: healthStats,
        };
      }

      return {
        status: "healthy",
        message: "Dead letter queue operating normally",
        stats: healthStats,
      };
    } catch (error: any) {
      logger.error("Failed to get DLQ health status", { error: error.message });
      return {
        status: "critical",
        message: "Failed to check DLQ health status",
        stats: {
          pendingEntries: 0,
          oldestPendingHours: 0,
          failedPermanentlyToday: 0,
          processingEntries: 0,
        },
      };
    }
  }
}

// Export singleton instance
export const deadLetterQueueService = new DeadLetterQueueService();
export default deadLetterQueueService;
