import { AppDataSource } from "../config/db.config";
import { OrderItem } from "../entities";
import { updateOrderItemStatus } from "../services/order.service";

async function testStatusUpdate() {
  try {
    await AppDataSource.initialize();
    console.log("Database connected");

    const orderItemRepo = AppDataSource.getRepository(OrderItem);

    // Find an order item to test with
    const orderItem = await orderItemRepo.findOne({
      relations: ["order", "order.buyer", "product"],
    });

    if (!orderItem) {
      console.log("No order items found. Please create an order first.");
      process.exit(1);
    }

    console.log(`Found order item: ${orderItem.id}`);
    console.log(`Current status: ${orderItem.status}`);

    // Test case-insensitive status updates
    const testStatuses = [
      "SHIPPED",    // Uppercase
      "shipped",    // Lowercase
      "Shipped",    // Mixed case
      "FULFILLED",  // Uppercase
      "fulfilled",  // Lowercase
    ];

    for (const status of testStatuses) {
      try {
        console.log(`\nTesting status: "${status}"`);
        
        const updated = await updateOrderItemStatus(orderItem.id, status);
        
        console.log(`✅ Success! Status updated to: ${updated.status}`);
        
        // Wait a bit between updates
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error: any) {
        console.log(`❌ Failed for status "${status}": ${error.message}`);
      }
    }

    // Test invalid status
    try {
      console.log(`\nTesting invalid status: "INVALID"`);
      await updateOrderItemStatus(orderItem.id, "INVALID");
      console.log(`❌ Should have failed for invalid status`);
    } catch (error: any) {
      console.log(`✅ Correctly rejected invalid status: ${error.message}`);
    }

    console.log("\n🎉 All tests completed!");
    process.exit(0);
    
  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

testStatusUpdate();
