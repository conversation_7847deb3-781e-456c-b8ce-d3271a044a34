import { AppDataSource } from "../config/db.config";
import { User, Conversation, Message } from "../entities";
import bcrypt from "bcrypt";

async function createTestChat() {
  try {
    await AppDataSource.initialize();
    console.log("Database connected");

    const userRepo = AppDataSource.getRepository(User);
    const conversationRepo = AppDataSource.getRepository(Conversation);
    const messageRepo = AppDataSource.getRepository(Message);

    // Create or find the two test users
    const testUsers = [
      {
        name: "Test Buyer",
        email: "<EMAIL>",
        password: "testPass",
        role: "buyer" as const,
      },
      {
        name: "<PERSON>",
        email: "<EMAIL>", 
        password: "testPass",
        role: "seller" as const,
      },
    ];

    const createdUsers: User[] = [];

    for (const userData of testUsers) {
      let user = await userRepo.findOne({ where: { email: userData.email } });
      
      if (!user) {
        const hashedPassword = await bcrypt.hash(userData.password, 10);
        user = userRepo.create({
          ...userData,
          password: hashedPassword,
        });
        await userRepo.save(user);
        console.log(`✅ Created user: ${userData.name} (${userData.email})`);
      } else {
        console.log(`⏭️  User already exists: ${userData.name} (${userData.email})`);
      }
      
      createdUsers.push(user);
    }

    const [buyer, seller] = createdUsers;

    // Check if conversation already exists
    let conversation = await conversationRepo.findOne({
      where: [
        {
          participantOne: { id: buyer.id },
          participantTwo: { id: seller.id },
        },
        {
          participantOne: { id: seller.id },
          participantTwo: { id: buyer.id },
        },
      ],
    });

    if (!conversation) {
      conversation = conversationRepo.create({
        participantOne: buyer,
        participantTwo: seller,
      });
      await conversationRepo.save(conversation);
      console.log(`✅ Created conversation between ${buyer.name} and ${seller.name}`);

      // Add some test messages
      const messages = [
        {
          sender: buyer,
          content: "Hi! I'm interested in your wireless headphones. Are they still available?",
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        },
        {
          sender: seller,
          content: "Hello! Yes, they are still available. They're in excellent condition and come with the original box.",
          createdAt: new Date(Date.now() - 1.5 * 60 * 60 * 1000), // 1.5 hours ago
        },
        {
          sender: buyer,
          content: "Great! What's the battery life like?",
          createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
        },
        {
          sender: seller,
          content: "The battery lasts about 30 hours with noise cancellation off, and about 20 hours with it on. Perfect for long trips!",
          createdAt: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
        },
        {
          sender: buyer,
          content: "Sounds perfect! I'll take them. Can you ship to Nairobi?",
          createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        },
        {
          sender: seller,
          content: "Absolutely! I can ship to Nairobi. Shipping usually takes 2-3 business days. Would you like me to send you the payment details?",
          createdAt: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
        },
      ];

      for (const msgData of messages) {
        const message = messageRepo.create({
          conversation,
          sender: msgData.sender,
          content: msgData.content,
          createdAt: msgData.createdAt,
        });
        await messageRepo.save(message);
      }

      // Update conversation with last message preview
      const lastMessage = messages[messages.length - 1];
      conversation.lastMessagePreview = lastMessage.content.length > 50 
        ? lastMessage.content.substring(0, 50) + "..."
        : lastMessage.content;
      conversation.updatedAt = lastMessage.createdAt;
      await conversationRepo.save(conversation);

      console.log(`✅ Added ${messages.length} messages to conversation`);
    } else {
      console.log(`⏭️  Conversation already exists between ${buyer.name} and ${seller.name}`);
    }

    console.log("\n🎉 Test chat data created successfully!");
    console.log("\n📋 Test Credentials:");
    console.log("Buyer: <EMAIL> / testPass");
    console.log("Seller: <EMAIL> / testPass");
    console.log("\n💬 Test Flow:");
    console.log("1. <NAME_EMAIL>");
    console.log("2. Go to any product page");
    console.log("3. Click 'Contact Seller' button");
    console.log("4. Should start conversation with seller");
    
    process.exit(0);
  } catch (error) {
    console.error("❌ Error creating test chat:", error);
    process.exit(1);
  }
}

createTestChat();
