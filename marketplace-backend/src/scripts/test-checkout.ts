import { AppDataSource } from "../config/db.config";
import { User, Product, CartItem } from "../entities";
import { createOrderFromCart } from "../services/order.service";

async function testCheckout() {
  try {
    await AppDataSource.initialize();
    console.log("Database connected");

    const userRepo = AppDataSource.getRepository(User);
    const productRepo = AppDataSource.getRepository(Product);
    const cartRepo = AppDataSource.getRepository(CartItem);

    // Find a test user (buyer)
    const buyer = await userRepo.findOne({
      where: { role: "buyer" },
    });

    if (!buyer) {
      console.log("No buyer found. Please create a buyer user first.");
      process.exit(1);
    }

    console.log(`Found buyer: ${buyer.name} (${buyer.email})`);

    // Find a product
    const product = await productRepo.findOne({
      where: {},
      relations: ["seller"],
    });

    if (!product) {
      console.log("No products found. Please create a product first.");
      process.exit(1);
    }

    console.log(`Found product: ${product.title} - KES ${product.price}`);

    // Clear existing cart
    await cartRepo.delete({ user: { id: buyer.id } });

    // Add item to cart
    const cartItem = cartRepo.create({
      user: buyer,
      product,
      quantity: 1,
    });

    await cartRepo.save(cartItem);
    console.log("Added item to cart");

    // Test checkout
    const checkoutData = {
      shippingAddress: {
        fullName: "Test User",
        phone: "+254700000000",
        street: "123 Test Street",
        city: "Nairobi",
        county: "Nairobi",
        postalCode: "00100",
        country: "Kenya",
      },
      paymentMethod: "mpesa",
      notes: "Test order",
    };

    console.log("Starting checkout...");
    const order = await createOrderFromCart(buyer.id, checkoutData);

    console.log("✅ Checkout successful!");
    console.log(`Order ID: ${order.id}`);
    console.log(`Total: KES ${order.total}`);
    console.log(`Status: ${order.status}`);

    // Verify cart is cleared
    const remainingCartItems = await cartRepo.find({
      where: { user: { id: buyer.id } },
    });

    console.log(`Cart items remaining: ${remainingCartItems.length}`);

    process.exit(0);
  } catch (error) {
    console.error("❌ Checkout test failed:", error);
    process.exit(1);
  }
}

testCheckout();
