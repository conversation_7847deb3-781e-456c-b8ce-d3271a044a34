import { AppDataSource } from "../config/db.config";
import { timeoutAnalyzer } from "../utils/timeoutAnalyzer";
import { getPerformanceTracker } from "../middlewares/performance.middleware";
import { emailRetryService } from "../utils/emailRetryService";
import { emailMetrics } from "../utils/emailMetrics";
import logger from "../utils/logger";

/**
 * Test script for timeout monitoring and performance tracking
 *
 * This script validates that all monitoring components are working correctly
 * and can detect various timeout and performance scenarios.
 */

async function testTimeoutMonitoring() {
  try {
    await AppDataSource.initialize();
    console.log("✅ Database connected successfully");

    console.log("\n🧪 Testing Timeout Monitoring System...\n");

    // Test 1: Basic timeout recording
    console.log("1. Testing basic timeout recording...");
    timeoutAnalyzer.recordTimeout("/api/orders/123/status", "PATCH", 15000);
    timeoutAnalyzer.recordTimeout("/api/orders/456/status", "PATCH", 12000);
    timeoutAnalyzer.recordTimeout("/api/orders/789/status", "PATCH", 18000);
    console.log("   ✅ Recorded 3 timeouts");

    // Test 2: Timeout pattern detection
    console.log("\n2. Testing timeout pattern detection...");
    const stats = timeoutAnalyzer.getStats();
    console.log("   📊 Timeout Stats:", {
      totalTimeouts: stats.totalTimeouts,
      recentTimeouts: stats.recentTimeouts,
      avgDuration: stats.avgDuration,
    });

    // Test 3: Performance tracking simulation
    console.log("\n3. Testing performance tracking...");
    const performanceTracker = getPerformanceTracker();

    // Simulate some performance metrics
    const mockMetrics = [
      {
        method: "GET",
        url: "/api/products",
        statusCode: 200,
        duration: 150,
        userAgent: "test",
        ip: "127.0.0.1",
        timestamp: Date.now(),
      },
      {
        method: "POST",
        url: "/api/orders",
        statusCode: 201,
        duration: 800,
        userAgent: "test",
        ip: "127.0.0.1",
        timestamp: Date.now(),
      },
      {
        method: "PATCH",
        url: "/api/orders/123/status",
        statusCode: 200,
        duration: 6000,
        userAgent: "test",
        ip: "127.0.0.1",
        timestamp: Date.now(),
      }, // Slow request
      {
        method: "GET",
        url: "/api/analytics",
        statusCode: 200,
        duration: 12000,
        userAgent: "test",
        ip: "127.0.0.1",
        timestamp: Date.now(),
      }, // Critical request
    ];

    mockMetrics.forEach((metrics) => {
      performanceTracker.recordMetrics(metrics);
    });

    const perfStats = performanceTracker.getStats();
    console.log("   📊 Performance Stats:", {
      totalRequests: perfStats.totalRequests,
      avgResponseTime: perfStats.avgResponseTime,
      slowRequests: perfStats.slowRequests,
      criticalRequests: perfStats.criticalRequests,
    });

    // Test 4: Email service health
    console.log("\n4. Testing email service health...");
    const emailHealth = emailRetryService.getHealthStatus();
    const emailStats = emailMetrics.getMetrics();

    console.log("   📧 Email Health:", {
      status: emailHealth.status,
      message: emailHealth.message,
      deadLetterQueueSize: emailHealth.deadLetterQueueSize,
    });

    console.log("   📧 Email Stats:", {
      sent: emailStats.sent,
      failed: emailStats.failed,
      avgResponseTime: emailStats.avgResponseTime,
    });

    // Test 5: Timeout analysis by type
    console.log("\n5. Testing timeout analysis by type...");
    const timeoutsByType = timeoutAnalyzer.getTimeoutsByType();
    console.log("   📊 Timeouts by Type:", timeoutsByType);

    // Test 6: Simulate timeout spike
    console.log("\n6. Testing timeout spike detection...");
    for (let i = 0; i < 7; i++) {
      timeoutAnalyzer.recordTimeout(
        `/api/test/endpoint-${i}`,
        "GET",
        15000 + i * 1000
      );
    }
    console.log(
      "   ✅ Recorded 7 additional timeouts (should trigger spike detection)"
    );

    // Test 7: Health check simulation
    console.log("\n7. Testing health check endpoints...");
    console.log("   🔗 Available health endpoints:");
    console.log("   - GET /api/health/ - Basic health check");
    console.log(
      "   - GET /api/health/detailed - Detailed health (authenticated)"
    );
    console.log(
      "   - GET /api/health/metrics - Performance metrics (admin only)"
    );
    console.log(
      "   - GET /api/health/timeouts - Timeout analysis (admin only)"
    );
    console.log(
      "   - GET /api/health/email - Email service health (admin only)"
    );
    console.log("   - POST /api/health/reset - Reset metrics (admin only)");

    // Test 8: Cleanup and reset
    console.log("\n8. Testing cleanup and reset...");
    timeoutAnalyzer.cleanup();
    performanceTracker.cleanup();
    console.log("   ✅ Cleanup completed");

    // Final stats
    console.log("\n📊 Final Statistics:");
    console.log("   Timeout Analyzer:", timeoutAnalyzer.getStats());
    console.log("   Performance Tracker:", performanceTracker.getStats());
    console.log("   Email Service:", emailRetryService.getHealthStatus());

    console.log("\n🎉 All tests completed successfully!");
    console.log("\n💡 Next steps:");
    console.log("   1. Start the server: npm run dev");
    console.log(
      "   2. Test health endpoints: http://localhost:5000/api/health/"
    );
    console.log("   3. Monitor logs for timeout and performance alerts");
    console.log("   4. Test order status updates to see monitoring in action");

    await AppDataSource.destroy();
    process.exit(0);
  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testTimeoutMonitoring();
}
