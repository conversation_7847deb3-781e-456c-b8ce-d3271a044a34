import { AppDataSource } from "../config/db.config";
import { Product, Inventory, User, InventoryStatus } from "../entities";

async function initializeInventory() {
  try {
    await AppDataSource.initialize();
    console.log("Database connected");

    const productRepo = AppDataSource.getRepository(Product);
    const inventoryRepo = AppDataSource.getRepository(Inventory);
    const userRepo = AppDataSource.getRepository(User);

    // Get all products that don't have inventory
    const products = await productRepo.find({
      relations: ["seller"],
    });

    console.log(`Found ${products.length} products`);

    for (const product of products) {
      // Check if inventory already exists
      const existingInventory = await inventoryRepo.findOne({
        where: { product: { id: product.id } },
      });

      if (!existingInventory) {
        // Create inventory for this product
        const currentStock = product.quantity || 10;
        const status =
          currentStock > 5
            ? InventoryStatus.IN_STOCK
            : currentStock > 0
              ? InventoryStatus.LOW_STOCK
              : InventoryStatus.OUT_OF_STOCK;

        const inventory = inventoryRepo.create({
          product,
          seller: product.seller,
          currentStock,
          lowStockThreshold: 5,
          reorderPoint: 3,
          reorderQuantity: 20,
          sku: `SKU-${product.id.slice(0, 8).toUpperCase()}`,
          status,
        });

        await inventoryRepo.save(inventory);
        console.log(`Created inventory for product: ${product.title}`);
      } else {
        console.log(`Inventory already exists for product: ${product.title}`);
      }
    }

    console.log("Inventory initialization completed");
    process.exit(0);
  } catch (error) {
    console.error("Error initializing inventory:", error);
    process.exit(1);
  }
}

initializeInventory();
