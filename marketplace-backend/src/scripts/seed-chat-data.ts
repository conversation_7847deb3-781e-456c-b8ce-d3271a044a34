import { AppDataSource } from "../config/db.config";
import { User, Conversation, Message } from "../entities";
import bcrypt from "bcrypt";

async function seedChatData() {
  try {
    await AppDataSource.initialize();
    console.log("Database connected");

    const userRepo = AppDataSource.getRepository(User);
    const conversationRepo = AppDataSource.getRepository(Conversation);
    const messageRepo = AppDataSource.getRepository(Message);

    // Create test users if they don't exist
    const testUsers = [
      {
        name: "Test Buyer",
        email: "<EMAIL>",
        password: "testPass",
        role: "buyer" as const,
      },
      {
        name: "<PERSON>",
        email: "<EMAIL>", 
        password: "testPass",
        role: "seller" as const,
      },
      {
        name: "<PERSON> Admin",
        email: "<EMAIL>",
        password: "testPass", 
        role: "admin" as const,
      },
    ];

    const createdUsers: User[] = [];

    for (const userData of testUsers) {
      let user = await userRepo.findOne({ where: { email: userData.email } });
      
      if (!user) {
        const hashedPassword = await bcrypt.hash(userData.password, 10);
        user = userRepo.create({
          ...userData,
          password: hashedPassword,
        });
        await userRepo.save(user);
        console.log(`✅ Created user: ${userData.name} (${userData.email})`);
      } else {
        console.log(`⏭️  User already exists: ${userData.name} (${userData.email})`);
      }
      
      createdUsers.push(user);
    }

    const [buyer, seller, admin] = createdUsers;

    // Create test conversations
    const conversations = [
      {
        participantOne: buyer,
        participantTwo: seller,
        messages: [
          {
            sender: buyer,
            content: "Hi! I'm interested in your wireless headphones. Are they still available?",
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          },
          {
            sender: seller,
            content: "Hello! Yes, they are still available. They're in excellent condition and come with the original box.",
            createdAt: new Date(Date.now() - 1.5 * 60 * 60 * 1000), // 1.5 hours ago
          },
          {
            sender: buyer,
            content: "Great! What's the battery life like?",
            createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
          },
          {
            sender: seller,
            content: "The battery lasts about 30 hours with noise cancellation off, and about 20 hours with it on. Perfect for long trips!",
            createdAt: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
          },
          {
            sender: buyer,
            content: "Sounds perfect! I'll take them. Can you ship to Nairobi?",
            createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
          },
        ],
      },
      {
        participantOne: buyer,
        participantTwo: admin,
        messages: [
          {
            sender: buyer,
            content: "Hi, I have a question about my recent order. The tracking number doesn't seem to work.",
            createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
          },
          {
            sender: admin,
            content: "Hello! I'd be happy to help you with that. Can you please provide your order ID?",
            createdAt: new Date(Date.now() - 2.5 * 60 * 60 * 1000), // 2.5 hours ago
          },
          {
            sender: buyer,
            content: "Sure, it's ORD-2024-001234",
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          },
          {
            sender: admin,
            content: "Thank you! I can see your order here. The tracking number was updated this morning. Please try: TRK789456123",
            createdAt: new Date(Date.now() - 1.5 * 60 * 60 * 1000), // 1.5 hours ago
          },
        ],
      },
    ];

    for (const convData of conversations) {
      // Check if conversation already exists
      let conversation = await conversationRepo.findOne({
        where: [
          {
            participantOne: { id: convData.participantOne.id },
            participantTwo: { id: convData.participantTwo.id },
          },
          {
            participantOne: { id: convData.participantTwo.id },
            participantTwo: { id: convData.participantOne.id },
          },
        ],
      });

      if (!conversation) {
        conversation = conversationRepo.create({
          participantOne: convData.participantOne,
          participantTwo: convData.participantTwo,
        });
        await conversationRepo.save(conversation);
        console.log(`✅ Created conversation between ${convData.participantOne.name} and ${convData.participantTwo.name}`);

        // Add messages to the conversation
        for (const msgData of convData.messages) {
          const message = messageRepo.create({
            conversation,
            sender: msgData.sender,
            content: msgData.content,
            createdAt: msgData.createdAt,
          });
          await messageRepo.save(message);
        }

        // Update conversation with last message preview
        const lastMessage = convData.messages[convData.messages.length - 1];
        conversation.lastMessagePreview = lastMessage.content.length > 50 
          ? lastMessage.content.substring(0, 50) + "..."
          : lastMessage.content;
        conversation.updatedAt = lastMessage.createdAt;
        await conversationRepo.save(conversation);

        console.log(`✅ Added ${convData.messages.length} messages to conversation`);
      } else {
        console.log(`⏭️  Conversation already exists between ${convData.participantOne.name} and ${convData.participantTwo.name}`);
      }
    }

    console.log("\n🎉 Chat data seeding completed!");
    console.log("\n📋 Test Credentials:");
    console.log("Buyer: <EMAIL> / testPass");
    console.log("Seller: <EMAIL> / testPass");
    console.log("Admin: <EMAIL> / testPass");
    
    process.exit(0);
  } catch (error) {
    console.error("❌ Error seeding chat data:", error);
    process.exit(1);
  }
}

seedChatData();
