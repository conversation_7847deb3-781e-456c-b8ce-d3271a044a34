import logger from "./logger";

/**
 * Timeout Pattern Detection System
 *
 * Tracks timeout occurrences across endpoints and detects patterns
 * that might indicate system issues or performance degradation.
 */

interface TimeoutRecord {
  timestamp: number;
  endpoint: string;
  method: string;
  duration: number;
  timeoutType: "route" | "controller" | "database" | "external";
  userId?: string;
  userAgent?: string;
  ip?: string;
  errorMessage?: string;
  stackTrace?: string;
}

interface TimeoutPattern {
  endpoint: string;
  count: number;
  avgDuration: number;
  timeWindow: string;
  severity: "low" | "medium" | "high" | "critical";
  timeoutTypes: string[];
}

class TimeoutAnalyzer {
  private timeouts: TimeoutRecord[] = [];
  private readonly MAX_RECORDS = 10000; // Prevent memory leaks
  private readonly SPIKE_THRESHOLD = 5; // Timeouts in 5 minutes
  private readonly SPIKE_WINDOW = 5 * 60 * 1000; // 5 minutes in ms
  private readonly ALERT_COOLDOWN = 10 * 60 * 1000; // 10 minutes between alerts
  private lastAlertTime = 0;

  /**
   * Record a timeout occurrence
   */
  recordTimeout(
    endpoint: string,
    method: string = "GET",
    duration: number = 0,
    timeoutType: TimeoutRecord["timeoutType"] = "route",
    userId?: string,
    userAgent?: string,
    ip?: string,
    errorMessage?: string,
    stackTrace?: string
  ): void {
    const record: TimeoutRecord = {
      timestamp: Date.now(),
      endpoint,
      method,
      duration,
      timeoutType,
      userId,
      userAgent,
      ip,
      errorMessage,
      stackTrace,
    };

    this.timeouts.push(record);

    // Keep only recent records to prevent memory leaks
    if (this.timeouts.length > this.MAX_RECORDS) {
      this.timeouts = this.timeouts.slice(-this.MAX_RECORDS);
    }

    // Analyze patterns after each timeout
    this.analyzePatterns();
  }

  /**
   * Analyze timeout patterns and detect spikes
   */
  private analyzePatterns(): void {
    const now = Date.now();
    const recentTimeouts = this.timeouts.filter(
      (t) => now - t.timestamp < this.SPIKE_WINDOW
    );

    if (recentTimeouts.length >= this.SPIKE_THRESHOLD) {
      // Check if we should alert (avoid spam)
      if (now - this.lastAlertTime > this.ALERT_COOLDOWN) {
        this.triggerTimeoutAlert(recentTimeouts);
        this.lastAlertTime = now;
      }
    }
  }

  /**
   * Trigger timeout alert with detailed analysis
   */
  private triggerTimeoutAlert(recentTimeouts: TimeoutRecord[]): void {
    const endpointGroups = this.groupByEndpoint(recentTimeouts);
    const patterns = this.analyzeEndpointPatterns(endpointGroups);

    logger.error("TIMEOUT SPIKE DETECTED", {
      count: recentTimeouts.length,
      timeWindow: "5 minutes",
      threshold: this.SPIKE_THRESHOLD,
      patterns,
      recommendation: "Check system health and email service status",
    });

    // TODO: In production, this could trigger:
    // - Slack/Teams notifications
    // - PagerDuty alerts
    // - Email to admin team
    // - Circuit breaker activation
  }

  /**
   * Group timeouts by endpoint
   */
  private groupByEndpoint(
    timeouts: TimeoutRecord[]
  ): Record<string, TimeoutRecord[]> {
    return timeouts.reduce(
      (groups, timeout) => {
        const key = `${timeout.method} ${timeout.endpoint}`;
        if (!groups[key]) {
          groups[key] = [];
        }
        groups[key].push(timeout);
        return groups;
      },
      {} as Record<string, TimeoutRecord[]>
    );
  }

  /**
   * Analyze patterns for each endpoint
   */
  private analyzeEndpointPatterns(
    endpointGroups: Record<string, TimeoutRecord[]>
  ): TimeoutPattern[] {
    return Object.entries(endpointGroups).map(([endpoint, timeouts]) => {
      const avgDuration =
        timeouts.reduce((sum, t) => sum + t.duration, 0) / timeouts.length;

      const timeoutTypes = [...new Set(timeouts.map(t => t.timeoutType))];

      let severity: TimeoutPattern['severity'] = 'low';
      if (timeouts.length >= 10) severity = 'critical';
      else if (timeouts.length >= 5) severity = 'high';
      else if (timeouts.length >= 3) severity = 'medium';

      return {
        endpoint,
        count: timeouts.length,
        avgDuration: Math.round(avgDuration),
        timeWindow: "5 minutes",
        severity,
        timeoutTypes,
      };
    });
  }

  /**
   * Get timeout statistics
   */
  getStats(): {
    totalTimeouts: number;
    recentTimeouts: number;
    byEndpoint: Record<string, number>;
    avgDuration: number;
    lastTimeout: Date | null;
  } {
    const now = Date.now();
    const recentTimeouts = this.timeouts.filter(
      (t) => now - t.timestamp < this.SPIKE_WINDOW
    );

    const byEndpoint = this.timeouts.reduce(
      (acc, timeout) => {
        const key = `${timeout.method} ${timeout.endpoint}`;
        acc[key] = (acc[key] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    const avgDuration =
      this.timeouts.length > 0
        ? this.timeouts.reduce((sum, t) => sum + t.duration, 0) /
          this.timeouts.length
        : 0;

    return {
      totalTimeouts: this.timeouts.length,
      recentTimeouts: recentTimeouts.length,
      byEndpoint,
      avgDuration: Math.round(avgDuration),
      lastTimeout:
        this.timeouts.length > 0
          ? new Date(this.timeouts[this.timeouts.length - 1].timestamp)
          : null,
    };
  }

  /**
   * Get timeouts by type (route, controller, database, external)
   */
  getTimeoutsByType(): Record<string, number> {
    const now = Date.now();
    const recentTimeouts = this.timeouts.filter(
      (t) => now - t.timestamp < this.SPIKE_WINDOW
    );

    const timeoutsByType: Record<string, number> = {};
    recentTimeouts.forEach(timeout => {
      timeoutsByType[timeout.timeoutType] = (timeoutsByType[timeout.timeoutType] || 0) + 1;
    });

    return timeoutsByType;
  }

  /**
   * Get timeout trends over time
   */
  getTimeoutTrends(): {
    hourly: Record<string, number>;
    daily: Record<string, number>;
    byEndpoint: Record<string, { count: number; trend: 'increasing' | 'stable' | 'decreasing' }>;
  } {
    const now = Date.now();
    const hourly: Record<string, number> = {};
    const daily: Record<string, number> = {};
    const endpointCounts: Record<string, number[]> = {};

    // Initialize time buckets
    for (let i = 0; i < 24; i++) {
      const hour = new Date(now - i * 60 * 60 * 1000).getHours();
      hourly[hour.toString()] = 0;
    }

    for (let i = 0; i < 7; i++) {
      const day = new Date(now - i * 24 * 60 * 60 * 1000).toDateString();
      daily[day] = 0;
    }

    this.timeouts.forEach(timeout => {
      const timeoutDate = new Date(timeout.timestamp);
      const hour = timeoutDate.getHours().toString();
      const day = timeoutDate.toDateString();

      if (hourly[hour] !== undefined) hourly[hour]++;
      if (daily[day] !== undefined) daily[day]++;

      // Track by endpoint for trend analysis
      const endpoint = `${timeout.method} ${timeout.endpoint}`;
      if (!endpointCounts[endpoint]) endpointCounts[endpoint] = [];
      endpointCounts[endpoint].push(timeout.timestamp);
    });

    // Calculate trends for endpoints
    const byEndpoint: Record<string, { count: number; trend: 'increasing' | 'stable' | 'decreasing' }> = {};

    Object.entries(endpointCounts).forEach(([endpoint, timestamps]) => {
      const recentHalf = timestamps.filter(ts => ts > now - 12 * 60 * 60 * 1000).length;
      const olderHalf = timestamps.filter(ts => ts <= now - 12 * 60 * 60 * 1000 && ts > now - 24 * 60 * 60 * 1000).length;

      let trend: 'increasing' | 'stable' | 'decreasing' = 'stable';
      if (recentHalf > olderHalf * 1.5) trend = 'increasing';
      else if (recentHalf < olderHalf * 0.5) trend = 'decreasing';

      byEndpoint[endpoint] = {
        count: timestamps.length,
        trend,
      };
    });

    return { hourly, daily, byEndpoint };
  }

  /**
   * Get health status
   */
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    message: string;
    recentTimeouts: number;
    totalTimeouts: number;
    topProblematicEndpoints: Array<{ endpoint: string; count: number }>;
  } {
    const now = Date.now();
    const recentTimeouts = this.timeouts.filter(t => now - t.timestamp < this.SPIKE_WINDOW);
    const stats = this.getStats();

    const topProblematicEndpoints = Object.entries(stats.byEndpoint)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([endpoint, count]) => ({ endpoint, count }));

    if (recentTimeouts.length >= 10) {
      return {
        status: 'critical',
        message: `Critical: ${recentTimeouts.length} timeouts in last 5 minutes`,
        recentTimeouts: recentTimeouts.length,
        totalTimeouts: this.timeouts.length,
        topProblematicEndpoints,
      };
    }

    if (recentTimeouts.length >= 3 || this.timeouts.length >= 50) {
      return {
        status: 'warning',
        message: `Warning: ${recentTimeouts.length} recent timeouts detected`,
        recentTimeouts: recentTimeouts.length,
        totalTimeouts: this.timeouts.length,
        topProblematicEndpoints,
      };
    }

    return {
      status: 'healthy',
      message: 'No significant timeout issues detected',
      recentTimeouts: recentTimeouts.length,
      totalTimeouts: this.timeouts.length,
      topProblematicEndpoints,
    };
  }

  /**
   * Clear old timeout records
   */
  cleanup(): void {
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    this.timeouts = this.timeouts.filter((t) => t.timestamp > oneHourAgo);

    logger.info("Timeout analyzer cleanup completed", {
      remainingRecords: this.timeouts.length,
    });
  }

  /**
   * Clear old timeout records with custom time window
   */
  clearOldTimeouts(olderThanMs: number = 7 * 24 * 60 * 60 * 1000): number {
    const cutoff = Date.now() - olderThanMs;
    const initialLength = this.timeouts.length;
    this.timeouts = this.timeouts.filter(timeout => timeout.timestamp > cutoff);
    const cleared = initialLength - this.timeouts.length;

    if (cleared > 0) {
      logger.info("Cleared old timeout records", {
        cleared,
        remaining: this.timeouts.length,
        cutoffAge: `${olderThanMs / 1000 / 60 / 60 / 24} days`,
      });
    }

    return cleared;
  }

  /**
   * Reset all timeout records
   */
  reset(): void {
    this.timeouts = [];
    this.lastAlertTime = 0;

    logger.info("Timeout analyzer reset completed");
  }
}

// Export singleton instance
export const timeoutAnalyzer = new TimeoutAnalyzer();

// Cleanup old records every hour
setInterval(
  () => {
    timeoutAnalyzer.cleanup();
  },
  60 * 60 * 1000
);
