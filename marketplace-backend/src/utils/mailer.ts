import nodemailer from "nodemailer";
import { renderEmailTemplate } from "./template";
import { getOrderConfirmationTemplate } from "../templates/order-confirmation.template";
import { getOrderStatusUpdateTemplate } from "../templates/order-status-update.template";
import logger from "./logger";
import { emailRetryService } from "./emailRetryService";
import { emailMetrics } from "./emailMetrics";

// Create reusable transporter
const createTransporter = () => {
  return nodemailer.createTransport({
    service: "gmail",
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });
};

/**
 * Send OTP email for password reset with retry mechanism
 */
export const sendOTPEmail = async (email: string, otp: string) => {
  // Render the OTP email template
  const html = renderEmailTemplate("otp", { otp });

  return emailRetryService.sendWithRetry(
    {
      to: email,
      subject: "Your Password Reset OTP",
      html,
      type: "otp",
      metadata: { otp, expiresIn: "5 minutes" },
    },
    async (to: string, subject: string, htmlContent: string) => {
      const transporter = createTransporter();

      const mailOptions = {
        from: `"Marketplace Security" <${process.env.EMAIL_USER}>`,
        to,
        subject,
        text: `Your OTP is ${otp}. It expires in 5 minutes.`,
        html: htmlContent,
      };

      return await transporter.sendMail(mailOptions);
    }
  );
};

/**
 * Send a generic email (internal function without retry)
 */
const sendEmailDirect = async (to: string, subject: string, html: string) => {
  const transporter = createTransporter();

  const mailOptions = {
    from: `"Marketplace" <${process.env.EMAIL_USER}>`,
    to,
    subject,
    html,
  };

  const result = await transporter.sendMail(mailOptions);
  return result;
};

/**
 * Send a generic email with retry mechanism
 */
export const sendEmail = async (
  to: string,
  subject: string,
  html: string,
  type: string = "generic"
) => {
  return emailRetryService.sendWithRetry(
    {
      to,
      subject,
      html,
      type,
    },
    sendEmailDirect
  );
};

/**
 * Send order confirmation email with beautiful template and retry mechanism
 */
export const sendOrderConfirmationEmail = async (
  email: string,
  name: string,
  order: {
    id: string;
    total: number;
    items: Array<{
      product: { name: string; price: number };
      quantity: number;
      price: number;
    }>;
    shippingAddress?: {
      fullName: string;
      street: string;
      city: string;
      county: string;
      postalCode: string;
      country: string;
    };
    paymentInfo?: {
      method: string;
      transactionId?: string;
    };
  }
) => {
  const html = getOrderConfirmationTemplate(name, order);
  const orderNumber = `#${order.id.slice(-8).toUpperCase()}`;

  return await sendEmail(
    email,
    `Order Confirmation - ${orderNumber}`,
    html,
    "order-confirmation"
  );
};

/**
 * Send order status update email with beautiful template and retry mechanism
 */
export const sendOrderStatusUpdateEmail = async (
  email: string,
  name: string,
  orderId: string,
  orderNumber: string,
  status: "pending" | "paid" | "shipped" | "fulfilled" | "cancelled",
  trackingNumber?: string
) => {
  const html = getOrderStatusUpdateTemplate(
    name,
    orderId,
    orderNumber,
    status,
    trackingNumber
  );

  return await sendEmail(
    email,
    `Order Update - ${orderNumber}`,
    html,
    "order-status-update"
  );
};
