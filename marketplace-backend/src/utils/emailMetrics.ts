import logger from "./logger";

/**
 * Email Metrics Tracking System
 * 
 * Tracks email sending performance, failure rates, and provides alerting
 * when failure rates exceed configurable thresholds.
 */

interface EmailMetrics {
  sent: number;
  failed: number;
  avgResponseTime: number;
  totalResponseTime: number;
  lastResetTime: number;
}

interface EmailAttempt {
  email: string;
  type: string;
  startTime: number;
  endTime?: number;
  success?: boolean;
  error?: string;
}

class EmailMetricsService {
  private metrics: EmailMetrics = {
    sent: 0,
    failed: 0,
    avgResponseTime: 0,
    totalResponseTime: 0,
    lastResetTime: Date.now(),
  };

  private recentAttempts: EmailAttempt[] = [];
  private readonly MAX_RECENT_ATTEMPTS = 1000;
  private readonly FAILURE_RATE_THRESHOLD = 0.1; // 10%
  private readonly RESET_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * Start tracking an email sending attempt
   */
  startEmailAttempt(email: string, type: string): string {
    const attemptId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const attempt: EmailAttempt = {
      email,
      type,
      startTime: Date.now(),
    };

    this.recentAttempts.push(attempt);
    
    // Keep only recent attempts to prevent memory leaks
    if (this.recentAttempts.length > this.MAX_RECENT_ATTEMPTS) {
      this.recentAttempts = this.recentAttempts.slice(-this.MAX_RECENT_ATTEMPTS);
    }

    return attemptId;
  }

  /**
   * Record successful email sending
   */
  recordSuccess(email: string, type: string, responseTime: number): void {
    this.metrics.sent++;
    this.metrics.totalResponseTime += responseTime;
    this.metrics.avgResponseTime = this.metrics.totalResponseTime / this.metrics.sent;

    // Update recent attempt
    const attempt = this.recentAttempts.find(a => 
      a.email === email && a.type === type && !a.success && !a.error
    );
    if (attempt) {
      attempt.endTime = Date.now();
      attempt.success = true;
    }

    logger.info("Email sent successfully", {
      email,
      type,
      responseTime: `${responseTime}ms`,
      avgResponseTime: `${this.metrics.avgResponseTime.toFixed(2)}ms`,
      totalSent: this.metrics.sent,
      totalFailed: this.metrics.failed,
    });

    this.checkForReset();
  }

  /**
   * Record failed email sending
   */
  recordFailure(email: string, type: string, error: string): void {
    this.metrics.failed++;

    // Update recent attempt
    const attempt = this.recentAttempts.find(a => 
      a.email === email && a.type === type && !a.success && !a.error
    );
    if (attempt) {
      attempt.endTime = Date.now();
      attempt.success = false;
      attempt.error = error;
    }

    const failureRate = this.getFailureRate();

    logger.warn("Email sending failed", {
      email,
      type,
      error,
      failureRate: `${(failureRate * 100).toFixed(2)}%`,
      totalSent: this.metrics.sent,
      totalFailed: this.metrics.failed,
    });

    // Check if failure rate exceeds threshold
    if (failureRate > this.FAILURE_RATE_THRESHOLD) {
      this.triggerHighFailureRateAlert(failureRate);
    }

    this.checkForReset();
  }

  /**
   * Get current failure rate
   */
  getFailureRate(): number {
    const total = this.metrics.sent + this.metrics.failed;
    return total > 0 ? this.metrics.failed / total : 0;
  }

  /**
   * Get current metrics
   */
  getMetrics(): EmailMetrics & { failureRate: number; uptime: number } {
    const uptime = Date.now() - this.metrics.lastResetTime;
    return {
      ...this.metrics,
      failureRate: this.getFailureRate(),
      uptime,
    };
  }

  /**
   * Get recent email attempts for debugging
   */
  getRecentAttempts(limit: number = 50): EmailAttempt[] {
    return this.recentAttempts
      .slice(-limit)
      .sort((a, b) => b.startTime - a.startTime);
  }

  /**
   * Get email performance by type
   */
  getPerformanceByType(): Record<string, { count: number; avgResponseTime: number; failureRate: number }> {
    const typeStats: Record<string, { 
      total: number; 
      successful: number; 
      totalResponseTime: number; 
    }> = {};

    this.recentAttempts.forEach(attempt => {
      if (!typeStats[attempt.type]) {
        typeStats[attempt.type] = { total: 0, successful: 0, totalResponseTime: 0 };
      }
      
      typeStats[attempt.type].total++;
      
      if (attempt.success && attempt.endTime) {
        typeStats[attempt.type].successful++;
        typeStats[attempt.type].totalResponseTime += (attempt.endTime - attempt.startTime);
      }
    });

    const result: Record<string, { count: number; avgResponseTime: number; failureRate: number }> = {};
    
    Object.entries(typeStats).forEach(([type, stats]) => {
      result[type] = {
        count: stats.total,
        avgResponseTime: stats.successful > 0 ? stats.totalResponseTime / stats.successful : 0,
        failureRate: stats.total > 0 ? (stats.total - stats.successful) / stats.total : 0,
      };
    });

    return result;
  }

  /**
   * Trigger high failure rate alert
   */
  private triggerHighFailureRateAlert(failureRate: number): void {
    const total = this.metrics.sent + this.metrics.failed;
    
    logger.error("HIGH EMAIL FAILURE RATE DETECTED", {
      failureRate: `${(failureRate * 100).toFixed(2)}%`,
      threshold: `${(this.FAILURE_RATE_THRESHOLD * 100).toFixed(2)}%`,
      totalSent: this.metrics.sent,
      totalFailed: this.metrics.failed,
      totalAttempts: total,
      avgResponseTime: `${this.metrics.avgResponseTime.toFixed(2)}ms`,
      uptime: `${((Date.now() - this.metrics.lastResetTime) / 1000 / 60).toFixed(2)} minutes`,
      recommendation: "Check email service health and configuration",
    });

    // TODO: In production, this could trigger:
    // - Slack/Teams notifications
    // - PagerDuty alerts
    // - Email to admin team
    // - Circuit breaker activation
  }

  /**
   * Reset metrics if enough time has passed
   */
  private checkForReset(): void {
    const timeSinceReset = Date.now() - this.metrics.lastResetTime;
    
    if (timeSinceReset > this.RESET_INTERVAL) {
      logger.info("Resetting email metrics", {
        previousMetrics: this.getMetrics(),
        resetInterval: `${this.RESET_INTERVAL / 1000 / 60 / 60} hours`,
      });
      
      this.resetMetrics();
    }
  }

  /**
   * Manually reset metrics
   */
  resetMetrics(): void {
    this.metrics = {
      sent: 0,
      failed: 0,
      avgResponseTime: 0,
      totalResponseTime: 0,
      lastResetTime: Date.now(),
    };
    
    // Keep only recent attempts from last hour
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    this.recentAttempts = this.recentAttempts.filter(attempt => 
      attempt.startTime > oneHourAgo
    );
  }

  /**
   * Get health status
   */
  getHealthStatus(): { status: 'healthy' | 'warning' | 'critical'; message: string } {
    const failureRate = this.getFailureRate();
    const total = this.metrics.sent + this.metrics.failed;
    
    if (total === 0) {
      return { status: 'healthy', message: 'No email activity recorded' };
    }
    
    if (failureRate > this.FAILURE_RATE_THRESHOLD * 2) {
      return { 
        status: 'critical', 
        message: `Critical failure rate: ${(failureRate * 100).toFixed(2)}%` 
      };
    }
    
    if (failureRate > this.FAILURE_RATE_THRESHOLD) {
      return { 
        status: 'warning', 
        message: `High failure rate: ${(failureRate * 100).toFixed(2)}%` 
      };
    }
    
    return { 
      status: 'healthy', 
      message: `Healthy operation: ${(failureRate * 100).toFixed(2)}% failure rate` 
    };
  }
}

// Export singleton instance
export const emailMetrics = new EmailMetricsService();
export default emailMetrics;
