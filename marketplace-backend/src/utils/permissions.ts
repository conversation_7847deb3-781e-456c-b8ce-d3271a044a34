import { UserRole } from "../entities/User.entity";

/**
 * Permission system for role-based access control
 */

export enum Permission {
  // User Management
  MANAGE_USERS = "manage_users",
  VIEW_USERS = "view_users",
  MANAGE_USER_ROLES = "manage_user_roles",
  
  // Product Management
  MANAGE_PRODUCTS = "manage_products",
  VIEW_PRODUCTS = "view_products",
  MODERATE_PRODUCTS = "moderate_products",
  
  // Order Management
  MANAGE_ORDERS = "manage_orders",
  VIEW_ORDERS = "view_orders",
  PROCESS_REFUNDS = "process_refunds",
  
  // Dispute Resolution
  MANAGE_DISPUTES = "manage_disputes",
  VIEW_DISPUTES = "view_disputes",
  RESOLVE_DISPUTES = "resolve_disputes",
  
  // Technical Monitoring
  VIEW_SYSTEM_HEALTH = "view_system_health",
  MANAGE_MONITORING = "manage_monitoring",
  VIEW_LOGS = "view_logs",
  MANAGE_DLQ = "manage_dlq",
  VIEW_PERFORMANCE_METRICS = "view_performance_metrics",
  
  // Issue Management
  VIEW_USER_ISSUES = "view_user_issues",
  MANAGE_USER_ISSUES = "manage_user_issues",
  ESCALATE_TO_DEVELOPERS = "escalate_to_developers",
  
  // System Administration
  MANAGE_SYSTEM_SETTINGS = "manage_system_settings",
  VIEW_ANALYTICS = "view_analytics",
  MANAGE_CATEGORIES = "manage_categories",
  
  // Communication
  MANAGE_MESSAGES = "manage_messages",
  VIEW_CONVERSATIONS = "view_conversations",
}

/**
 * Role-based permission mapping
 */
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  buyer: [
    // Buyers can only manage their own data
  ],
  
  seller: [
    Permission.MANAGE_PRODUCTS,
    Permission.VIEW_PRODUCTS,
    Permission.VIEW_ORDERS,
    Permission.MANAGE_MESSAGES,
    Permission.VIEW_CONVERSATIONS,
  ],
  
  admin: [
    // Legacy admin role - full access for backward compatibility
    ...Object.values(Permission),
  ],
  
  business_admin: [
    // Business operations and customer support
    Permission.MANAGE_USERS,
    Permission.VIEW_USERS,
    Permission.MANAGE_PRODUCTS,
    Permission.VIEW_PRODUCTS,
    Permission.MODERATE_PRODUCTS,
    Permission.MANAGE_ORDERS,
    Permission.VIEW_ORDERS,
    Permission.PROCESS_REFUNDS,
    Permission.MANAGE_DISPUTES,
    Permission.VIEW_DISPUTES,
    Permission.RESOLVE_DISPUTES,
    Permission.MANAGE_CATEGORIES,
    Permission.MANAGE_MESSAGES,
    Permission.VIEW_CONVERSATIONS,
    Permission.VIEW_ANALYTICS,
  ],
  
  technical_admin: [
    // Technical operations and system monitoring
    Permission.VIEW_SYSTEM_HEALTH,
    Permission.MANAGE_MONITORING,
    Permission.VIEW_LOGS,
    Permission.MANAGE_DLQ,
    Permission.VIEW_PERFORMANCE_METRICS,
    Permission.VIEW_USER_ISSUES,
    Permission.MANAGE_USER_ISSUES,
    Permission.ESCALATE_TO_DEVELOPERS,
    Permission.MANAGE_SYSTEM_SETTINGS,
    Permission.VIEW_ANALYTICS,
    Permission.VIEW_USERS, // Read-only access to users for debugging
    Permission.VIEW_ORDERS, // Read-only access to orders for debugging
    Permission.VIEW_PRODUCTS, // Read-only access to products for debugging
  ],
};

/**
 * Check if a user role has a specific permission
 */
export function hasPermission(role: UserRole, permission: Permission): boolean {
  const rolePermissions = ROLE_PERMISSIONS[role] || [];
  return rolePermissions.includes(permission);
}

/**
 * Check if a user role has any of the specified permissions
 */
export function hasAnyPermission(role: UserRole, permissions: Permission[]): boolean {
  return permissions.some(permission => hasPermission(role, permission));
}

/**
 * Check if a user role has all of the specified permissions
 */
export function hasAllPermissions(role: UserRole, permissions: Permission[]): boolean {
  return permissions.every(permission => hasPermission(role, permission));
}

/**
 * Get all permissions for a role
 */
export function getRolePermissions(role: UserRole): Permission[] {
  return ROLE_PERMISSIONS[role] || [];
}

/**
 * Check if a role is an admin role (any type of admin)
 */
export function isAdminRole(role: UserRole): boolean {
  return ['admin', 'business_admin', 'technical_admin'].includes(role);
}

/**
 * Check if a role is a technical admin role
 */
export function isTechnicalAdmin(role: UserRole): boolean {
  return role === 'technical_admin' || role === 'admin';
}

/**
 * Check if a role is a business admin role
 */
export function isBusinessAdmin(role: UserRole): boolean {
  return role === 'business_admin' || role === 'admin';
}

/**
 * Get user-friendly role names
 */
export const ROLE_NAMES: Record<UserRole, string> = {
  buyer: "Buyer",
  seller: "Seller", 
  admin: "Super Admin",
  business_admin: "Business Admin",
  technical_admin: "Technical Admin",
};

/**
 * Get role descriptions
 */
export const ROLE_DESCRIPTIONS: Record<UserRole, string> = {
  buyer: "Can browse and purchase products",
  seller: "Can sell products and manage inventory",
  admin: "Full system access and control",
  business_admin: "Manages users, orders, disputes, and business operations",
  technical_admin: "Manages system monitoring, logs, and technical issues",
};
