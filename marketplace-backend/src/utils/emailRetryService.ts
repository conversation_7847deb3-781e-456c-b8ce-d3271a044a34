import logger from "./logger";
import { emailMetrics } from "./emailMetrics";
import { deadLetterQueueService } from "../services/deadLetterQueue.service";
import { EmailType, FailureReason } from "../entities/DeadLetterQueue.entity";

/**
 * Email Retry Service with Exponential Backoff
 * 
 * Implements robust retry mechanism for email sending with:
 * - Configurable retry attempts
 * - Exponential backoff delays
 * - Dead letter queue for failed emails
 * - Comprehensive error handling and logging
 */

interface EmailData {
  to: string;
  subject: string;
  html: string;
  text?: string;
  type: EmailType; // For metrics tracking
  userId?: string;
  orderId?: string;
  metadata?: Record<string, any>; // Additional context
}

interface RetryConfig {
  maxRetries: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
  jitterEnabled: boolean;
}

interface FailedEmail {
  emailData: EmailData;
  finalError: string;
  attemptCount: number;
  firstAttemptTime: number;
  lastAttemptTime: number;
  retryHistory: Array<{
    attempt: number;
    timestamp: number;
    error: string;
    delayMs: number;
  }>;
}

class EmailRetryService {
  private defaultConfig: RetryConfig = {
    maxRetries: 3,
    baseDelayMs: 1000, // 1 second
    maxDelayMs: 30000, // 30 seconds
    backoffMultiplier: 2,
    jitterEnabled: true,
  };

  private deadLetterQueue: FailedEmail[] = [];
  private readonly MAX_DEAD_LETTER_SIZE = 1000;

  /**
   * Send email with retry mechanism and exponential backoff
   */
  async sendWithRetry(
    emailData: EmailData,
    emailSendFunction: (to: string, subject: string, html: string) => Promise<any>,
    config: Partial<RetryConfig> = {}
  ): Promise<void> {
    const retryConfig = { ...this.defaultConfig, ...config };
    const startTime = Date.now();
    
    // Start metrics tracking
    emailMetrics.startEmailAttempt(emailData.to, emailData.type);

    const retryHistory: FailedEmail['retryHistory'] = [];
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        const attemptStartTime = Date.now();
        
        logger.info("Attempting to send email", {
          attempt,
          maxRetries: retryConfig.maxRetries,
          recipient: emailData.to,
          type: emailData.type,
          subject: emailData.subject,
        });

        // Attempt to send email
        const result = await emailSendFunction(emailData.to, emailData.subject, emailData.html);
        
        const responseTime = Date.now() - attemptStartTime;
        const totalTime = Date.now() - startTime;

        // Record success in metrics
        emailMetrics.recordSuccess(emailData.to, emailData.type, responseTime);

        logger.info("Email sent successfully", {
          attempt,
          recipient: emailData.to,
          type: emailData.type,
          responseTime: `${responseTime}ms`,
          totalTime: `${totalTime}ms`,
          retryHistory: retryHistory.length > 0 ? retryHistory : undefined,
        });

        return result;

      } catch (error: any) {
        lastError = error;
        const isLastAttempt = attempt === retryConfig.maxRetries;
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        // Calculate delay for next attempt
        const baseDelay = Math.pow(retryConfig.backoffMultiplier, attempt - 1) * retryConfig.baseDelayMs;
        const jitter = retryConfig.jitterEnabled ? Math.random() * 0.1 * baseDelay : 0;
        const delayMs = Math.min(baseDelay + jitter, retryConfig.maxDelayMs);

        // Record retry attempt
        retryHistory.push({
          attempt,
          timestamp: Date.now(),
          error: errorMessage,
          delayMs: isLastAttempt ? 0 : delayMs,
        });

        logger.warn("Email send attempt failed", {
          attempt,
          maxRetries: retryConfig.maxRetries,
          recipient: emailData.to,
          type: emailData.type,
          error: errorMessage,
          nextRetryIn: isLastAttempt ? "none" : `${delayMs}ms`,
          isLastAttempt,
        });

        if (isLastAttempt) {
          // Final failure - record in metrics and dead letter queue
          const totalTime = Date.now() - startTime;
          
          emailMetrics.recordFailure(emailData.to, emailData.type, errorMessage);
          
          await this.sendToDeadLetterQueue({
            emailData,
            finalError: errorMessage,
            attemptCount: attempt,
            firstAttemptTime: startTime,
            lastAttemptTime: Date.now(),
            retryHistory,
          });

          logger.error("Email sending failed after all retries", {
            recipient: emailData.to,
            type: emailData.type,
            totalAttempts: attempt,
            totalTime: `${totalTime}ms`,
            finalError: errorMessage,
            retryHistory,
          });

          throw new Error(`Email sending failed after ${attempt} attempts: ${errorMessage}`);
        }

        // Wait before retry with exponential backoff
        await this.sleep(delayMs);
      }
    }
  }

  /**
   * Send failed email to dead letter queue
   */
  private async sendToDeadLetterQueue(failedEmail: FailedEmail): Promise<void> {
    try {
      // Determine failure reason based on error message
      const failureReason = this.categorizeFailureReason(failedEmail.finalError);

      // Add to database-backed dead letter queue
      await deadLetterQueueService.addToQueue({
        emailType: failedEmail.emailData.type,
        recipientEmail: failedEmail.emailData.to,
        subject: failedEmail.emailData.subject,
        htmlContent: failedEmail.emailData.html,
        textContent: failedEmail.emailData.text,
        emailData: failedEmail.emailData,
        failureReason,
        errorMessage: failedEmail.finalError,
        stackTrace: failedEmail.retryHistory.map(h => `Attempt ${h.attempt}: ${h.error}`).join('\n'),
        userId: failedEmail.emailData.userId,
        orderId: failedEmail.emailData.orderId,
        priority: this.getEmailPriority(failedEmail.emailData.type),
        metadata: {
          ...failedEmail.emailData.metadata,
          attemptCount: failedEmail.attemptCount,
          totalTimeMs: failedEmail.lastAttemptTime - failedEmail.firstAttemptTime,
          retryHistory: failedEmail.retryHistory,
        },
      });

      // Also add to in-memory queue for backward compatibility
      this.deadLetterQueue.push(failedEmail);
      if (this.deadLetterQueue.length > this.MAX_DEAD_LETTER_SIZE) {
        this.deadLetterQueue = this.deadLetterQueue.slice(-this.MAX_DEAD_LETTER_SIZE);
      }

      logger.error("Email moved to dead letter queue", {
        recipient: failedEmail.emailData.to,
        type: failedEmail.emailData.type,
        subject: failedEmail.emailData.subject,
        finalError: failedEmail.finalError,
        attemptCount: failedEmail.attemptCount,
        totalTime: `${failedEmail.lastAttemptTime - failedEmail.firstAttemptTime}ms`,
        failureReason,
      });

    } catch (dlqError: any) {
      logger.error("Failed to add email to dead letter queue", {
        originalError: failedEmail.finalError,
        dlqError: dlqError.message,
        recipient: failedEmail.emailData.to,
      });
    }
  }

  /**
   * Categorize failure reason based on error message
   */
  private categorizeFailureReason(errorMessage: string): FailureReason {
    const lowerError = errorMessage.toLowerCase();

    if (lowerError.includes('timeout') || lowerError.includes('timed out')) {
      return 'timeout';
    }
    if (lowerError.includes('invalid') && lowerError.includes('email')) {
      return 'invalid_email';
    }
    if (lowerError.includes('rate limit') || lowerError.includes('too many')) {
      return 'rate_limit';
    }
    if (lowerError.includes('authentication') || lowerError.includes('auth')) {
      return 'authentication_failed';
    }
    if (lowerError.includes('smtp') || lowerError.includes('mail server')) {
      return 'smtp_error';
    }
    if (lowerError.includes('service unavailable') || lowerError.includes('connection')) {
      return 'service_unavailable';
    }

    return 'unknown_error';
  }

  /**
   * Get email priority based on type
   */
  private getEmailPriority(emailType: EmailType): number {
    switch (emailType) {
      case 'otp_verification':
      case 'password_reset':
        return 1; // High priority
      case 'order_confirmation':
      case 'order_status_update':
        return 2; // Medium priority
      case 'welcome':
      case 'general':
      default:
        return 3; // Low priority
    }
  }

  /**
   * Get dead letter queue contents
   */
  getDeadLetterQueue(): FailedEmail[] {
    return [...this.deadLetterQueue];
  }

  /**
   * Get dead letter queue statistics
   */
  getDeadLetterStats(): {
    totalFailed: number;
    byType: Record<string, number>;
    byError: Record<string, number>;
    recentFailures: number;
  } {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    
    const byType: Record<string, number> = {};
    const byError: Record<string, number> = {};
    let recentFailures = 0;

    this.deadLetterQueue.forEach(failed => {
      // Count by type
      byType[failed.emailData.type] = (byType[failed.emailData.type] || 0) + 1;
      
      // Count by error
      byError[failed.finalError] = (byError[failed.finalError] || 0) + 1;
      
      // Count recent failures
      if (failed.lastAttemptTime > oneHourAgo) {
        recentFailures++;
      }
    });

    return {
      totalFailed: this.deadLetterQueue.length,
      byType,
      byError,
      recentFailures,
    };
  }

  /**
   * Retry emails from dead letter queue
   */
  async retryFromDeadLetterQueue(
    emailSendFunction: (to: string, subject: string, html: string) => Promise<any>,
    maxRetries: number = 10
  ): Promise<{ successful: number; failed: number }> {
    const toRetry = this.deadLetterQueue.splice(0, maxRetries);
    let successful = 0;
    let failed = 0;

    logger.info("Retrying emails from dead letter queue", {
      count: toRetry.length,
      maxRetries,
    });

    for (const failedEmail of toRetry) {
      try {
        await this.sendWithRetry(
          failedEmail.emailData,
          emailSendFunction,
          { maxRetries: 2 } // Fewer retries for DLQ items
        );
        successful++;
        
        logger.info("Successfully retried email from dead letter queue", {
          recipient: failedEmail.emailData.to,
          type: failedEmail.emailData.type,
        });
      } catch (error) {
        failed++;
        // Put back in dead letter queue
        this.deadLetterQueue.push(failedEmail);
        
        logger.warn("Failed to retry email from dead letter queue", {
          recipient: failedEmail.emailData.to,
          type: failedEmail.emailData.type,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    logger.info("Dead letter queue retry completed", {
      attempted: toRetry.length,
      successful,
      failed,
      remainingInQueue: this.deadLetterQueue.length,
    });

    return { successful, failed };
  }

  /**
   * Clear dead letter queue
   */
  clearDeadLetterQueue(): number {
    const count = this.deadLetterQueue.length;
    this.deadLetterQueue = [];
    
    logger.info("Dead letter queue cleared", { clearedCount: count });
    
    return count;
  }

  /**
   * Sleep utility for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get service health status
   */
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    deadLetterQueueSize: number;
    recentFailures: number;
    message: string;
  } {
    const stats = this.getDeadLetterStats();
    
    if (stats.recentFailures > 10) {
      return {
        status: 'critical',
        deadLetterQueueSize: stats.totalFailed,
        recentFailures: stats.recentFailures,
        message: `Critical: ${stats.recentFailures} email failures in the last hour`,
      };
    }
    
    if (stats.recentFailures > 5 || stats.totalFailed > 50) {
      return {
        status: 'warning',
        deadLetterQueueSize: stats.totalFailed,
        recentFailures: stats.recentFailures,
        message: `Warning: ${stats.recentFailures} recent failures, ${stats.totalFailed} total in queue`,
      };
    }
    
    return {
      status: 'healthy',
      deadLetterQueueSize: stats.totalFailed,
      recentFailures: stats.recentFailures,
      message: 'Email retry service operating normally',
    };
  }
}

// Export singleton instance
export const emailRetryService = new EmailRetryService();
export default emailRetryService;
