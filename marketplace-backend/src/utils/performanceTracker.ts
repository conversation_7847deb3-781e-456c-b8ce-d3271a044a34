import { Request, Response, NextFunction } from "express";
import logger from "./logger";

/**
 * High-Resolution Performance Tracking System
 * 
 * Uses process.hrtime.bigint() for microsecond precision timing
 * Tracks request performance, detects slow requests, and provides
 * comprehensive analytics as documented in the timeout-tango blog.
 */

interface PerformanceMetric {
  method: string;
  url: string;
  statusCode: number;
  duration: number; // in milliseconds
  timestamp: number;
  userAgent?: string;
  userId?: string;
  ip?: string;
  memoryUsage?: NodeJS.MemoryUsage;
}

interface PerformanceStats {
  totalRequests: number;
  avgResponseTime: number;
  slowRequests: number;
  errorRequests: number;
  requestsByMethod: Record<string, number>;
  requestsByStatus: Record<string, number>;
  slowestRequests: PerformanceMetric[];
  recentRequests: PerformanceMetric[];
}

class PerformanceTracker {
  private metrics: PerformanceMetric[] = [];
  private readonly MAX_METRICS = 10000;
  private readonly SLOW_REQUEST_THRESHOLD = 5000; // 5 seconds
  private readonly VERY_SLOW_THRESHOLD = 10000; // 10 seconds
  private readonly CRITICAL_THRESHOLD = 15000; // 15 seconds

  /**
   * High-resolution performance middleware
   */
  middleware = (req: Request, res: Response, next: NextFunction) => {
    const startTime = process.hrtime.bigint();
    const startMemory = process.memoryUsage();

    // Capture request details
    const requestDetails = {
      method: req.method,
      url: req.url,
      userAgent: req.get("User-Agent"),
      userId: (req as any).user?.id,
      ip: req.ip || req.connection.remoteAddress,
    };

    res.on("finish", () => {
      const endTime = process.hrtime.bigint();
      const endMemory = process.memoryUsage();
      
      // Calculate duration in milliseconds with microsecond precision
      const duration = Number(endTime - startTime) / 1000000;

      const metric: PerformanceMetric = {
        ...requestDetails,
        statusCode: res.statusCode,
        duration,
        timestamp: Date.now(),
        memoryUsage: {
          rss: endMemory.rss - startMemory.rss,
          heapUsed: endMemory.heapUsed - startMemory.heapUsed,
          heapTotal: endMemory.heapTotal - startMemory.heapTotal,
          external: endMemory.external - startMemory.external,
          arrayBuffers: endMemory.arrayBuffers - startMemory.arrayBuffers,
        },
      };

      // Store metric
      this.recordMetric(metric);

      // Log performance with appropriate level
      this.logPerformance(metric);

      // Check for performance issues
      this.checkPerformanceThresholds(metric);
    });

    next();
  };

  /**
   * Record performance metric
   */
  private recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);

    // Keep metrics array manageable
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }
  }

  /**
   * Log performance with appropriate level
   */
  private logPerformance(metric: PerformanceMetric): void {
    const logData = {
      method: metric.method,
      url: metric.url,
      statusCode: metric.statusCode,
      duration: `${metric.duration.toFixed(2)}ms`,
      userAgent: metric.userAgent,
      userId: metric.userId,
      ip: metric.ip,
      memoryDelta: metric.memoryUsage ? {
        rss: `${(metric.memoryUsage.rss / 1024 / 1024).toFixed(2)}MB`,
        heapUsed: `${(metric.memoryUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`,
      } : undefined,
    };

    // Choose log level based on performance and status
    if (metric.statusCode >= 500) {
      logger.error("Request completed with server error", logData);
    } else if (metric.duration > this.CRITICAL_THRESHOLD) {
      logger.error("CRITICAL: Extremely slow request", logData);
    } else if (metric.duration > this.VERY_SLOW_THRESHOLD) {
      logger.warn("Very slow request detected", logData);
    } else if (metric.duration > this.SLOW_REQUEST_THRESHOLD) {
      logger.warn("Slow request detected", logData);
    } else if (metric.statusCode >= 400) {
      logger.warn("Request completed with client error", logData);
    } else {
      logger.info("Request completed", logData);
    }
  }

  /**
   * Check performance thresholds and trigger alerts
   */
  private checkPerformanceThresholds(metric: PerformanceMetric): void {
    if (metric.duration > this.CRITICAL_THRESHOLD) {
      this.triggerCriticalPerformanceAlert(metric);
    } else if (metric.duration > this.SLOW_REQUEST_THRESHOLD) {
      this.checkSlowRequestPattern();
    }
  }

  /**
   * Trigger critical performance alert
   */
  private triggerCriticalPerformanceAlert(metric: PerformanceMetric): void {
    logger.error("CRITICAL PERFORMANCE ALERT", {
      message: "Request exceeded critical performance threshold",
      threshold: `${this.CRITICAL_THRESHOLD}ms`,
      actualDuration: `${metric.duration.toFixed(2)}ms`,
      method: metric.method,
      url: metric.url,
      statusCode: metric.statusCode,
      userId: metric.userId,
      timestamp: new Date(metric.timestamp).toISOString(),
      recommendation: "Immediate investigation required",
    });

    // TODO: In production, this could trigger:
    // - PagerDuty alerts
    // - Slack notifications
    // - Auto-scaling triggers
    // - Circuit breaker activation
  }

  /**
   * Check for slow request patterns
   */
  private checkSlowRequestPattern(): void {
    const recentRequests = this.getRecentRequests(300000); // Last 5 minutes
    const slowRequests = recentRequests.filter(r => r.duration > this.SLOW_REQUEST_THRESHOLD);
    
    if (slowRequests.length > 10) {
      logger.warn("SLOW REQUEST PATTERN DETECTED", {
        message: "Multiple slow requests detected in recent period",
        timeWindow: "5 minutes",
        slowRequestCount: slowRequests.length,
        totalRequests: recentRequests.length,
        slowRequestPercentage: `${((slowRequests.length / recentRequests.length) * 100).toFixed(2)}%`,
        avgSlowDuration: `${(slowRequests.reduce((sum, r) => sum + r.duration, 0) / slowRequests.length).toFixed(2)}ms`,
        recommendation: "Check system resources and database performance",
      });
    }
  }

  /**
   * Get recent requests within time window
   */
  private getRecentRequests(timeWindowMs: number): PerformanceMetric[] {
    const cutoff = Date.now() - timeWindowMs;
    return this.metrics.filter(metric => metric.timestamp > cutoff);
  }

  /**
   * Get performance statistics
   */
  getStats(timeWindowMs?: number): PerformanceStats {
    const metricsToAnalyze = timeWindowMs 
      ? this.getRecentRequests(timeWindowMs)
      : this.metrics;

    if (metricsToAnalyze.length === 0) {
      return {
        totalRequests: 0,
        avgResponseTime: 0,
        slowRequests: 0,
        errorRequests: 0,
        requestsByMethod: {},
        requestsByStatus: {},
        slowestRequests: [],
        recentRequests: [],
      };
    }

    const totalDuration = metricsToAnalyze.reduce((sum, m) => sum + m.duration, 0);
    const slowRequests = metricsToAnalyze.filter(m => m.duration > this.SLOW_REQUEST_THRESHOLD);
    const errorRequests = metricsToAnalyze.filter(m => m.statusCode >= 400);

    const requestsByMethod: Record<string, number> = {};
    const requestsByStatus: Record<string, number> = {};

    metricsToAnalyze.forEach(metric => {
      requestsByMethod[metric.method] = (requestsByMethod[metric.method] || 0) + 1;
      requestsByStatus[metric.statusCode.toString()] = (requestsByStatus[metric.statusCode.toString()] || 0) + 1;
    });

    const slowestRequests = [...metricsToAnalyze]
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10);

    const recentRequests = [...metricsToAnalyze]
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 50);

    return {
      totalRequests: metricsToAnalyze.length,
      avgResponseTime: totalDuration / metricsToAnalyze.length,
      slowRequests: slowRequests.length,
      errorRequests: errorRequests.length,
      requestsByMethod,
      requestsByStatus,
      slowestRequests,
      recentRequests,
    };
  }

  /**
   * Get performance health status
   */
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    message: string;
    metrics: {
      avgResponseTime: number;
      slowRequestPercentage: number;
      errorRate: number;
      recentRequests: number;
    };
  } {
    const recentStats = this.getStats(300000); // Last 5 minutes
    
    if (recentStats.totalRequests === 0) {
      return {
        status: 'healthy',
        message: 'No recent requests to analyze',
        metrics: {
          avgResponseTime: 0,
          slowRequestPercentage: 0,
          errorRate: 0,
          recentRequests: 0,
        },
      };
    }

    const slowRequestPercentage = (recentStats.slowRequests / recentStats.totalRequests) * 100;
    const errorRate = (recentStats.errorRequests / recentStats.totalRequests) * 100;

    const metrics = {
      avgResponseTime: recentStats.avgResponseTime,
      slowRequestPercentage,
      errorRate,
      recentRequests: recentStats.totalRequests,
    };

    // Determine health status
    if (recentStats.avgResponseTime > this.VERY_SLOW_THRESHOLD || slowRequestPercentage > 20 || errorRate > 10) {
      return {
        status: 'critical',
        message: 'Critical performance issues detected',
        metrics,
      };
    }

    if (recentStats.avgResponseTime > this.SLOW_REQUEST_THRESHOLD || slowRequestPercentage > 10 || errorRate > 5) {
      return {
        status: 'warning',
        message: 'Performance degradation detected',
        metrics,
      };
    }

    return {
      status: 'healthy',
      message: 'Performance within normal parameters',
      metrics,
    };
  }

  /**
   * Clear old metrics
   */
  clearOldMetrics(olderThanMs: number = 24 * 60 * 60 * 1000): number {
    const cutoff = Date.now() - olderThanMs;
    const initialLength = this.metrics.length;
    this.metrics = this.metrics.filter(metric => metric.timestamp > cutoff);
    const cleared = initialLength - this.metrics.length;
    
    if (cleared > 0) {
      logger.info("Cleared old performance metrics", {
        cleared,
        remaining: this.metrics.length,
        cutoffAge: `${olderThanMs / 1000 / 60 / 60} hours`,
      });
    }
    
    return cleared;
  }

  /**
   * Get endpoint performance analysis
   */
  getEndpointAnalysis(): Record<string, {
    count: number;
    avgDuration: number;
    slowRequests: number;
    errorRate: number;
    p95Duration: number;
    p99Duration: number;
  }> {
    const endpointStats: Record<string, PerformanceMetric[]> = {};
    
    // Group by endpoint
    this.metrics.forEach(metric => {
      const endpoint = `${metric.method} ${metric.url}`;
      if (!endpointStats[endpoint]) {
        endpointStats[endpoint] = [];
      }
      endpointStats[endpoint].push(metric);
    });

    const analysis: Record<string, any> = {};
    
    Object.entries(endpointStats).forEach(([endpoint, metrics]) => {
      const durations = metrics.map(m => m.duration).sort((a, b) => a - b);
      const errorCount = metrics.filter(m => m.statusCode >= 400).length;
      const slowCount = metrics.filter(m => m.duration > this.SLOW_REQUEST_THRESHOLD).length;
      
      analysis[endpoint] = {
        count: metrics.length,
        avgDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
        slowRequests: slowCount,
        errorRate: (errorCount / metrics.length) * 100,
        p95Duration: durations[Math.floor(durations.length * 0.95)] || 0,
        p99Duration: durations[Math.floor(durations.length * 0.99)] || 0,
      };
    });

    return analysis;
  }
}

// Export singleton instance
export const performanceTracker = new PerformanceTracker();
export default performanceTracker;
