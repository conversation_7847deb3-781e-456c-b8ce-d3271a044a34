/**
 * @swagger
 * components:
 *   schemas:
 *     SystemHealth:
 *       type: object
 *       properties:
 *         overall:
 *           type: object
 *           properties:
 *             status:
 *               type: string
 *               enum: [healthy, warning, critical]
 *             timestamp:
 *               type: string
 *               format: date-time
 *         email:
 *           $ref: '#/components/schemas/EmailHealthStatus'
 *         retryService:
 *           $ref: '#/components/schemas/RetryServiceHealth'
 *         performance:
 *           $ref: '#/components/schemas/PerformanceHealth'
 *         timeouts:
 *           $ref: '#/components/schemas/TimeoutHealth'
 *         deadLetterQueue:
 *           $ref: '#/components/schemas/DLQHealth'
 *         uptime:
 *           type: number
 *           description: Server uptime in seconds
 *         memory:
 *           type: object
 *           properties:
 *             rss:
 *               type: number
 *             heapTotal:
 *               type: number
 *             heapUsed:
 *               type: number
 *             external:
 *               type: number
 *             arrayBuffers:
 *               type: number
 *
 *     EmailHealthStatus:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           enum: [healthy, warning, critical]
 *         details:
 *           type: string
 *         metrics:
 *           type: object
 *           properties:
 *             totalSent:
 *               type: number
 *             totalFailed:
 *               type: number
 *             successRate:
 *               type: number
 *
 *     EmailMetrics:
 *       type: object
 *       properties:
 *         metrics:
 *           type: object
 *           properties:
 *             totalSent:
 *               type: number
 *             totalFailed:
 *               type: number
 *             successRate:
 *               type: number
 *             avgResponseTime:
 *               type: number
 *             lastHourSent:
 *               type: number
 *             lastHourFailed:
 *               type: number
 *         performanceByType:
 *           type: object
 *           additionalProperties:
 *             type: object
 *             properties:
 *               count:
 *                 type: number
 *               avgDuration:
 *                 type: number
 *               successRate:
 *                 type: number
 *               lastSent:
 *                 type: string
 *                 format: date-time
 *         healthStatus:
 *           $ref: '#/components/schemas/EmailHealthStatus'
 *         timestamp:
 *           type: string
 *           format: date-time
 *
 *     PerformanceMetrics:
 *       type: object
 *       properties:
 *         stats:
 *           type: object
 *           properties:
 *             totalRequests:
 *               type: number
 *             avgResponseTime:
 *               type: number
 *             minResponseTime:
 *               type: number
 *             maxResponseTime:
 *               type: number
 *             slowRequests:
 *               type: number
 *             verySlowRequests:
 *               type: number
 *             requestsPerMinute:
 *               type: number
 *         healthStatus:
 *           type: object
 *           properties:
 *             status:
 *               type: string
 *               enum: [healthy, warning, critical]
 *             details:
 *               type: string
 *         endpointAnalysis:
 *           type: array
 *           items:
 *             type: array
 *             items:
 *               oneOf:
 *                 - type: string
 *                 - type: object
 *                   properties:
 *                     count:
 *                       type: number
 *                     avgDuration:
 *                       type: number
 *                     minDuration:
 *                       type: number
 *                     maxDuration:
 *                       type: number
 *                     slowRequests:
 *                       type: number
 *                     lastAccessed:
 *                       type: string
 *                       format: date-time
 *         timestamp:
 *           type: string
 *           format: date-time
 *
 *     TimeoutAnalytics:
 *       type: object
 *       properties:
 *         stats:
 *           type: object
 *           properties:
 *             totalTimeouts:
 *               type: number
 *             timeoutRate:
 *               type: number
 *             avgTimeoutDuration:
 *               type: number
 *             timeoutsLastHour:
 *               type: number
 *         timeoutsByType:
 *           type: object
 *           additionalProperties:
 *             type: object
 *             properties:
 *               count:
 *                 type: number
 *               avgDuration:
 *                 type: number
 *               lastOccurred:
 *                 type: string
 *                 format: date-time
 *         trends:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               timestamp:
 *                 type: string
 *                 format: date-time
 *               count:
 *                 type: number
 *               type:
 *                 type: string
 *         healthStatus:
 *           type: object
 *           properties:
 *             status:
 *               type: string
 *               enum: [healthy, warning, critical]
 *             details:
 *               type: string
 *         timestamp:
 *           type: string
 *           format: date-time
 *
 *     DLQStats:
 *       type: object
 *       properties:
 *         stats:
 *           type: object
 *           properties:
 *             totalEntries:
 *               type: number
 *             pendingEntries:
 *               type: number
 *             processingEntries:
 *               type: number
 *             resolvedEntries:
 *               type: number
 *             failedPermanently:
 *               type: number
 *             cancelledEntries:
 *               type: number
 *             avgRetryAttempts:
 *               type: number
 *             oldestPendingAge:
 *               type: number
 *             entriesLast24h:
 *               type: number
 *             successfulRetriesLast24h:
 *               type: number
 *             byEmailType:
 *               type: object
 *               additionalProperties:
 *                 type: number
 *             byFailureReason:
 *               type: object
 *               additionalProperties:
 *                 type: number
 *             byPriority:
 *               type: object
 *               additionalProperties:
 *                 type: number
 *         healthStatus:
 *           type: object
 *           properties:
 *             status:
 *               type: string
 *               enum: [healthy, warning, critical]
 *             details:
 *               type: string
 *             thresholds:
 *               type: object
 *               properties:
 *                 warningThreshold:
 *                   type: number
 *                 criticalThreshold:
 *                   type: number
 *         timestamp:
 *           type: string
 *           format: date-time
 *
 *     DLQEntry:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         emailType:
 *           type: string
 *         recipientEmail:
 *           type: string
 *         subject:
 *           type: string
 *         failureReason:
 *           type: string
 *         priority:
 *           type: number
 *         retryAttempts:
 *           type: number
 *         maxRetryAttempts:
 *           type: number
 *         status:
 *           type: string
 *           enum: [pending, processing, resolved, failed_permanently, cancelled]
 *         createdAt:
 *           type: string
 *           format: date-time
 *         lastRetryAt:
 *           type: string
 *           format: date-time
 *         nextRetryAt:
 *           type: string
 *           format: date-time
 *         processingNode:
 *           type: string
 *         userId:
 *           type: string
 *         errorDetails:
 *           type: string
 *
 *     ApiResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           enum: [success, error]
 *         message:
 *           type: string
 *         data:
 *           type: object
 *
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           enum: [error]
 *         message:
 *           type: string
 *         error:
 *           type: string
 */

// This file contains Swagger/OpenAPI documentation for monitoring endpoints
export {};
