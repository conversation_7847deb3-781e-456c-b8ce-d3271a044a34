import cron from "node-cron";
import { deadLetterQueueService } from "../services/deadLetterQueue.service";
import logger from "../utils/logger";

/**
 * Dead Letter Queue Processor Job
 * 
 * Scheduled job that automatically processes retryable entries in the dead letter queue
 * and performs maintenance tasks like cleaning up old entries and resetting stale processing entries.
 */

class DLQProcessorJob {
  private isProcessing = false;
  private readonly RETRY_SCHEDULE = "*/5 * * * *"; // Every 5 minutes
  private readonly CLEANUP_SCHEDULE = "0 2 * * *"; // Daily at 2 AM
  private readonly STALE_RESET_SCHEDULE = "*/10 * * * *"; // Every 10 minutes

  /**
   * Start all DLQ processing jobs
   */
  start(): void {
    this.startRetryProcessor();
    this.startCleanupJob();
    this.startStaleResetJob();
    
    logger.info("DLQ processor jobs started", {
      retrySchedule: this.RETRY_SCHEDULE,
      cleanupSchedule: this.CLEANUP_SCHEDULE,
      staleResetSchedule: this.STALE_RESET_SCHEDULE,
    });
  }

  /**
   * Start the retry processor job
   */
  private startRetryProcessor(): void {
    cron.schedule(this.RETRY_SCHEDULE, async () => {
      if (this.isProcessing) {
        logger.debug("DLQ retry processor already running, skipping");
        return;
      }

      this.isProcessing = true;
      
      try {
        const result = await deadLetterQueueService.processRetryableEntries();
        
        if (result.processed > 0) {
          logger.info("DLQ retry processing completed", {
            processed: result.processed,
            successful: result.successful,
            failed: result.failed,
            successRate: result.processed > 0 ? (result.successful / result.processed * 100).toFixed(1) + '%' : '0%',
          });
        }

        // Log warning if too many failures
        if (result.failed > result.successful && result.processed > 0) {
          logger.warn("DLQ retry processing has high failure rate", {
            processed: result.processed,
            successful: result.successful,
            failed: result.failed,
            failureRate: (result.failed / result.processed * 100).toFixed(1) + '%',
          });
        }

      } catch (error: any) {
        logger.error("DLQ retry processing failed", {
          error: error.message,
          stack: error.stack,
        });
      } finally {
        this.isProcessing = false;
      }
    }, {
      scheduled: true,
      timezone: "Africa/Nairobi", // Kenya timezone
    });
  }

  /**
   * Start the cleanup job for old resolved/cancelled entries
   */
  private startCleanupJob(): void {
    cron.schedule(this.CLEANUP_SCHEDULE, async () => {
      try {
        const deletedCount = await deadLetterQueueService.cleanup(30); // Clean entries older than 30 days
        
        if (deletedCount > 0) {
          logger.info("DLQ cleanup completed", {
            deletedCount,
            olderThanDays: 30,
          });
        }

      } catch (error: any) {
        logger.error("DLQ cleanup failed", {
          error: error.message,
          stack: error.stack,
        });
      }
    }, {
      scheduled: true,
      timezone: "Africa/Nairobi",
    });
  }

  /**
   * Start the stale entry reset job
   */
  private startStaleResetJob(): void {
    cron.schedule(this.STALE_RESET_SCHEDULE, async () => {
      try {
        const resetCount = await deadLetterQueueService.resetStaleEntries();
        
        if (resetCount > 0) {
          logger.warn("Reset stale DLQ processing entries", {
            resetCount,
            staleThresholdMinutes: 30,
          });
        }

      } catch (error: any) {
        logger.error("DLQ stale reset failed", {
          error: error.message,
          stack: error.stack,
        });
      }
    }, {
      scheduled: true,
      timezone: "Africa/Nairobi",
    });
  }

  /**
   * Process DLQ entries manually (for testing or immediate processing)
   */
  async processNow(): Promise<{
    processed: number;
    successful: number;
    failed: number;
  }> {
    if (this.isProcessing) {
      throw new Error("DLQ processing is already in progress");
    }

    this.isProcessing = true;
    
    try {
      logger.info("Manual DLQ processing started");
      const result = await deadLetterQueueService.processRetryableEntries();
      
      logger.info("Manual DLQ processing completed", {
        processed: result.processed,
        successful: result.successful,
        failed: result.failed,
      });

      return result;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Get processing status
   */
  getStatus(): {
    isProcessing: boolean;
    schedules: {
      retry: string;
      cleanup: string;
      staleReset: string;
    };
  } {
    return {
      isProcessing: this.isProcessing,
      schedules: {
        retry: this.RETRY_SCHEDULE,
        cleanup: this.CLEANUP_SCHEDULE,
        staleReset: this.STALE_RESET_SCHEDULE,
      },
    };
  }

  /**
   * Stop all scheduled jobs (for graceful shutdown)
   */
  stop(): void {
    cron.getTasks().forEach((task) => {
      task.stop();
    });
    
    logger.info("DLQ processor jobs stopped");
  }
}

// Export singleton instance
export const dlqProcessorJob = new DLQProcessorJob();
export default dlqProcessorJob;
