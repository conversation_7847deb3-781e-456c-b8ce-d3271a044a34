import { AppDataSource } from "../config/db.config";
import { User } from "../entities/User.entity";

/**
 * Migration script to update existing admin users to new role system
 * 
 * This script:
 * 1. Identifies existing admin users
 * 2. Prompts for role assignment (business_admin or technical_admin)
 * 3. Updates user roles accordingly
 * 
 * Run with: npm run migrate:roles
 */

interface RoleUpdateChoice {
  userId: string;
  email: string;
  currentRole: string;
  newRole: 'business_admin' | 'technical_admin' | 'admin';
}

async function updateAdminRoles() {
  try {
    console.log("🔄 Starting admin role migration...");
    
    // Initialize database connection
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log("✅ Database connection established");
    }

    const userRepository = AppDataSource.getRepository(User);
    
    // Find all existing admin users
    const adminUsers = await userRepository.find({
      where: { role: 'admin' },
      select: ['id', 'email', 'name', 'role', 'createdAt']
    });

    if (adminUsers.length === 0) {
      console.log("ℹ️  No existing admin users found. Migration complete.");
      return;
    }

    console.log(`\n📋 Found ${adminUsers.length} admin user(s):`);
    adminUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email} (${user.name}) - Created: ${user.createdAt.toLocaleDateString()}`);
    });

    console.log("\n🎯 Role Assignment Options:");
    console.log("1. business_admin - Manages users, orders, disputes, business operations");
    console.log("2. technical_admin - Manages system monitoring, logs, technical issues");
    console.log("3. admin - Keep as super admin (full access)");

    // For automated migration, we'll keep the first admin as super admin
    // and convert others to business_admin by default
    const updates: RoleUpdateChoice[] = [];
    
    adminUsers.forEach((user, index) => {
      if (index === 0) {
        // Keep first admin as super admin
        updates.push({
          userId: user.id,
          email: user.email,
          currentRole: user.role,
          newRole: 'admin'
        });
      } else {
        // Convert additional admins to business_admin by default
        updates.push({
          userId: user.id,
          email: user.email,
          currentRole: user.role,
          newRole: 'business_admin'
        });
      }
    });

    console.log("\n📝 Planned role updates:");
    updates.forEach(update => {
      console.log(`${update.email}: ${update.currentRole} → ${update.newRole}`);
    });

    // Apply updates
    console.log("\n🔄 Applying role updates...");
    
    for (const update of updates) {
      await userRepository.update(
        { id: update.userId },
        { role: update.newRole }
      );
      console.log(`✅ Updated ${update.email} to ${update.newRole}`);
    }

    console.log("\n🎉 Admin role migration completed successfully!");
    console.log("\nℹ️  Summary:");
    console.log(`- Super Admins: ${updates.filter(u => u.newRole === 'admin').length}`);
    console.log(`- Business Admins: ${updates.filter(u => u.newRole === 'business_admin').length}`);
    console.log(`- Technical Admins: ${updates.filter(u => u.newRole === 'technical_admin').length}`);

  } catch (error) {
    console.error("❌ Migration failed:", error);
    throw error;
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log("🔌 Database connection closed");
    }
  }
}

/**
 * Interactive role assignment (for manual use)
 */
async function interactiveRoleAssignment() {
  // This would require readline for interactive prompts
  // For now, we'll use the automated approach above
  console.log("Interactive mode not implemented yet. Using automated assignment.");
  await updateAdminRoles();
}

// Run migration if called directly
if (require.main === module) {
  updateAdminRoles()
    .then(() => {
      console.log("Migration completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Migration failed:", error);
      process.exit(1);
    });
}

export { updateAdminRoles, interactiveRoleAssignment };
