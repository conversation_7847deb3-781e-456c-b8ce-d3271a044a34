{"name": "marketplace-backend", "version": "1.0.0", "main": "index.js", "scripts": {"typeorm": "typeorm-ts-node-commonjs", "migration:run": "ts-node ./node_modules/typeorm/cli.js migration:run -d ./src/config/db.config.ts", "migrations:revert": "ts-node ./node_modules/typeorm/cli.js migration:revert -d ./src/config/db.config.ts", "migration:show": "pnpm run typeorm migration show -- -d ./src/config/db.config.ts", "migration:generate": "pnpm run typeorm migration:generate -d ./src/config/db.config.ts", "migration:create": "pnpm run typeorm migrations:create", "schema:sync": "pnpm run typeorm schema:sync -- -d ./src/config/db.config.ts", "migration:generate:docker": "docker exec -it marketplace-api pnpm run migration:generate", "migration:run:docker": "docker exec -it marketplace-api pnpm run migration:run", "migration:revert:docker": "docker exec -it marketplace-api pnpm run migrations:revert", "dev": "ts-node-dev --respawn --transpile-only src/main.ts", "build": "tsc", "start": "node dist/main.js", "lint": "eslint . --ext .ts", "format": "prettier --write .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:timeout-monitoring": "ts-node src/scripts/test-timeout-monitoring.ts", "seed": "ts-node src/seeders/index.ts", "seed:categories": "ts-node src/seeders/category.seeder.ts", "db:check": "ts-node src/scripts/check-db.ts", "migrate:roles": "ts-node src/migrations/update-admin-roles.ts", "docker:up": "docker-compose up --build", "docker:down": "docker-compose down"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "class-validator": "^0.14.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "fuse.js": "^7.1.0", "json2csv": "6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.0", "node-cron": "^4.1.1", "nodemailer": "^7.0.3", "pg": "^8.16.0", "redis": "^5.1.0", "reflect-metadata": "^0.2.2", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.24", "winston": "^3.17.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/express-serve-static-core": "^5.0.6", "@types/jest": "^29.5.0", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.21", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/supertest": "^2.0.12", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.27.0", "jest": "^29.5.0", "prettier": "^3.5.3", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}