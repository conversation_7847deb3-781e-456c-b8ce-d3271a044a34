import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";
import { apiSlice } from "./api/apiSlice";
import { inventoryApi, adminInventoryApi } from "./api/inventory";
import { chatApi } from "./api/chat";
import { usersApi } from "./api/users";
import { monitoringApi } from "./api/monitoringApi";
import authReducer from "./features/auth/authSlice";
import notificationReducer from "./features/notifications/notificationSlice";

export const store = configureStore({
  reducer: {
    // Add the generated reducer as a specific top-level slice
    [apiSlice.reducerPath]: apiSlice.reducer,
    [inventoryApi.reducerPath]: inventoryApi.reducer,
    [adminInventoryApi.reducerPath]: adminInventoryApi.reducer,
    [chatApi.reducerPath]: chatApi.reducer,
    [usersApi.reducerPath]: usersApi.reducer,
    [monitoringApi.reducerPath]: monitoringApi.reducer,
    auth: authReducer,
    notifications: notificationReducer,
  },
  // Adding the api middleware enables caching, invalidation, polling,
  // and other useful features of `rtk-query`.
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      apiSlice.middleware,
      inventoryApi.middleware,
      adminInventoryApi.middleware,
      chatApi.middleware,
      usersApi.middleware,
      monitoringApi.middleware
    ),
});

// optional, but required for refetchOnFocus/refetchOnReconnect behaviors
// see `setupListeners` docs - takes an optional callback as the 2nd arg for customization
setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
