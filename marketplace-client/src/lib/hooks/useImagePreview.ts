import { useEffect, useState } from 'react';

export function useImagePreview(file: File | null) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!file) {
      setPreviewUrl(null);
      setError(null);
      setIsLoading(false);
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('File is not an image');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      setIsLoading(false);

      // Cleanup function
      return () => {
        URL.revokeObjectURL(url);
      };
    } catch (err) {
      console.error('Error creating object URL:', err);
      setError('Failed to create preview');
      setIsLoading(false);
    }
  }, [file]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  return { previewUrl, isLoading, error };
}
