import { useState, useCallback } from 'react'
import { AxiosError, AxiosResponse } from 'axios'
import axiosInstance from '../api/axiosInstance'

interface UseApiState<T> {
  data: T | null
  loading: boolean
  error: string | null
}

interface UseApiReturn<T> extends UseApiState<T> {
  execute: (...args: any[]) => Promise<T>
  reset: () => void
}

export function useApi<T = any>(
  apiFunction: (...args: any[]) => Promise<AxiosResponse<T>>
): UseApiReturn<T> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  })

  const execute = useCallback(
    async (...args: any[]): Promise<T> => {
      setState((prev) => ({ ...prev, loading: true, error: null }))

      try {
        const response = await apiFunction(...args)
        const data = response.data
        setState({ data, loading: false, error: null })
        return data
      } catch (error) {
        const errorMessage = error instanceof AxiosError 
          ? error.response?.data?.message || error.message 
          : 'An error occurred'
        
        setState((prev) => ({ 
          ...prev, 
          loading: false, 
          error: errorMessage 
        }))
        throw error
      }
    },
    [apiFunction]
  )

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null })
  }, [])

  return {
    ...state,
    execute,
    reset,
  }
}

// Convenience hook for GET requests
export function useApiGet<T = any>(url: string) {
  return useApi<T>(() => axiosInstance.get(url))
}

// Convenience hook for POST requests
export function useApiPost<T = any>(url: string) {
  return useApi<T>((data: any) => axiosInstance.post(url, data))
}

// Convenience hook for PUT requests
export function useApiPut<T = any>(url: string) {
  return useApi<T>((data: any) => axiosInstance.put(url, data))
}

// Convenience hook for DELETE requests
export function useApiDelete<T = any>(url: string) {
  return useApi<T>(() => axiosInstance.delete(url))
}
