import { useDispatch } from 'react-redux'
import { addNotification, type NotificationType } from '../features/notifications/notificationSlice'

interface NotificationOptions {
  title?: string
  message: string
  duration?: number
}

export function useNotifications() {
  const dispatch = useDispatch()

  const notify = (type: NotificationType, options: NotificationOptions) => {
    dispatch(
      addNotification({
        type,
        title: options.title,
        message: options.message,
        duration: options.duration,
      })
    )
  }

  return {
    success: (message: string, title?: string, duration?: number) =>
      notify('success', { message, title, duration }),
    
    error: (message: string, title?: string, duration?: number) =>
      notify('error', { message, title, duration }),
    
    warning: (message: string, title?: string, duration?: number) =>
      notify('warning', { message, title, duration }),
    
    info: (message: string, title?: string, duration?: number) =>
      notify('info', { message, title, duration }),

    // Convenience methods
    apiError: (error: any) => {
      const message = error?.response?.data?.message || error?.message || 'An error occurred'
      notify('error', { message, title: 'API Error' })
    },

    validationError: (message: string) =>
      notify('error', { message, title: 'Validation Error' }),

    networkError: () =>
      notify('error', { 
        message: 'Please check your internet connection and try again.',
        title: 'Network Error'
      }),

    loginSuccess: () =>
      notify('success', { message: 'Welcome back!', title: 'Login Successful' }),

    logoutSuccess: () =>
      notify('info', { 
        message: 'You have been logged out successfully.',
        title: 'Logged Out'
      }),

    saveSuccess: () =>
      notify('success', { 
        message: 'Your changes have been saved successfully.',
        title: 'Saved'
      }),

    deleteSuccess: () =>
      notify('success', { message: 'Item deleted successfully.', title: 'Deleted' }),

    copySuccess: () =>
      notify('success', { message: 'Copied to clipboard!', title: 'Copied' }),
  }
}
