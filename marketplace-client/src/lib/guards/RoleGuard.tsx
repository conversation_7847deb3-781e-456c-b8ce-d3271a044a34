'use client'

import { useAuth } from '@/lib/contexts/AuthContext'
import type { UserRole } from '@/lib/features/auth/authSlice'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { ShieldX, ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface RoleGuardProps {
  children: React.ReactNode
  allowedRoles: UserRole | UserRole[]
  fallback?: React.ReactNode
  redirectTo?: string
}

export function RoleGuard({ 
  children, 
  allowedRoles, 
  fallback,
  redirectTo 
}: RoleGuardProps) {
  const { user, hasRole } = useAuth()
  const router = useRouter()

  const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles]

  if (!user || !hasRole(allowedRoles)) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <div className="flex items-center justify-center min-h-[400px] p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <ShieldX className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don't have permission to access this page.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground text-center">
              <p>Required role(s): {roles.join(', ')}</p>
              {user && <p>Your role: {user.role}</p>}
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => router.back()}
                className="flex-1"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
              <Button 
                onClick={() => {
                  if (redirectTo) {
                    router.push(redirectTo)
                  } else {
                    // Redirect based on user role
                    switch (user?.role) {
                      case 'admin':
                        router.push('/admin/dashboard')
                        break
                      case 'seller':
                        router.push('/seller/dashboard')
                        break
                      case 'buyer':
                        router.push('/products')
                        break
                      default:
                        router.push('/')
                    }
                  }
                }}
                className="flex-1"
              >
                Go to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return <>{children}</>
}

// Convenience components for specific roles
export function AdminGuard({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles="admin" fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function SellerGuard({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['admin', 'seller']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function BuyerGuard({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['admin', 'seller', 'buyer']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}
