'use client'

import { useAuth } from '@/lib/contexts/AuthContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Lock, ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface PermissionGuardProps {
  children: React.ReactNode
  permission: string | string[]
  fallback?: React.ReactNode
  requireAll?: boolean // If true, user must have ALL permissions. If false, user needs ANY permission
}

export function PermissionGuard({ 
  children, 
  permission, 
  fallback,
  requireAll = false 
}: PermissionGuardProps) {
  const { user, hasPermission } = useAuth()
  const router = useRouter()

  const permissions = Array.isArray(permission) ? permission : [permission]
  
  const hasAccess = requireAll 
    ? permissions.every(p => hasPermission(p))
    : permissions.some(p => hasPermission(p))

  if (!user || !hasAccess) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <div className="flex items-center justify-center min-h-[400px] p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-orange-100">
              <Lock className="h-6 w-6 text-orange-600" />
            </div>
            <CardTitle>Insufficient Permissions</CardTitle>
            <CardDescription>
              You don't have the required permissions to access this feature.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground text-center">
              <p>Required permission(s): {permissions.join(', ')}</p>
              {user && <p>Your role: {user.role}</p>}
            </div>
            <Button 
              variant="outline" 
              onClick={() => router.back()}
              className="w-full"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return <>{children}</>
}

// Hook for conditional rendering based on permissions
export function usePermissions() {
  const { hasPermission } = useAuth()
  
  return {
    hasPermission,
    canView: (resource: string) => hasPermission(`view:${resource}`),
    canManage: (resource: string) => hasPermission(`manage:${resource}`),
    canCreate: (resource: string) => hasPermission(`create:${resource}`),
    canEdit: (resource: string) => hasPermission(`edit:${resource}`),
    canDelete: (resource: string) => hasPermission(`delete:${resource}`),
  }
}
