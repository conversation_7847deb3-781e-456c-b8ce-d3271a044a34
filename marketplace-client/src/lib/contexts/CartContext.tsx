"use client";

import React, { createContext, useContext, useReducer, useEffect } from "react";
import { useAuth } from "./AuthContext";

// Types
export interface CartItem {
  id: string;
  product: {
    id: string;
    title: string;
    price: number;
    images: string[];
    quantity: number; // Available stock
    seller: {
      id: string;
      name: string;
    };
  };
  quantity: number;
  createdAt: string;
}

interface CartState {
  items: CartItem[];
  isLoading: boolean;
  error: string | null;
  total: number;
  itemCount: number;
}

type CartAction =
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_ERROR"; payload: string | null }
  | { type: "SET_ITEMS"; payload: CartItem[] }
  | { type: "ADD_ITEM"; payload: CartItem }
  | { type: "UPDATE_ITEM"; payload: { id: string; quantity: number } }
  | {
      type: "OPTIMISTIC_UPDATE_ITEM";
      payload: { id: string; quantity: number };
    }
  | { type: "REMOVE_ITEM"; payload: string }
  | { type: "CLEAR_CART" };

interface CartContextType extends CartState {
  addToCart: (productId: string, quantity?: number) => Promise<void>;
  updateCartItem: (itemId: string, quantity: number) => Promise<void>;
  optimisticUpdateItem: (itemId: string, quantity: number) => void;
  removeFromCart: (itemId: string) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

// Cart reducer
function cartReducer(state: CartState, action: CartAction): CartState {
  switch (action.type) {
    case "SET_LOADING":
      return { ...state, isLoading: action.payload };

    case "SET_ERROR":
      return { ...state, error: action.payload, isLoading: false };

    case "SET_ITEMS":
      const items = action.payload;
      const total = items.reduce(
        (sum, item) => sum + item.product.price * item.quantity,
        0
      );
      const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);
      return {
        ...state,
        items,
        total,
        itemCount,
        isLoading: false,
        error: null,
      };

    case "ADD_ITEM":
      const newItems = [...state.items, action.payload];
      const newTotal = newItems.reduce(
        (sum, item) => sum + item.product.price * item.quantity,
        0
      );
      const newItemCount = newItems.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      return {
        ...state,
        items: newItems,
        total: newTotal,
        itemCount: newItemCount,
      };

    case "UPDATE_ITEM":
      const updatedItems = state.items.map((item) =>
        item.id === action.payload.id
          ? { ...item, quantity: action.payload.quantity }
          : item
      );
      const updatedTotal = updatedItems.reduce(
        (sum, item) => sum + item.product.price * item.quantity,
        0
      );
      const updatedItemCount = updatedItems.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      return {
        ...state,
        items: updatedItems,
        total: updatedTotal,
        itemCount: updatedItemCount,
      };

    case "OPTIMISTIC_UPDATE_ITEM":
      // Same as UPDATE_ITEM but for optimistic updates
      const optimisticUpdatedItems = state.items.map((item) =>
        item.id === action.payload.id
          ? { ...item, quantity: action.payload.quantity }
          : item
      );
      const optimisticUpdatedTotal = optimisticUpdatedItems.reduce(
        (sum, item) => sum + item.product.price * item.quantity,
        0
      );
      const optimisticUpdatedItemCount = optimisticUpdatedItems.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      return {
        ...state,
        items: optimisticUpdatedItems,
        total: optimisticUpdatedTotal,
        itemCount: optimisticUpdatedItemCount,
      };

    case "REMOVE_ITEM":
      const filteredItems = state.items.filter(
        (item) => item.id !== action.payload
      );
      const filteredTotal = filteredItems.reduce(
        (sum, item) => sum + item.product.price * item.quantity,
        0
      );
      const filteredItemCount = filteredItems.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      return {
        ...state,
        items: filteredItems,
        total: filteredTotal,
        itemCount: filteredItemCount,
      };

    case "CLEAR_CART":
      return {
        ...state,
        items: [],
        total: 0,
        itemCount: 0,
      };

    default:
      return state;
  }
}

// Initial state
const initialState: CartState = {
  items: [],
  isLoading: false,
  error: null,
  total: 0,
  itemCount: 0,
};

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(cartReducer, initialState);
  const { isAuthenticated, token } = useAuth();

  // API base URL
  const API_BASE =
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

  // Fetch cart items
  const refreshCart = async () => {
    if (!isAuthenticated || !token) {
      dispatch({ type: "CLEAR_CART" });
      return;
    }

    dispatch({ type: "SET_LOADING", payload: true });

    try {
      const response = await fetch(`${API_BASE}/cart/get-all-items`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch cart items");
      }

      const data = await response.json();
      console.log("Cart data received:", data);
      dispatch({ type: "SET_ITEMS", payload: data.cartItems || [] });
    } catch (error) {
      console.error("Error fetching cart:", error);
      dispatch({ type: "SET_ERROR", payload: "Failed to load cart" });
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  // Add item to cart
  const addToCart = async (productId: string, quantity: number = 1) => {
    if (!isAuthenticated || !token) {
      throw new Error("Please log in to add items to cart");
    }

    dispatch({ type: "SET_LOADING", payload: true });

    try {
      const response = await fetch(`${API_BASE}/cart/add-to`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ productId, quantity }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to add item to cart");
      }

      const addResult = await response.json();
      console.log("Add to cart result:", addResult);

      // Refresh cart to get updated data
      await refreshCart();
    } catch (error) {
      console.error("Error adding to cart:", error);
      dispatch({
        type: "SET_ERROR",
        payload:
          error instanceof Error ? error.message : "Failed to add item to cart",
      });
      throw error;
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  // Update cart item quantity
  const updateCartItem = async (itemId: string, quantity: number) => {
    if (!isAuthenticated || !token) return;

    try {
      const response = await fetch(`${API_BASE}/cart/update/${itemId}`, {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ quantity }),
      });

      if (!response.ok) {
        throw new Error("Failed to update cart item");
      }

      dispatch({ type: "UPDATE_ITEM", payload: { id: itemId, quantity } });
    } catch (error) {
      console.error("Error updating cart item:", error);
      dispatch({ type: "SET_ERROR", payload: "Failed to update cart item" });
    }
  };

  // Remove item from cart
  const removeFromCart = async (itemId: string) => {
    if (!isAuthenticated || !token) return;

    try {
      const response = await fetch(`${API_BASE}/cart/remove/${itemId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to remove cart item");
      }

      dispatch({ type: "REMOVE_ITEM", payload: itemId });
    } catch (error) {
      console.error("Error removing cart item:", error);
      dispatch({ type: "SET_ERROR", payload: "Failed to remove cart item" });
    }
  };

  // Clear entire cart
  const clearCart = async () => {
    if (!isAuthenticated || !token) return;

    try {
      const response = await fetch(`${API_BASE}/cart/clear`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to clear cart");
      }

      dispatch({ type: "CLEAR_CART" });
    } catch (error) {
      console.error("Error clearing cart:", error);
      dispatch({ type: "SET_ERROR", payload: "Failed to clear cart" });
    }
  };

  // Load cart on auth change
  useEffect(() => {
    if (isAuthenticated) {
      refreshCart();
    } else {
      dispatch({ type: "CLEAR_CART" });
    }
  }, [isAuthenticated]);

  // Optimistic update for immediate UI feedback
  const optimisticUpdateItem = (itemId: string, quantity: number) => {
    dispatch({
      type: "OPTIMISTIC_UPDATE_ITEM",
      payload: { id: itemId, quantity },
    });
  };

  const value: CartContextType = {
    ...state,
    addToCart,
    updateCartItem,
    optimisticUpdateItem,
    removeFromCart,
    clearCart,
    refreshCart,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error("useCart must be used within a CartProvider");
  }
  return context;
}
