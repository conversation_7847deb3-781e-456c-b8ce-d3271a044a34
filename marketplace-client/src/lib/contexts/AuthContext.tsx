"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCurrentUser,
  selectCurrentToken,
  selectIsAuthenticated,
  selectAuthLoading,
  setCredentials,
  logOut,
  setLoading,
  type User,
  type UserRole,
} from "@/lib/features/auth/authSlice";
import {
  useLoginMutation,
  useRegisterMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
} from "@/lib/api/authApi";

interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isInitialized: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (
    email: string,
    password: string,
    name: string,
    role: UserRole
  ) => Promise<void>;
  logout: () => void;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (
    email: string,
    otp: string,
    newPassword: string
  ) => Promise<void>;
  hasRole: (role: UserRole | UserRole[]) => boolean;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const dispatch = useDispatch();
  const user = useSelector(selectCurrentUser);
  const token = useSelector(selectCurrentToken);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const isLoading = useSelector(selectAuthLoading);
  const [isInitialized, setIsInitialized] = useState(false);

  // API mutation hooks
  const [loginMutation] = useLoginMutation();
  const [registerMutation] = useRegisterMutation();
  const [forgotPasswordMutation] = useForgotPasswordMutation();
  const [resetPasswordMutation] = useResetPasswordMutation();

  // Initialize auth state from localStorage on mount
  useEffect(() => {
    const initializeAuth = async () => {
      dispatch(setLoading(true));

      try {
        const token = localStorage.getItem("token");
        const refreshToken = localStorage.getItem("refreshToken");
        const userData = localStorage.getItem("user");

        if (token && refreshToken && userData) {
          const user = JSON.parse(userData);

          // Check if token is expired (basic check)
          try {
            const tokenPayload = JSON.parse(atob(token.split(".")[1]));
            const currentTime = Date.now() / 1000;

            if (tokenPayload.exp && tokenPayload.exp < currentTime) {
              // Token is expired, clear auth data
              localStorage.removeItem("token");
              localStorage.removeItem("refreshToken");
              localStorage.removeItem("user");
              dispatch(logOut());
            } else {
              dispatch(setCredentials({ user, token, refreshToken }));
            }
          } catch (tokenError) {
            // If token parsing fails, treat as invalid
            localStorage.removeItem("token");
            localStorage.removeItem("refreshToken");
            localStorage.removeItem("user");
            dispatch(logOut());
          }
        }
      } catch (error) {
        console.error("Failed to initialize auth:", error);
        // Clear invalid data
        localStorage.removeItem("token");
        localStorage.removeItem("refreshToken");
        localStorage.removeItem("user");
        dispatch(logOut());
      } finally {
        dispatch(setLoading(false));
        setIsInitialized(true);
      }
    };

    initializeAuth();
  }, [dispatch]);

  // Persist auth state to localStorage
  useEffect(() => {
    if (isAuthenticated && user) {
      localStorage.setItem("user", JSON.stringify(user));
    } else {
      localStorage.removeItem("token");
      localStorage.removeItem("refreshToken");
      localStorage.removeItem("user");
    }
  }, [isAuthenticated, user]);

  const login = async (email: string, password: string) => {
    dispatch(setLoading(true));

    try {
      const response = await loginMutation({ email, password }).unwrap();

      localStorage.setItem("token", response.accessToken);
      localStorage.setItem("refreshToken", response.refreshToken);

      dispatch(
        setCredentials({
          user: response.user,
          token: response.accessToken,
          refreshToken: response.refreshToken,
        })
      );
    } catch (error: any) {
      throw new Error(error?.data?.message || "Login failed");
    } finally {
      dispatch(setLoading(false));
    }
  };

  const register = async (
    email: string,
    password: string,
    name: string,
    role: UserRole
  ) => {
    dispatch(setLoading(true));

    try {
      const response = await registerMutation({
        email,
        password,
        name,
        role,
      }).unwrap();
    } catch (error: any) {
      throw new Error(
        error?.data?.error || error?.data?.message || "Registration failed"
      );
    } finally {
      dispatch(setLoading(false));
    }
  };

  const logout = () => {
    dispatch(logOut());
  };

  const forgotPassword = async (email: string) => {
    try {
      await forgotPasswordMutation({ email }).unwrap();
    } catch (error: any) {
      throw new Error(error?.data?.message || "Failed to send reset email");
    }
  };

  const resetPassword = async (
    email: string,
    otp: string,
    newPassword: string
  ) => {
    try {
      await resetPasswordMutation({ email, otp, newPassword }).unwrap();
    } catch (error: any) {
      throw new Error(error?.data?.message || "Failed to reset password");
    }
  };

  const hasRole = (role: UserRole | UserRole[]): boolean => {
    if (!user) return false;

    const roles = Array.isArray(role) ? role : [role];
    return roles.includes(user.role);
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;

    // Define role-based permissions
    const rolePermissions: Record<UserRole, string[]> = {
      admin: [
        "view:admin",
        "view:seller",
        "view:buyer",
        "manage:users",
        "manage:products",
        "manage:orders",
        "manage:categories",
        "view:analytics",
      ],
      seller: [
        "view:seller",
        "view:buyer",
        "manage:own-products",
        "view:own-orders",
        "view:own-analytics",
      ],
      buyer: [
        "view:buyer",
        "view:products",
        "manage:own-orders",
        "manage:cart",
      ],
    };

    return rolePermissions[user.role]?.includes(permission) || false;
  };

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated,
    isLoading,
    isInitialized,
    login,
    register,
    logout,
    forgotPassword,
    resetPassword,
    hasRole,
    hasPermission,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
