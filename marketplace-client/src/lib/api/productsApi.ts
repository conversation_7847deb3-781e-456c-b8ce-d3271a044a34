import { apiSlice } from "./apiSlice";
import type { Product, Category } from "@/lib/mocks/data";

export interface ProductsResponse {
  message: string;
  data: Product[];
  currentPage: number;
  totalItems: number;
  totalPages: number;
}

export interface ProductsQuery {
  page?: number;
  limit?: number;
  category?: string;
  search?: string;
  searchType?: "normal" | "predictive" | "fuzzy";
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  featured?: boolean;
  minPrice?: number;
  maxPrice?: number;
  sellerId?: string;
}

export interface CreateProductRequest {
  title: string;
  description: string;
  price: number;
  category: string;
  quantity: number;
  images?: File[];
}

export interface SellerProductsResponse {
  message: string;
  data: Product[];
  currentPage: number;
  totalItems: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface SellerProductsQuery {
  page?: number;
  limit?: number;
  search?: string;
  searchType?: "normal" | "fuzzy";
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export const productsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getProducts: builder.query<ProductsResponse, ProductsQuery>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();

        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            // Handle searchType conversion for backend compatibility
            if (key === "searchType") {
              if (value === "fuzzy") {
                searchParams.append("fuzzy", "true");
              }
              // Don't append searchType itself, backend doesn't use it
              return;
            }
            searchParams.append(key, value.toString());
          }
        });

        return `/products?${searchParams.toString()}`;
      },
      providesTags: ["Product"],
    }),

    getProduct: builder.query<{ message: string; data: Product }, string>({
      query: (id) => `/products/${id}?t=${Date.now()}`,
      providesTags: (result, error, id) => [{ type: "Product", id }],
    }),

    createProduct: builder.mutation<
      { message: string; data: Product },
      FormData
    >({
      query: (formData) => ({
        url: "/products/create-new",
        method: "POST",
        body: formData,
      }),
      invalidatesTags: ["Product"],
    }),

    getSellerProducts: builder.query<
      SellerProductsResponse,
      SellerProductsQuery
    >({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();

        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            // Handle searchType conversion for backend compatibility
            if (key === "searchType") {
              if (value === "fuzzy") {
                searchParams.append("fuzzy", "true");
              }
              // Don't append searchType itself, backend doesn't use it
              return;
            }
            searchParams.append(key, value.toString());
          }
        });

        // Add timestamp to prevent caching issues
        const timestamp = Date.now();
        searchParams.append("_t", timestamp.toString());

        return `/products/seller?${searchParams.toString()}`;
      },
      providesTags: (result) => {
        const products = Array.isArray(result?.data)
          ? result.data
          : result?.data?.products || [];

        return [
          "Product",
          { type: "Product", id: "SELLER_LIST" },
          ...products.map((product: any) => ({
            type: "Product" as const,
            id: product.id,
          })),
        ];
      },
    }),

    updateProduct: builder.mutation<
      { message: string; data: Product },
      { id: string; formData: FormData }
    >({
      query: ({ id, formData }) => ({
        url: `/products/${id}`,
        method: "PUT",
        body: formData,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Product", id },
        { type: "Product", id: "SELLER_LIST" },
        { type: "Product", id: "LIST" },
        "Product",
      ],
      // Optimistically update the cache
      async onQueryStarted({ id }, { dispatch, queryFulfilled }) {
        try {
          const { data: updatedProduct } = await queryFulfilled;

          // Update individual product cache
          dispatch(
            productsApi.util.updateQueryData("getProduct", id, (draft) => {
              Object.assign(draft, updatedProduct);
            })
          );

          // Update seller products list cache
          dispatch(
            productsApi.util.updateQueryData(
              "getSellerProducts",
              undefined,
              (draft) => {
                if (draft?.data) {
                  const products = Array.isArray(draft.data)
                    ? draft.data
                    : draft.data.products;
                  if (Array.isArray(products)) {
                    const index = products.findIndex((p: any) => p.id === id);
                    if (index !== -1) {
                      Object.assign(products[index], updatedProduct.data);
                    }
                  }
                }
              }
            )
          );
        } catch (error) {
          console.error("Failed to update cache optimistically:", error);
        }
      },
    }),

    deleteProduct: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `/products/products/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Product"],
    }),

    getCategories: builder.query<{ message: string; data: Category[] }, void>({
      query: () => "/categories",
      providesTags: ["Category"],
    }),

    getProductSuggestions: builder.query<{ suggestions: string[] }, string>({
      query: (query) => `/products/suggest?query=${encodeURIComponent(query)}`,
      // Don't cache suggestions as they should be real-time
      keepUnusedDataFor: 0,
    }),
  }),
});

export const {
  useGetProductsQuery,
  useGetProductQuery,
  useCreateProductMutation,
  useGetSellerProductsQuery,
  useUpdateProductMutation,
  useDeleteProductMutation,
  useGetCategoriesQuery,
  useGetProductSuggestionsQuery,
} = productsApi;
