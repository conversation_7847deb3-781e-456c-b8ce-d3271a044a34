import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

// Types for monitoring data
export interface SystemHealth {
  overall: {
    status: "healthy" | "warning" | "critical";
    timestamp: string;
  };
  email: {
    status: "healthy" | "warning" | "critical";
    details: string;
    metrics: {
      totalSent: number;
      totalFailed: number;
      successRate: number;
    };
  };
  retryService: {
    status: "healthy" | "warning" | "critical";
    details: string;
    metrics: {
      totalRetries: number;
      successfulRetries: number;
      failedRetries: number;
    };
  };
  performance: {
    status: "healthy" | "warning" | "critical";
    details: string;
    metrics: {
      avgResponseTime: number;
      totalRequests: number;
      slowRequests: number;
    };
  };
  timeouts: {
    status: "healthy" | "warning" | "critical";
    details: string;
    metrics: {
      totalTimeouts: number;
      timeoutRate: number;
    };
  };
  deadLetterQueue: {
    status: "healthy" | "warning" | "critical";
    details: string;
    metrics: {
      totalEntries: number;
      pendingEntries: number;
      failedPermanently: number;
    };
  };
  uptime: number;
  memory: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
    arrayBuffers: number;
  };
}

export interface EmailMetrics {
  metrics: {
    totalSent: number;
    totalFailed: number;
    successRate: number;
    avgResponseTime: number;
    lastHourSent: number;
    lastHourFailed: number;
  };
  performanceByType: Record<
    string,
    {
      count: number;
      avgDuration: number;
      successRate: number;
      lastSent: string;
    }
  >;
  healthStatus: {
    status: "healthy" | "warning" | "critical";
    details: string;
  };
  timestamp: string;
}

export interface PerformanceMetrics {
  stats: {
    totalRequests: number;
    avgResponseTime: number;
    minResponseTime: number;
    maxResponseTime: number;
    slowRequests: number;
    verySlowRequests: number;
    requestsPerMinute: number;
  };
  healthStatus: {
    status: "healthy" | "warning" | "critical";
    details: string;
  };
  endpointAnalysis: Array<
    [
      string,
      {
        count: number;
        avgDuration: number;
        minDuration: number;
        maxDuration: number;
        slowRequests: number;
        lastAccessed: string;
      }
    ]
  >;
  timestamp: string;
}

export interface TimeoutAnalytics {
  stats: {
    totalTimeouts: number;
    timeoutRate: number;
    avgTimeoutDuration: number;
    timeoutsLastHour: number;
  };
  timeoutsByType: Record<
    string,
    {
      count: number;
      avgDuration: number;
      lastOccurred: string;
    }
  >;
  trends: Array<{
    timestamp: string;
    count: number;
    type: string;
  }>;
  healthStatus: {
    status: "healthy" | "warning" | "critical";
    details: string;
  };
  timestamp: string;
}

export interface DLQStats {
  stats: {
    totalEntries: number;
    pendingEntries: number;
    processingEntries: number;
    resolvedEntries: number;
    failedPermanently: number;
    cancelledEntries: number;
    avgRetryAttempts: number;
    oldestPendingAge: number;
    entriesLast24h: number;
    successfulRetriesLast24h: number;
    byEmailType: Record<string, number>;
    byFailureReason: Record<string, number>;
    byPriority: Record<string, number>;
  };
  healthStatus: {
    status: "healthy" | "warning" | "critical";
    details: string;
    thresholds: {
      warningThreshold: number;
      criticalThreshold: number;
    };
  };
  timestamp: string;
}

export interface DLQEntry {
  id: string;
  emailType: string;
  recipientEmail: string;
  subject: string;
  failureReason: string;
  priority: number;
  retryAttempts: number;
  maxRetryAttempts: number;
  status: string;
  createdAt: string;
  lastRetryAt?: string;
  nextRetryAt?: string;
  processingNode?: string;
  userId?: string;
  errorDetails?: string;
}

export const monitoringApi = createApi({
  reducerPath: "monitoringApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${
      process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api"
    }/monitoring`,
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as any).auth.token;
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: [
    "SystemHealth",
    "EmailMetrics",
    "PerformanceMetrics",
    "TimeoutAnalytics",
    "DLQStats",
    "DLQEntries",
  ],
  endpoints: (builder) => ({
    getSystemHealth: builder.query<
      { status: string; data: SystemHealth },
      void
    >({
      query: () => "/health",
      providesTags: ["SystemHealth"],
    }),
    getEmailMetrics: builder.query<
      { status: string; data: EmailMetrics },
      void
    >({
      query: () => "/email/metrics",
      providesTags: ["EmailMetrics"],
    }),
    getPerformanceMetrics: builder.query<
      { status: string; data: PerformanceMetrics },
      { timeWindow?: number }
    >({
      query: ({ timeWindow } = {}) => ({
        url: "/performance/metrics",
        params: timeWindow ? { timeWindow } : {},
      }),
      providesTags: ["PerformanceMetrics"],
    }),
    getTimeoutAnalytics: builder.query<
      { status: string; data: TimeoutAnalytics },
      void
    >({
      query: () => "/timeouts/analytics",
      providesTags: ["TimeoutAnalytics"],
    }),
    getDLQStats: builder.query<{ status: string; data: DLQStats }, void>({
      query: () => "/dlq/stats",
      providesTags: ["DLQStats"],
    }),
    getDLQEntries: builder.query<
      { status: string; data: { entries: DLQEntry[]; total: number } },
      {
        page?: number;
        limit?: number;
        status?: string;
        emailType?: string;
        failureReason?: string;
      }
    >({
      query: (params = {}) => ({
        url: "/dlq/entries",
        params,
      }),
      providesTags: ["DLQEntries"],
    }),
    retryDLQEntry: builder.mutation<
      { status: string; message: string },
      string
    >({
      query: (id) => ({
        url: `/dlq/entries/${id}/retry`,
        method: "POST",
      }),
      invalidatesTags: ["DLQStats", "DLQEntries"],
    }),
    resolveDLQEntry: builder.mutation<
      { status: string; message: string },
      { id: string; resolution: string }
    >({
      query: ({ id, resolution }) => ({
        url: `/dlq/entries/${id}/resolve`,
        method: "POST",
        body: { resolution },
      }),
      invalidatesTags: ["DLQStats", "DLQEntries"],
    }),
    cancelDLQEntry: builder.mutation<
      { status: string; message: string },
      { id: string; reason: string }
    >({
      query: ({ id, reason }) => ({
        url: `/dlq/entries/${id}/cancel`,
        method: "POST",
        body: { reason },
      }),
      invalidatesTags: ["DLQStats", "DLQEntries"],
    }),
    processDLQ: builder.mutation<
      { status: string; message: string; data: { processed: number } },
      void
    >({
      query: () => ({
        url: "/dlq/process",
        method: "POST",
      }),
      invalidatesTags: ["DLQStats", "DLQEntries"],
    }),
    cleanupDLQ: builder.mutation<
      { status: string; message: string; data: { cleaned: number } },
      { olderThanDays?: number }
    >({
      query: ({ olderThanDays = 30 } = {}) => ({
        url: "/dlq/cleanup",
        method: "POST",
        body: { olderThanDays },
      }),
      invalidatesTags: ["DLQStats", "DLQEntries"],
    }),
  }),
});

export const {
  useGetSystemHealthQuery,
  useGetEmailMetricsQuery,
  useGetPerformanceMetricsQuery,
  useGetTimeoutAnalyticsQuery,
  useGetDLQStatsQuery,
  useGetDLQEntriesQuery,
  useRetryDLQEntryMutation,
  useResolveDLQEntryMutation,
  useCancelDLQEntryMutation,
  useProcessDLQMutation,
  useCleanupDLQMutation,
} = monitoringApi;
