import axios, {
  AxiosError,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";
import { store } from "../store";
import { setCredentials, logOut } from "../features/auth/authSlice";
import { addNotification } from "../features/notifications/notificationSlice";

// Create axios instance
const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || "/api",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor
axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const state = store.getState();
    const token = state.auth.token;

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & {
      _retry?: boolean;
    };

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const state = store.getState();
        const refreshToken = state.auth.refreshToken;

        if (refreshToken) {
          // Attempt to refresh the token
          const response = await axios.post(
            `${
              process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api"
            }/auth/refresh`,
            {
              refreshToken,
            }
          );

          const { accessToken } = response.data;

          // Update the store with new token (keep existing user and refresh token)
          const currentState = store.getState();
          store.dispatch(
            setCredentials({
              user: currentState.auth.user!,
              token: accessToken,
              refreshToken: currentState.auth.refreshToken!,
            })
          );

          // Retry the original request with new token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return axiosInstance(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, logout user
        store.dispatch(logOut());
        store.dispatch(
          addNotification({
            type: "error",
            message: "Session expired. Please login again.",
          })
        );

        // Redirect to login page
        if (typeof window !== "undefined") {
          window.location.href = "/auth/login";
        }
      }
    }

    // Handle other errors
    const errorMessage = getErrorMessage(error);

    // Don't show notifications for certain endpoints or status codes
    const shouldShowNotification = !isExcludedFromNotification(error);

    if (shouldShowNotification) {
      store.dispatch(
        addNotification({
          type: "error",
          message: errorMessage,
        })
      );
    }

    return Promise.reject(error);
  }
);

// Helper function to extract error message
function getErrorMessage(error: AxiosError): string {
  if (error.response?.data && typeof error.response.data === "object") {
    const data = error.response.data as any;
    return data.message || data.error || "An error occurred";
  }

  if (error.message) {
    return error.message;
  }

  return "Network error occurred";
}

// Helper function to determine if error should show notification
function isExcludedFromNotification(error: AxiosError): boolean {
  // Don't show notifications for:
  // - 401 errors (handled by refresh logic)
  // - Validation errors (handled by forms)
  // - Specific endpoints that handle their own errors

  const status = error.response?.status;
  const url = error.config?.url;

  if (status === 401) return true;
  if (status === 422) return true; // Validation errors
  if (url?.includes("/auth/refresh")) return true;

  return false;
}

export default axiosInstance;
