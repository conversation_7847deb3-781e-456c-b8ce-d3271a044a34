import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const API_BASE = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

export interface Inventory {
  id: string;
  product: {
    id: string;
    title: string;
    description: string;
    price: number;
    images: string[];
  };
  currentStock: number;
  reservedStock: number;
  lowStockThreshold: number;
  reorderPoint: number;
  reorderQuantity: number;
  status: "in_stock" | "low_stock" | "out_of_stock" | "discontinued";
  sku?: string;
  location?: string;
  costPrice?: number;
  availableStock: number;
  isLowStock: boolean;
  isOutOfStock: boolean;
  needsReorder: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface StockMovement {
  id: string;
  type: "purchase" | "sale" | "adjustment" | "return" | "damage" | "restock";
  quantity: number;
  previousStock: number;
  newStock: number;
  reason?: string;
  reference?: string;
  createdAt: string;
}

export interface InventoryStats {
  totalProducts: number;
  inStock: number;
  lowStock: number;
  outOfStock: number;
  totalValue: number;
}

export interface CreateInventoryPayload {
  productId: string;
  currentStock: number;
  lowStockThreshold?: number;
  reorderPoint?: number;
  reorderQuantity?: number;
  sku?: string;
  location?: string;
  costPrice?: number;
}

export interface UpdateStockPayload {
  quantity: number;
  type: "purchase" | "sale" | "adjustment" | "return" | "damage" | "restock";
  reason?: string;
  reference?: string;
}

export interface UpdateInventoryPayload {
  newQuantity?: number;
  lowStockThreshold?: number;
  reorderPoint?: number;
  reorderQuantity?: number;
  sku?: string;
  location?: string;
  costPrice?: number;
  reason?: string;
}

export interface InventoryFilters {
  status?: string;
  lowStock?: boolean;
  outOfStock?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

export const inventoryApi = createApi({
  reducerPath: "inventoryApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${API_BASE}/inventory`,
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as any).auth.token;
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ["Inventory", "InventoryStats", "LowStock"],
  endpoints: (builder) => ({
    createInventory: builder.mutation<
      { success: boolean; data: Inventory; message: string },
      CreateInventoryPayload
    >({
      query: (payload) => ({
        url: "",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Inventory", "InventoryStats"],
    }),

    getSellerInventory: builder.query<
      {
        success: boolean;
        data: {
          inventories: Inventory[];
          total: number;
          page: number;
          totalPages: number;
        };
      },
      InventoryFilters
    >({
      query: (filters) => ({
        url: "seller",
        params: filters,
      }),
      providesTags: ["Inventory"],
    }),

    getInventoryByProduct: builder.query<
      { success: boolean; data: Inventory },
      string
    >({
      query: (productId) => `product/${productId}`,
      providesTags: ["Inventory"],
    }),

    updateStock: builder.mutation<
      { success: boolean; data: Inventory; message: string },
      { inventoryId: string; payload: UpdateStockPayload }
    >({
      query: ({ inventoryId, payload }) => ({
        url: `${inventoryId}/stock`,
        method: "PUT",
        body: payload,
      }),
      invalidatesTags: ["Inventory", "InventoryStats", "LowStock"],
    }),

    adjustStock: builder.mutation<
      { success: boolean; data: Inventory; message: string },
      { inventoryId: string; newQuantity: number; reason?: string }
    >({
      query: ({ inventoryId, newQuantity, reason }) => ({
        url: `${inventoryId}/adjust`,
        method: "PUT",
        body: { newQuantity, reason },
      }),
      invalidatesTags: ["Inventory", "InventoryStats", "LowStock"],
    }),

    updateInventory: builder.mutation<
      { success: boolean; data: Inventory; message: string },
      { inventoryId: string } & UpdateInventoryPayload
    >({
      query: ({ inventoryId, ...payload }) => ({
        url: `${inventoryId}`,
        method: "PUT",
        body: payload,
      }),
      invalidatesTags: ["Inventory", "InventoryStats", "LowStock"],
    }),

    getInventoryStats: builder.query<
      { success: boolean; data: InventoryStats },
      void
    >({
      query: () => "stats",
      providesTags: ["InventoryStats"],
    }),

    getLowStockItems: builder.query<
      { success: boolean; data: Inventory[] },
      void
    >({
      query: () => "low-stock",
      providesTags: ["LowStock"],
    }),

    bulkUpdateStock: builder.mutation<
      {
        success: boolean;
        data: Array<{
          success: boolean;
          inventoryId: string;
          data?: Inventory;
          error?: string;
        }>;
        message: string;
      },
      Array<{
        inventoryId: string;
        quantity: number;
        type: string;
        reason?: string;
        reference?: string;
      }>
    >({
      query: (updates) => ({
        url: "bulk-update",
        method: "PUT",
        body: { updates },
      }),
      invalidatesTags: ["Inventory", "InventoryStats", "LowStock"],
    }),
  }),
});

export const {
  useCreateInventoryMutation,
  useGetSellerInventoryQuery,
  useGetInventoryByProductQuery,
  useUpdateStockMutation,
  useAdjustStockMutation,
  useUpdateInventoryMutation,
  useGetInventoryStatsQuery,
  useGetLowStockItemsQuery,
  useBulkUpdateStockMutation,
} = inventoryApi;

// Admin Inventory API
export const adminInventoryApi = createApi({
  reducerPath: "adminInventoryApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${API_BASE}/admin/inventory`,
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as any).auth.token;
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ["AdminInventory", "AdminInventoryStats", "AdminLowStock"],
  endpoints: (builder) => ({
    getAllInventory: builder.query<
      {
        success: boolean;
        data: {
          inventories: Inventory[];
          total: number;
          page: number;
          totalPages: number;
          hasNext: boolean;
          hasPrev: boolean;
        };
      },
      {
        page?: number;
        limit?: number;
        search?: string;
        status?: string;
        sellerId?: string;
        category?: string;
        sortBy?: string;
        sortOrder?: "asc" | "desc";
      }
    >({
      query: (params) => ({
        url: "",
        params,
      }),
      providesTags: ["AdminInventory"],
    }),

    getInventoryStats: builder.query<
      {
        success: boolean;
        data: {
          overview: {
            totalProducts: number;
            inStock: number;
            lowStock: number;
            outOfStock: number;
            discontinued: number;
            totalValue: number;
            averageStockLevel: number;
          };
          distribution: {
            inStockPercentage: number;
            lowStockPercentage: number;
            outOfStockPercentage: number;
            discontinuedPercentage: number;
          };
          topCategories: any[];
          recentMovements: any[];
        };
      },
      void
    >({
      query: () => "stats",
      providesTags: ["AdminInventoryStats"],
    }),

    getLowStockAlerts: builder.query<
      {
        success: boolean;
        data: (Inventory & {
          severity: "critical" | "warning" | "info";
          daysUntilStockout: number | null;
          suggestedReorderQuantity: number;
        })[];
      },
      { severity?: string }
    >({
      query: (params) => ({
        url: "low-stock-alerts",
        params,
      }),
      providesTags: ["AdminLowStock"],
    }),

    bulkUpdateInventory: builder.mutation<
      {
        success: boolean;
        data: Array<{
          inventoryId: string;
          success: boolean;
          data?: Inventory;
          error?: string;
        }>;
      },
      {
        updates: Array<{
          inventoryId: string;
          quantity?: number;
          lowStockThreshold?: number;
          reorderPoint?: number;
          reorderQuantity?: number;
          status?: string;
          reason?: string;
        }>;
      }
    >({
      query: (payload) => ({
        url: "bulk-update",
        method: "PUT",
        body: payload,
      }),
      invalidatesTags: ["AdminInventory", "AdminInventoryStats"],
    }),

    updateInventoryStatus: builder.mutation<
      { success: boolean; data: Inventory },
      { id: string; status: string; reason?: string }
    >({
      query: ({ id, ...body }) => ({
        url: `${id}/status`,
        method: "PATCH",
        body,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "AdminInventory", id },
        "AdminInventoryStats",
      ],
    }),

    autoRestock: builder.mutation<
      {
        success: boolean;
        data: Array<{
          inventoryId: string;
          success: boolean;
          restockedQuantity?: number;
          error?: string;
        }>;
      },
      { inventoryIds: string[]; restockQuantity?: number }
    >({
      query: (payload) => ({
        url: "auto-restock",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: [
        "AdminInventory",
        "AdminInventoryStats",
        "AdminLowStock",
      ],
    }),
  }),
});

export const {
  useGetAllInventoryQuery: useGetAllAdminInventoryQuery,
  useGetInventoryStatsQuery: useGetAdminInventoryStatsQuery,
  useGetLowStockAlertsQuery,
  useBulkUpdateInventoryMutation,
  useUpdateInventoryStatusMutation,
  useAutoRestockMutation,
} = adminInventoryApi;
