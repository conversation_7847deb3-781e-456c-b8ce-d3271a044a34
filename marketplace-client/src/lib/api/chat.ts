import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const API_BASE = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

// Types
export interface User {
  id: string;
  name: string;
  email: string;
  role: "buyer" | "seller" | "admin";
}

export interface Conversation {
  id: string;
  participantOne: User;
  participantTwo: User;
  lastMessagePreview?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  id: string;
  conversation: Conversation;
  sender: User;
  content: string;
  isRead: boolean;
  deletedForEveryone: boolean;
  deletedForUsers: string[];
  createdAt: string;
  updatedAt: string;
}

export interface SendMessageRequest {
  conversationId: string;
  content: string;
}

export interface StartConversationRequest {
  participantId: string;
}

// Chat API
export const chatApi = createApi({
  reducerPath: "chatApi",
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE,
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as any).auth.token;
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ["Conversation", "Message"],
  endpoints: (builder) => ({
    // Conversation endpoints
    getConversations: builder.query<Conversation[], void>({
      query: () => "conversations",
      providesTags: ["Conversation"],
    }),

    startConversation: builder.mutation<Conversation, StartConversationRequest>({
      query: (body) => ({
        url: "conversations",
        method: "POST",
        body,
      }),
      invalidatesTags: ["Conversation"],
    }),

    // Message endpoints
    getMessages: builder.query<Message[], string>({
      query: (conversationId) => `messages/${conversationId}`,
      providesTags: (result, error, conversationId) => [
        { type: "Message", id: conversationId },
      ],
    }),

    sendMessage: builder.mutation<Message, SendMessageRequest>({
      query: (body) => ({
        url: "messages",
        method: "POST",
        body,
      }),
      // Optimistic update
      async onQueryStarted(
        { conversationId, content },
        { dispatch, queryFulfilled, getState }
      ) {
        const currentUser = (getState() as any).auth.user;
        
        // Optimistic update for messages
        const patchResult = dispatch(
          chatApi.util.updateQueryData(
            "getMessages",
            conversationId,
            (draft) => {
              const optimisticMessage: Message = {
                id: `temp-${Date.now()}`,
                conversation: { id: conversationId } as Conversation,
                sender: currentUser,
                content,
                isRead: false,
                deletedForEveryone: false,
                deletedForUsers: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              };
              draft.push(optimisticMessage);
            }
          )
        );

        // Optimistic update for conversation list
        const conversationPatch = dispatch(
          chatApi.util.updateQueryData(
            "getConversations",
            undefined,
            (draft) => {
              const conversation = draft.find(c => c.id === conversationId);
              if (conversation) {
                conversation.lastMessagePreview = content.length > 50 
                  ? content.substring(0, 50) + "..."
                  : content;
                conversation.updatedAt = new Date().toISOString();
                
                // Move to top of list
                const index = draft.indexOf(conversation);
                if (index > 0) {
                  draft.splice(index, 1);
                  draft.unshift(conversation);
                }
              }
            }
          )
        );

        try {
          const { data: actualMessage } = await queryFulfilled;
          
          // Replace optimistic message with real one
          dispatch(
            chatApi.util.updateQueryData(
              "getMessages",
              conversationId,
              (draft) => {
                const tempIndex = draft.findIndex(m => m.id.startsWith('temp-'));
                if (tempIndex !== -1) {
                  draft[tempIndex] = actualMessage;
                }
              }
            )
          );
        } catch {
          // Revert optimistic updates on error
          patchResult.undo();
          conversationPatch.undo();
        }
      },
      invalidatesTags: (result, error, { conversationId }) => [
        { type: "Message", id: conversationId },
        "Conversation",
      ],
    }),

    deleteMessageForUser: builder.mutation<Message, string>({
      query: (messageId) => ({
        url: `messages/${messageId}/user`,
        method: "DELETE",
      }),
      async onQueryStarted(messageId, { dispatch, queryFulfilled, getState }) {
        const currentUser = (getState() as any).auth.user;
        
        // Find which conversation this message belongs to
        const state = getState() as any;
        let conversationId: string | null = null;
        
        // Look through cached messages to find the conversation
        Object.entries(state.chatApi.queries).forEach(([key, query]: [string, any]) => {
          if (key.startsWith('getMessages') && query.data) {
            const message = query.data.find((m: Message) => m.id === messageId);
            if (message) {
              conversationId = message.conversation.id;
            }
          }
        });

        if (conversationId) {
          // Optimistically remove message
          const patchResult = dispatch(
            chatApi.util.updateQueryData(
              "getMessages",
              conversationId,
              (draft) => {
                const index = draft.findIndex(m => m.id === messageId);
                if (index !== -1) {
                  draft.splice(index, 1);
                }
              }
            )
          );

          try {
            await queryFulfilled;
          } catch {
            // Revert on error
            patchResult.undo();
          }
        }
      },
      invalidatesTags: ["Message", "Conversation"],
    }),

    deleteMessageForEveryone: builder.mutation<Message, string>({
      query: (messageId) => ({
        url: `messages/${messageId}/everyone`,
        method: "DELETE",
      }),
      invalidatesTags: ["Message", "Conversation"],
    }),

    markMessageAsRead: builder.mutation<Message, string>({
      query: (messageId) => ({
        url: `messages/${messageId}/read`,
        method: "PATCH",
      }),
      async onQueryStarted(messageId, { dispatch, queryFulfilled, getState }) {
        // Find which conversation this message belongs to
        const state = getState() as any;
        let conversationId: string | null = null;
        
        Object.entries(state.chatApi.queries).forEach(([key, query]: [string, any]) => {
          if (key.startsWith('getMessages') && query.data) {
            const message = query.data.find((m: Message) => m.id === messageId);
            if (message) {
              conversationId = message.conversation.id;
            }
          }
        });

        if (conversationId) {
          // Optimistically mark as read
          const patchResult = dispatch(
            chatApi.util.updateQueryData(
              "getMessages",
              conversationId,
              (draft) => {
                const message = draft.find(m => m.id === messageId);
                if (message) {
                  message.isRead = true;
                }
              }
            )
          );

          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      },
      invalidatesTags: ["Message"],
    }),
  }),
});

export const {
  useGetConversationsQuery,
  useStartConversationMutation,
  useGetMessagesQuery,
  useSendMessageMutation,
  useDeleteMessageForUserMutation,
  useDeleteMessageForEveryoneMutation,
  useMarkMessageAsReadMutation,
} = chatApi;
