/**
 * Utility functions for handling image URLs in the marketplace application
 */

/**
 * Get the full URL for a product image
 * @param imagePath - The image path from the backend (e.g., "/uploads/image.jpg")
 * @param fallback - Fallback image path if the main image is not available
 * @returns Full URL to the image
 */
export function getImageUrl(
  imagePath: string | null | undefined,
  fallback: string = "/placeholder-product.svg"
): string {
  if (!imagePath) {
    return fallback;
  }

  // If it's already a full URL, return as is
  if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
    return imagePath;
  }

  // Handle different path formats from the backend
  let cleanPath = imagePath;

  // Remove /app prefix if it exists (some old uploads have /app/uploads/)
  if (cleanPath.startsWith("/app/uploads/")) {
    cleanPath = cleanPath.replace("/app/uploads/", "/uploads/");
  }

  // Ensure path starts with /uploads/
  if (!cleanPath.startsWith("/uploads/") && !cleanPath.startsWith("/")) {
    cleanPath = `/uploads/${cleanPath}`;
  }

  // Construct the full URL using the backend server URL (not API URL)
  // Static files are served from the root, not from /api
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";
  const baseUrl = apiUrl.replace("/api", "");
  return `${baseUrl}${cleanPath}`;
}

/**
 * Get the first image URL from a product's images array
 * @param images - Array of image paths
 * @param fallback - Fallback image path if no images are available
 * @returns Full URL to the first image or fallback
 */
export function getFirstImageUrl(
  images: string[] | null | undefined,
  fallback: string = "/placeholder-product.svg"
): string {
  if (!images || !Array.isArray(images) || images.length === 0) {
    return fallback;
  }

  return getImageUrl(images[0], fallback);
}

/**
 * Get all image URLs from a product's images array
 * @param images - Array of image paths
 * @returns Array of full URLs
 */
export function getAllImageUrls(images: string[] | null | undefined): string[] {
  if (!images || !Array.isArray(images)) {
    return [];
  }

  return images.map((imagePath) => getImageUrl(imagePath));
}

/**
 * Check if an image path is valid
 * @param imagePath - The image path to validate
 * @returns True if the path is valid, false otherwise
 */
export function isValidImagePath(
  imagePath: string | null | undefined
): boolean {
  if (!imagePath || typeof imagePath !== "string") {
    return false;
  }

  // Check if it's a valid image extension
  const validExtensions = [".jpg", ".jpeg", ".png", ".webp", ".gif"];
  const extension = imagePath.toLowerCase().split(".").pop();

  return validExtensions.includes(`.${extension}`);
}
