/**
 * Format price in Kenya Shillings (KES)
 * @param price - The price to format
 * @returns Formatted price string in KES
 */
export function formatPrice(price: number): string {
  return new Intl.NumberFormat("en-KE", {
    style: "currency",
    currency: "KES",
  }).format(price);
}

/**
 * Format price with custom options
 * @param price - The price to format
 * @param options - Intl.NumberFormat options
 * @returns Formatted price string
 */
export function formatPriceWithOptions(
  price: number,
  options: Intl.NumberFormatOptions = {}
): string {
  const defaultOptions: Intl.NumberFormatOptions = {
    style: "currency",
    currency: "KES",
  };

  return new Intl.NumberFormat("en-KE", {
    ...defaultOptions,
    ...options,
  }).format(price);
}
