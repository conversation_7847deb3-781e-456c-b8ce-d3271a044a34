import { UserRole } from "@/lib/features/auth/authSlice";

/**
 * Frontend permission system for role-based access control
 */

export enum Permission {
  // User Management
  MANAGE_USERS = "manage_users",
  VIEW_USERS = "view_users",
  MANAGE_USER_ROLES = "manage_user_roles",
  
  // Product Management
  MANAGE_PRODUCTS = "manage_products",
  VIEW_PRODUCTS = "view_products",
  MODERATE_PRODUCTS = "moderate_products",
  
  // Order Management
  MANAGE_ORDERS = "manage_orders",
  VIEW_ORDERS = "view_orders",
  PROCESS_REFUNDS = "process_refunds",
  
  // Dispute Resolution
  MANAGE_DISPUTES = "manage_disputes",
  VIEW_DISPUTES = "view_disputes",
  RESOLVE_DISPUTES = "resolve_disputes",
  
  // Technical Monitoring
  VIEW_SYSTEM_HEALTH = "view_system_health",
  MANAGE_MONITORING = "manage_monitoring",
  VIEW_LOGS = "view_logs",
  MANAGE_DLQ = "manage_dlq",
  VIEW_PERFORMANCE_METRICS = "view_performance_metrics",
  
  // Issue Management
  VIEW_USER_ISSUES = "view_user_issues",
  MANAGE_USER_ISSUES = "manage_user_issues",
  ESCALATE_TO_DEVELOPERS = "escalate_to_developers",
  
  // System Administration
  MANAGE_SYSTEM_SETTINGS = "manage_system_settings",
  VIEW_ANALYTICS = "view_analytics",
  MANAGE_CATEGORIES = "manage_categories",
  
  // Communication
  MANAGE_MESSAGES = "manage_messages",
  VIEW_CONVERSATIONS = "view_conversations",
}

/**
 * Role-based permission mapping (matches backend)
 */
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  buyer: [],
  
  seller: [
    Permission.MANAGE_PRODUCTS,
    Permission.VIEW_PRODUCTS,
    Permission.VIEW_ORDERS,
    Permission.MANAGE_MESSAGES,
    Permission.VIEW_CONVERSATIONS,
  ],
  
  admin: [
    // Legacy admin role - full access
    ...Object.values(Permission),
  ],
  
  business_admin: [
    Permission.MANAGE_USERS,
    Permission.VIEW_USERS,
    Permission.MANAGE_PRODUCTS,
    Permission.VIEW_PRODUCTS,
    Permission.MODERATE_PRODUCTS,
    Permission.MANAGE_ORDERS,
    Permission.VIEW_ORDERS,
    Permission.PROCESS_REFUNDS,
    Permission.MANAGE_DISPUTES,
    Permission.VIEW_DISPUTES,
    Permission.RESOLVE_DISPUTES,
    Permission.MANAGE_CATEGORIES,
    Permission.MANAGE_MESSAGES,
    Permission.VIEW_CONVERSATIONS,
    Permission.VIEW_ANALYTICS,
  ],
  
  technical_admin: [
    Permission.VIEW_SYSTEM_HEALTH,
    Permission.MANAGE_MONITORING,
    Permission.VIEW_LOGS,
    Permission.MANAGE_DLQ,
    Permission.VIEW_PERFORMANCE_METRICS,
    Permission.VIEW_USER_ISSUES,
    Permission.MANAGE_USER_ISSUES,
    Permission.ESCALATE_TO_DEVELOPERS,
    Permission.MANAGE_SYSTEM_SETTINGS,
    Permission.VIEW_ANALYTICS,
    Permission.VIEW_USERS,
    Permission.VIEW_ORDERS,
    Permission.VIEW_PRODUCTS,
  ],
};

/**
 * Check if a user role has a specific permission
 */
export function hasPermission(role: UserRole, permission: Permission): boolean {
  const rolePermissions = ROLE_PERMISSIONS[role] || [];
  return rolePermissions.includes(permission);
}

/**
 * Check if a user role has any of the specified permissions
 */
export function hasAnyPermission(role: UserRole, permissions: Permission[]): boolean {
  return permissions.some(permission => hasPermission(role, permission));
}

/**
 * Check if a user role has all of the specified permissions
 */
export function hasAllPermissions(role: UserRole, permissions: Permission[]): boolean {
  return permissions.every(permission => hasPermission(role, permission));
}

/**
 * Check if a role is an admin role (any type of admin)
 */
export function isAdminRole(role: UserRole): boolean {
  return ['admin', 'business_admin', 'technical_admin'].includes(role);
}

/**
 * Check if a role is a technical admin role
 */
export function isTechnicalAdmin(role: UserRole): boolean {
  return role === 'technical_admin' || role === 'admin';
}

/**
 * Check if a role is a business admin role
 */
export function isBusinessAdmin(role: UserRole): boolean {
  return role === 'business_admin' || role === 'admin';
}

/**
 * Get user-friendly role names
 */
export const ROLE_NAMES: Record<UserRole, string> = {
  buyer: "Buyer",
  seller: "Seller", 
  admin: "Super Admin",
  business_admin: "Business Admin",
  technical_admin: "Technical Admin",
};

/**
 * Get role descriptions
 */
export const ROLE_DESCRIPTIONS: Record<UserRole, string> = {
  buyer: "Can browse and purchase products",
  seller: "Can sell products and manage inventory",
  admin: "Full system access and control",
  business_admin: "Manages users, orders, disputes, and business operations",
  technical_admin: "Manages system monitoring, logs, and technical issues",
};

/**
 * Navigation items based on role permissions
 */
export interface NavigationItem {
  name: string;
  href: string;
  icon?: string;
  permission?: Permission;
  children?: NavigationItem[];
}

export const getAdminNavigationItems = (role: UserRole): NavigationItem[] => {
  const items: NavigationItem[] = [];

  // Dashboard - always available for admins
  if (isAdminRole(role)) {
    items.push({
      name: "Dashboard",
      href: "/admin/dashboard",
    });
  }

  // Business Admin Items
  if (hasPermission(role, Permission.MANAGE_USERS)) {
    items.push({
      name: "Users",
      href: "/admin/users",
      permission: Permission.MANAGE_USERS,
    });
  }

  if (hasPermission(role, Permission.MANAGE_ORDERS)) {
    items.push({
      name: "Orders",
      href: "/admin/orders",
      permission: Permission.MANAGE_ORDERS,
    });
  }

  if (hasPermission(role, Permission.MANAGE_DISPUTES)) {
    items.push({
      name: "Disputes",
      href: "/admin/disputes",
      permission: Permission.MANAGE_DISPUTES,
    });
  }

  // Technical Admin Items
  if (hasPermission(role, Permission.VIEW_SYSTEM_HEALTH)) {
    items.push({
      name: "Monitoring",
      href: "/admin/monitoring",
      permission: Permission.VIEW_SYSTEM_HEALTH,
      children: [
        {
          name: "System Health",
          href: "/admin/monitoring/health",
          permission: Permission.VIEW_SYSTEM_HEALTH,
        },
        {
          name: "Email & DLQ",
          href: "/admin/monitoring/email",
          permission: Permission.MANAGE_DLQ,
        },
        {
          name: "Performance",
          href: "/admin/monitoring/performance",
          permission: Permission.VIEW_PERFORMANCE_METRICS,
        },
        {
          name: "User Issues",
          href: "/admin/monitoring/issues",
          permission: Permission.VIEW_USER_ISSUES,
        },
      ],
    });
  }

  // Settings - available for all admin types
  if (isAdminRole(role)) {
    items.push({
      name: "Settings",
      href: "/admin/settings",
    });
  }

  return items;
};
