import type { User, UserRole } from "@/lib/features/auth/authSlice";

// Mock Users
export const mockUsers: User[] = [
  {
    id: "1",
    email: "<EMAIL>",
    name: "Admin User",
    role: "admin",
    avatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face",
  },
  {
    id: "2",
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "seller",
    avatar:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face",
  },
  {
    id: "3",
    email: "<EMAIL>",
    name: "Jane Buyer",
    role: "buyer",
    avatar:
      "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face",
  },
];

// Mock Categories
export interface Category {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export const mockCategories: Category[] = [
  {
    id: "1",
    name: "Electronics",
    description: "Latest gadgets and electronic devices",
    image:
      "https://images.unsplash.com/photo-1498049794561-7780e7231661?w=300&h=200&fit=crop",
    productCount: 45,
  },
  {
    id: "2",
    name: "Clothing",
    description: "Fashion and apparel for all occasions",
    image:
      "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=200&fit=crop",
    productCount: 78,
  },
  {
    id: "3",
    name: "Home & Garden",
    description: "Everything for your home and garden",
    image:
      "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop",
    productCount: 32,
  },
  {
    id: "4",
    name: "Books",
    description: "Books, magazines, and educational materials",
    image:
      "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=300&h=200&fit=crop",
    productCount: 156,
  },
];

// Mock Products
export interface Product {
  id: string;
  title: string; // Changed from 'name' to match backend
  description: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: Category | null; // Updated to match backend response
  categoryId?: string;
  sellerId?: string;
  sellerName?: string;
  quantity: number; // Changed from 'stock' to match backend
  rating?: number;
  reviewCount?: number;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
  featured?: boolean;
  seller?: {
    id: string;
    name: string;
    email: string;
    role: string;
    isVerified: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

export const mockProducts: Product[] = [
  {
    id: "1",
    title: "Wireless Bluetooth Headphones",
    description:
      "High-quality wireless headphones with noise cancellation and 30-hour battery life.",
    price: 199.99,
    originalPrice: 249.99,
    images: [
      "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=500&fit=crop",
      "https://images.unsplash.com/photo-1484704849700-f032a568e944?w=500&h=500&fit=crop",
    ],
    category: {
      id: "1",
      name: "Electronics",
      description: "Electronic devices and gadgets",
      icon: "laptop",
      isActive: true,
      sortOrder: 1,
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
    },
    categoryId: "1",
    sellerId: "2",
    sellerName: "John Seller",
    quantity: 25,
    rating: 4.5,
    reviewCount: 128,
    tags: ["wireless", "bluetooth", "noise-cancelling"],
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-20T15:30:00Z",
    featured: true,
  },
  {
    id: "2",
    title: "Organic Cotton T-Shirt",
    description:
      "Comfortable and sustainable organic cotton t-shirt available in multiple colors.",
    price: 29.99,
    images: [
      "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=500&fit=crop",
    ],
    category: {
      id: "2",
      name: "Clothing",
      description: "Fashion and apparel",
      icon: "shirt",
      isActive: true,
      sortOrder: 2,
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
    },
    categoryId: "2",
    sellerId: "2",
    sellerName: "John Seller",
    quantity: 50,
    rating: 4.2,
    reviewCount: 45,
    tags: ["organic", "cotton", "sustainable"],
    createdAt: "2024-01-10T08:00:00Z",
    updatedAt: "2024-01-18T12:00:00Z",
    featured: false,
  },
  {
    id: "3",
    title: "Smart Home Security Camera",
    description:
      "1080p HD security camera with night vision, motion detection, and mobile app control.",
    price: 89.99,
    images: [
      "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500&h=500&fit=crop",
    ],
    category: {
      id: "1",
      name: "Electronics",
      description: "Electronic devices and gadgets",
      icon: "laptop",
      isActive: true,
      sortOrder: 1,
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
    },
    categoryId: "1",
    sellerId: "2",
    sellerName: "John Seller",
    quantity: 15,
    rating: 4.7,
    reviewCount: 89,
    tags: ["smart-home", "security", "camera"],
    createdAt: "2024-01-12T14:00:00Z",
    updatedAt: "2024-01-19T09:15:00Z",
    featured: true,
  },
];

// Mock Orders
export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  total: number;
  status: "pending" | "processing" | "shipped" | "delivered" | "cancelled";
  shippingAddress: Address;
  paymentMethod: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  productId: string;
  productName: string;
  quantity: number;
  price: number;
  sellerId: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export const mockOrders: Order[] = [
  {
    id: "1",
    userId: "3",
    items: [
      {
        productId: "1",
        productName: "Wireless Bluetooth Headphones",
        quantity: 1,
        price: 199.99,
        sellerId: "2",
      },
    ],
    total: 199.99,
    status: "delivered",
    shippingAddress: {
      street: "123 Main St",
      city: "New York",
      state: "NY",
      zipCode: "10001",
      country: "USA",
    },
    paymentMethod: "Credit Card",
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-20T15:30:00Z",
  },
];

// Mock Reviews
export interface Review {
  id: string;
  productId: string;
  userId: string;
  userName: string;
  rating: number;
  title: string;
  comment: string;
  createdAt: string;
  helpful: number;
}

export const mockReviews: Review[] = [
  {
    id: "1",
    productId: "1",
    userId: "3",
    userName: "Jane Buyer",
    rating: 5,
    title: "Excellent sound quality!",
    comment:
      "These headphones exceeded my expectations. The sound quality is amazing and the battery life is as advertised.",
    createdAt: "2024-01-18T14:30:00Z",
    helpful: 12,
  },
];
