import { describe, it, expect } from 'vitest'
import authReducer, {
  setCredentials,
  logOut,
  setLoading,
  updateUser,
  selectCurrentUser,
  selectIsAuthenticated,
} from '../authSlice'
import { createMockUser } from '@/test/utils'

describe('authSlice', () => {
  const initialState = {
    user: null,
    token: null,
    refreshToken: null,
    isAuthenticated: false,
    isLoading: false,
  }

  it('should return the initial state', () => {
    expect(authReducer(undefined, { type: 'unknown' })).toEqual(initialState)
  })

  it('should handle setCredentials', () => {
    const mockUser = createMockUser()
    const credentials = {
      user: mockUser,
      token: 'test-token',
      refreshToken: 'test-refresh-token',
    }

    const actual = authReducer(initialState, setCredentials(credentials))
    
    expect(actual.user).toEqual(mockUser)
    expect(actual.token).toBe('test-token')
    expect(actual.refreshToken).toBe('test-refresh-token')
    expect(actual.isAuthenticated).toBe(true)
  })

  it('should handle logOut', () => {
    const authenticatedState = {
      user: createMockUser(),
      token: 'test-token',
      refreshToken: 'test-refresh-token',
      isAuthenticated: true,
      isLoading: false,
    }

    const actual = authReducer(authenticatedState, logOut())
    
    expect(actual.user).toBeNull()
    expect(actual.token).toBeNull()
    expect(actual.refreshToken).toBeNull()
    expect(actual.isAuthenticated).toBe(false)
  })

  it('should handle setLoading', () => {
    const actual = authReducer(initialState, setLoading(true))
    expect(actual.isLoading).toBe(true)
  })

  it('should handle updateUser', () => {
    const mockUser = createMockUser()
    const stateWithUser = {
      ...initialState,
      user: mockUser,
      isAuthenticated: true,
    }

    const updates = { name: 'Updated Name' }
    const actual = authReducer(stateWithUser, updateUser(updates))
    
    expect(actual.user?.name).toBe('Updated Name')
    expect(actual.user?.email).toBe(mockUser.email) // Other fields unchanged
  })

  it('should not update user when user is null', () => {
    const updates = { name: 'Updated Name' }
    const actual = authReducer(initialState, updateUser(updates))
    
    expect(actual.user).toBeNull()
  })

  describe('selectors', () => {
    const mockState = {
      auth: {
        user: createMockUser(),
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        isAuthenticated: true,
        isLoading: false,
      },
    }

    it('should select current user', () => {
      expect(selectCurrentUser(mockState)).toEqual(mockState.auth.user)
    })

    it('should select authentication status', () => {
      expect(selectIsAuthenticated(mockState)).toBe(true)
    })
  })
})
