import { store } from '../store'
import { addNotification, type NotificationType } from '../features/notifications/notificationSlice'

interface NotificationOptions {
  title?: string
  message: string
  duration?: number
}

class NotificationService {
  private dispatch = store.dispatch

  private notify(type: NotificationType, options: NotificationOptions) {
    this.dispatch(
      addNotification({
        type,
        title: options.title,
        message: options.message,
        duration: options.duration,
      })
    )
  }

  success(message: string, title?: string, duration?: number) {
    this.notify('success', { message, title, duration })
  }

  error(message: string, title?: string, duration?: number) {
    this.notify('error', { message, title, duration })
  }

  warning(message: string, title?: string, duration?: number) {
    this.notify('warning', { message, title, duration })
  }

  info(message: string, title?: string, duration?: number) {
    this.notify('info', { message, title, duration })
  }

  // Convenience methods for common scenarios
  apiError(error: any) {
    const message = error?.response?.data?.message || error?.message || 'An error occurred'
    this.error(message, 'API Error')
  }

  validationError(message: string) {
    this.error(message, 'Validation Error')
  }

  networkError() {
    this.error('Please check your internet connection and try again.', 'Network Error')
  }

  loginSuccess() {
    this.success('Welcome back!', 'Login Successful')
  }

  logoutSuccess() {
    this.info('You have been logged out successfully.', 'Logged Out')
  }

  saveSuccess() {
    this.success('Your changes have been saved successfully.', 'Saved')
  }

  deleteSuccess() {
    this.success('Item deleted successfully.', 'Deleted')
  }

  copySuccess() {
    this.success('Copied to clipboard!', 'Copied')
  }
}

// Export singleton instance
export const notificationService = new NotificationService()

// Export hook for use in components
export function useNotifications() {
  return notificationService
}
