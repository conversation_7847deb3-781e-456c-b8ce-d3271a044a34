"use client";

import { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { toast } from "sonner";
import {
  selectNotifications,
  removeNotification,
  addNotification,
} from "../features/notifications/notificationSlice";

export function NotificationProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const notifications = useSelector(selectNotifications);
  const dispatch = useDispatch();

  useEffect(() => {
    notifications.forEach((notification) => {
      const { id, type, title, message, duration = 4000 } = notification;

      // Create toast based on type
      switch (type) {
        case "success":
          toast.success(title || "Success", {
            description: message,
            duration,
            onDismiss: () => dispatch(removeNotification(id)),
          });
          break;
        case "error":
          toast.error(title || "Error", {
            description: message,
            duration,
            onDismiss: () => dispatch(removeNotification(id)),
          });
          break;
        case "warning":
          toast.warning(title || "Warning", {
            description: message,
            duration,
            onDismiss: () => dispatch(removeNotification(id)),
          });
          break;
        case "info":
          toast.info(title || "Info", {
            description: message,
            duration,
            onDismiss: () => dispatch(removeNotification(id)),
          });
          break;
        default:
          toast(title || message, {
            description: title ? message : undefined,
            duration,
            onDismiss: () => dispatch(removeNotification(id)),
          });
      }

      // Remove notification from store after showing
      setTimeout(() => {
        dispatch(removeNotification(id));
      }, 100);
    });
  }, [notifications, dispatch]);

  return <>{children}</>;
}

// Hook for using notifications
export function useNotifications() {
  const dispatch = useDispatch();

  const success = (message: string, title?: string) => {
    dispatch(
      addNotification({
        type: "success",
        title: title || "Success",
        message,
        duration: 4000,
      })
    );
  };

  const error = (message: string, title?: string) => {
    dispatch(
      addNotification({
        type: "error",
        title: title || "Error",
        message,
        duration: 5000,
      })
    );
  };

  const warning = (message: string, title?: string) => {
    dispatch(
      addNotification({
        type: "warning",
        title: title || "Warning",
        message,
        duration: 4000,
      })
    );
  };

  const info = (message: string, title?: string) => {
    dispatch(
      addNotification({
        type: "info",
        title: title || "Info",
        message,
        duration: 4000,
      })
    );
  };

  return {
    success,
    error,
    warning,
    info,
  };
}
