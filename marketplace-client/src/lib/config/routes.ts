import type { UserRole } from '@/lib/features/auth/authSlice'

export interface RouteConfig {
  path: string
  allowedRoles?: UserRole[]
  requiredPermissions?: string[]
  isPublic?: boolean
  redirectTo?: string
}

export const routeConfig: RouteConfig[] = [
  // Public routes (accessible to everyone)
  {
    path: '/',
    isPublic: true,
  },
  {
    path: '/products',
    isPublic: true,
  },
  {
    path: '/products/[id]',
    isPublic: true,
  },
  {
    path: '/categories',
    isPublic: true,
  },
  {
    path: '/about',
    isPublic: true,
  },
  {
    path: '/contact',
    isPublic: true,
  },
  {
    path: '/auth/login',
    isPublic: true,
  },
  {
    path: '/auth/register',
    isPublic: true,
  },

  // Authenticated user routes
  {
    path: '/profile',
    allowedRoles: ['buyer', 'seller', 'admin'],
  },
  {
    path: '/settings',
    allowedRoles: ['buyer', 'seller', 'admin'],
  },
  {
    path: '/orders',
    allowedRoles: ['buyer', 'seller', 'admin'],
  },
  {
    path: '/cart',
    allowedRoles: ['buyer', 'seller', 'admin'],
  },

  // Seller routes
  {
    path: '/seller',
    allowedRoles: ['seller', 'admin'],
    redirectTo: '/seller/dashboard',
  },
  {
    path: '/seller/dashboard',
    allowedRoles: ['seller', 'admin'],
    requiredPermissions: ['view:seller'],
  },
  {
    path: '/seller/products',
    allowedRoles: ['seller', 'admin'],
    requiredPermissions: ['manage:own-products'],
  },
  {
    path: '/seller/products/new',
    allowedRoles: ['seller', 'admin'],
    requiredPermissions: ['manage:own-products'],
  },
  {
    path: '/seller/products/[id]',
    allowedRoles: ['seller', 'admin'],
    requiredPermissions: ['manage:own-products'],
  },
  {
    path: '/seller/orders',
    allowedRoles: ['seller', 'admin'],
    requiredPermissions: ['view:own-orders'],
  },
  {
    path: '/seller/analytics',
    allowedRoles: ['seller', 'admin'],
    requiredPermissions: ['view:own-analytics'],
  },

  // Admin routes
  {
    path: '/admin',
    allowedRoles: ['admin'],
    redirectTo: '/admin/dashboard',
  },
  {
    path: '/admin/dashboard',
    allowedRoles: ['admin'],
    requiredPermissions: ['view:admin'],
  },
  {
    path: '/admin/users',
    allowedRoles: ['admin'],
    requiredPermissions: ['manage:users'],
  },
  {
    path: '/admin/products',
    allowedRoles: ['admin'],
    requiredPermissions: ['manage:products'],
  },
  {
    path: '/admin/orders',
    allowedRoles: ['admin'],
    requiredPermissions: ['manage:orders'],
  },
  {
    path: '/admin/categories',
    allowedRoles: ['admin'],
    requiredPermissions: ['manage:categories'],
  },
  {
    path: '/admin/analytics',
    allowedRoles: ['admin'],
    requiredPermissions: ['view:analytics'],
  },
  {
    path: '/admin/settings',
    allowedRoles: ['admin'],
    requiredPermissions: ['view:admin'],
  },
]

export function getRouteConfig(path: string): RouteConfig | undefined {
  return routeConfig.find(route => {
    // Handle dynamic routes
    const routePattern = route.path.replace(/\[.*?\]/g, '[^/]+')
    const regex = new RegExp(`^${routePattern}$`)
    return regex.test(path)
  })
}

export function isPublicRoute(path: string): boolean {
  const config = getRouteConfig(path)
  return config?.isPublic || false
}

export function getDefaultRouteForRole(role: UserRole): string {
  switch (role) {
    case 'admin':
      return '/admin/dashboard'
    case 'seller':
      return '/seller/dashboard'
    case 'buyer':
      return '/products'
    default:
      return '/'
  }
}
