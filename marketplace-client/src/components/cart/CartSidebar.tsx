"use client";

import { useState } from "react";
import { useCart } from "@/lib/contexts/CartContext";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  ArrowRight,
  Package,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { getFirstImageUrl } from "@/lib/utils/imageUtils";
import { OptimisticQuantityControl } from "./OptimisticQuantityControl";

interface CartSidebarProps {
  children?: React.ReactNode;
}

export default function CartSidebar({ children }: CartSidebarProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { items, itemCount, total, isLoading, updateCartItem, removeFromCart } =
    useCart();

  const handleQuantityChange = async (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      await removeFromCart(itemId);
    } else {
      await updateCartItem(itemId, newQuantity);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
    }).format(price);
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        {children || (
          <Button
            variant="ghost"
            size="icon"
            className="relative marketplace-text-warm hover:bg-orange-50"
          >
            <ShoppingCart className="h-5 w-5" />
            {itemCount > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs bg-orange-500 hover:bg-orange-600"
              >
                {itemCount}
              </Badge>
            )}
          </Button>
        )}
      </SheetTrigger>

      <SheetContent className="w-full sm:max-w-xl marketplace-theme p-0">
        <div className="p-6 border-b border-gray-100">
          <SheetHeader className="space-y-3">
            <SheetTitle className="marketplace-heading flex items-center text-xl">
              <ShoppingCart className="mr-3 h-6 w-6 marketplace-text-warm" />
              Shopping Cart
            </SheetTitle>
            <SheetDescription className="marketplace-text-muted text-base">
              {itemCount === 0
                ? "Your cart is empty"
                : `${itemCount} item${itemCount !== 1 ? "s" : ""} in your cart`}
            </SheetDescription>
          </SheetHeader>
        </div>

        <div className="flex flex-col h-full">
          {isLoading ? (
            <div className="flex items-center justify-center py-12 px-6">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-orange-500"></div>
            </div>
          ) : items.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16 px-6 text-center">
              <Package className="h-16 w-16 marketplace-text-muted mb-6" />
              <h3 className="text-xl font-medium marketplace-text-primary mb-3">
                Your cart is empty
              </h3>
              <p className="marketplace-text-muted mb-8 text-base">
                Add some products to get started
              </p>
              <Button
                asChild
                className="marketplace-btn-primary px-8 py-3"
                onClick={() => setIsOpen(false)}
              >
                <Link href="/products">Browse Products</Link>
              </Button>
            </div>
          ) : (
            <>
              {/* Cart Items */}
              <ScrollArea className="flex-1 px-6 py-4">
                <div className="space-y-6">
                  {items.map((item) => (
                    <div
                      key={item.id}
                      className="marketplace-card p-5 hover:shadow-md transition-shadow"
                    >
                      <div className="flex gap-4">
                        {/* Product Image */}
                        <div className="relative w-20 h-20 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                          {item.product.images &&
                          item.product.images.length > 0 ? (
                            <Image
                              src={getFirstImageUrl(item.product.images)}
                              alt={item.product.title}
                              fill
                              className="object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Package className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                        </div>

                        {/* Product Details */}
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium marketplace-text-primary text-base line-clamp-2 mb-1">
                            {item.product.title}
                          </h4>
                          <p className="text-sm marketplace-text-muted mb-3">
                            by {item.product.seller.name}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className="font-semibold marketplace-text-warm text-lg">
                              {formatPrice(item.product.price)}
                            </span>

                            {/* Optimistic Quantity Controls */}
                            <div className="flex items-center gap-2">
                              <OptimisticQuantityControl
                                itemId={item.id}
                                initialQuantity={item.quantity}
                                size="small"
                                onQuantityChange={(quantity) => {
                                  // Optimistic update handled by component
                                }}
                                disabled={
                                  item.quantity >= item.product.quantity
                                }
                              />
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 text-red-500 hover:text-red-700 hover:bg-red-50"
                                onClick={() => removeFromCart(item.id)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>

                          {/* Stock Warning */}
                          {item.product.quantity < 5 && (
                            <div className="mt-3 p-2 bg-orange-50 border border-orange-200 rounded-md">
                              <p className="text-xs text-orange-700 font-medium">
                                ⚠️ Only {item.product.quantity} left in stock
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>

              {/* Cart Summary */}
              <div className="border-t border-gray-200 bg-gray-50 px-6 py-6 mt-4">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="marketplace-text-muted text-base">
                      Subtotal
                    </span>
                    <span className="font-semibold marketplace-text-primary text-base">
                      {formatPrice(total)}
                    </span>
                  </div>

                  <Separator />

                  <div className="flex justify-between items-center text-xl">
                    <span className="font-semibold marketplace-text-primary">
                      Total
                    </span>
                    <span className="font-bold marketplace-text-warm">
                      {formatPrice(total)}
                    </span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3 mt-8">
                  <Button
                    asChild
                    className="w-full marketplace-btn-primary h-12 text-base font-semibold"
                    onClick={() => setIsOpen(false)}
                  >
                    <Link href="/checkout">
                      Proceed to Checkout
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Link>
                  </Button>

                  <Button
                    variant="outline"
                    asChild
                    className="w-full marketplace-btn-outline h-11 text-base"
                    onClick={() => setIsOpen(false)}
                  >
                    <Link href="/cart">View Full Cart</Link>
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
