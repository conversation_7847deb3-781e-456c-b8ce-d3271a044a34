/**
 * Optimistic Quantity Control Component
 *
 * Provides instant UI feedback with offline support and reconciliation
 */

import React, { useState, useEffect, useCallback, useRef } from "react";
import { Minus, Plus, Wifi, WifiOff, Clock } from "lucide-react";
import { useCart } from "@/lib/contexts/CartContext";
import { useAuth } from "@/lib/contexts/AuthContext";
import { Button } from "@/components/ui/button";

interface OptimisticQuantityControlProps {
  itemId: string;
  initialQuantity: number;
  onQuantityChange?: (quantity: number) => void;
  disabled?: boolean;
  size?: "default" | "small"; // Add size variant for sidebar
}

export function OptimisticQuantityControl({
  itemId,
  initialQuantity,
  onQuantityChange,
  disabled = false,
  size = "default",
}: OptimisticQuantityControlProps) {
  const cart = useCart();
  const { token } = useAuth();

  const [optimisticQuantity, setOptimisticQuantity] = useState(initialQuantity);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [pendingOperations, setPendingOperations] = useState<Set<string>>(
    new Set()
  );
  const [lastSyncedQuantity, setLastSyncedQuantity] = useState(initialQuantity);

  // Add ref to track if we're in the middle of a sync operation
  const syncInProgress = useRef(false);

  // We'll define the online/offline effect after the functions are defined

  // Track if this is the initial mount
  const isInitialMount = useRef(true);

  // Sync optimistic quantity with cart state
  useEffect(() => {
    // On initial mount, always sync both values
    if (isInitialMount.current) {
      setOptimisticQuantity(initialQuantity);
      setLastSyncedQuantity(initialQuantity);
      isInitialMount.current = false;
    } else {
      // On subsequent updates, only update optimisticQuantity if we don't have unsynced changes
      const currentHasUnsyncedChanges =
        optimisticQuantity !== lastSyncedQuantity;

      if (!currentHasUnsyncedChanges && !syncInProgress.current) {
        setOptimisticQuantity(initialQuantity);
        setLastSyncedQuantity(initialQuantity);
      }
      // If we have unsynced changes, ignore cart context updates
    }
  }, [initialQuantity]);

  // Update quantity on server
  const updateQuantityOnServer = useCallback(
    async (quantity: number) => {
      const operationId = `update-${itemId}-${Date.now()}`;
      setPendingOperations((prev) => new Set(prev).add(operationId));

      try {
        const API_BASE =
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

        const response = await fetch(`${API_BASE}/cart/update/${itemId}`, {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ quantity }),
        });

        if (!response.ok) {
          throw new Error("Failed to update cart item");
        }

        setPendingOperations((prev) => {
          const newSet = new Set(prev);
          newSet.delete(operationId);
          return newSet;
        });
        return true;
      } catch (error) {
        setPendingOperations((prev) => {
          const newSet = new Set(prev);
          newSet.delete(operationId);
          return newSet;
        });
        throw error;
      }
    },
    [itemId, token]
  );

  // Reconcile pending changes when back online
  const reconcilePendingChanges = useCallback(async () => {
    if (optimisticQuantity !== lastSyncedQuantity) {
      try {
        // Make direct API call without connectivity pre-check for reconciliation
        const API_BASE =
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";
        const response = await fetch(`${API_BASE}/cart/update/${itemId}`, {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ quantity: optimisticQuantity }),
        });

        if (!response.ok) {
          throw new Error("Failed to update cart item");
        }

        setLastSyncedQuantity(optimisticQuantity);
        setPendingOperations(new Set());
      } catch (error) {
        // Don't update lastSyncedQuantity on failure - keep the unsynced state
      }
    }
  }, [optimisticQuantity, lastSyncedQuantity, isOnline, itemId, token]);

  // Monitor online/offline status (defined after functions to avoid circular dependency)
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      // Reconcile when back online
      reconcilePendingChanges();
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, [reconcilePendingChanges]);

  // Calculate status variables
  const hasUnsyncedChanges = optimisticQuantity !== lastSyncedQuantity;
  const hasPendingOperations = pendingOperations.size > 0;

  // Periodic retry for unsynced changes when online
  useEffect(() => {
    if (hasUnsyncedChanges && isOnline) {
      const retryInterval = setInterval(() => {
        reconcilePendingChanges();
      }, 5000); // Retry every 5 seconds

      return () => {
        clearInterval(retryInterval);
      };
    }
  }, [hasUnsyncedChanges, isOnline, reconcilePendingChanges]);

  // Handle quantity change with optimistic updates
  const handleQuantityChange = async (newQuantity: number) => {
    if (newQuantity < 1 || disabled) return;

    // 1. Apply optimistic update immediately to local state
    setOptimisticQuantity(newQuantity);
    onQuantityChange?.(newQuantity);

    // 2. Apply optimistic update to cart context (updates cart count immediately)
    cart.optimisticUpdateItem(itemId, newQuantity);

    // 3. Attempt server sync if we're online
    if (isOnline) {
      try {
        syncInProgress.current = true;
        await updateQuantityOnServer(newQuantity);
        // Only update lastSyncedQuantity on successful sync
        setLastSyncedQuantity(newQuantity);
        syncInProgress.current = false;
      } catch (error) {
        syncInProgress.current = false;
        // Keep optimistic update, will reconcile later
        // DON'T update lastSyncedQuantity on failure
      }
    }
    // If offline, just keep the optimistic update
  };

  const increment = () => handleQuantityChange(optimisticQuantity + 1);
  const decrement = () => handleQuantityChange(optimisticQuantity - 1);

  // Size-specific styling
  const buttonSize = size === "small" ? "h-6 w-6" : "h-8 w-8";
  const iconSize = size === "small" ? "h-3 w-3" : "h-4 w-4";

  return (
    <div className="flex items-center space-x-2">
      {/* Decrease button */}
      <Button
        variant="outline"
        size="icon"
        className={buttonSize}
        onClick={decrement}
        disabled={disabled || optimisticQuantity <= 1}
        aria-label="Decrease quantity"
      >
        <Minus className={iconSize} />
      </Button>

      {/* Quantity display with status indicators */}
      <div className="relative flex items-center">
        <span className="font-medium min-w-[2rem] text-center">
          {optimisticQuantity}
        </span>

        {/* Status indicators */}
        <div className="flex items-center ml-2 space-x-1.5">
          {/* Online/Offline indicator */}
          {isOnline ? (
            <div title="Online">
              <Wifi className="h-4 w-4 text-green-500" />
            </div>
          ) : (
            <div title="Offline">
              <WifiOff className="h-4 w-4 text-red-500" />
            </div>
          )}

          {/* Pending operations indicator - show whenever there are unsynced changes */}
          {(hasPendingOperations || hasUnsyncedChanges) && (
            <div
              title={
                isOnline
                  ? "Syncing... (click to retry)"
                  : "Will sync when online (click to retry)"
              }
              onClick={() => {
                reconcilePendingChanges();
              }}
            >
              <Clock className="h-4 w-4 text-yellow-500 animate-pulse cursor-pointer" />
            </div>
          )}
        </div>
      </div>

      {/* Increase button */}
      <Button
        variant="outline"
        size="icon"
        className={buttonSize}
        onClick={increment}
        disabled={disabled}
        aria-label="Increase quantity"
      >
        <Plus className={iconSize} />
      </Button>

      {/* Status message */}
      {!isOnline && hasUnsyncedChanges && (
        <span className="text-xs text-yellow-600 ml-2">
          Will sync when online
        </span>
      )}
    </div>
  );
}
