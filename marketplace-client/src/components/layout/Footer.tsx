import Link from "next/link";
import { Package } from "lucide-react";

export default function Footer() {
  return (
    <footer className="border-t border-gray-200 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-6 sm:py-8 lg:py-10">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
            {/* Company Info */}
            <div className="space-y-4 sm:space-y-5">
              <div className="flex items-center space-x-3">
                <Package className="h-7 w-7 sm:h-8 sm:w-8 marketplace-text-warm" />
                <span className="text-lg sm:text-xl font-bold marketplace-heading">
                  Duka Kuu
                </span>
              </div>
              <p className="text-sm sm:text-base marketplace-text-muted leading-relaxed">
                Kenya's trusted marketplace for discovering quality products
                from sellers countrywide.
              </p>
            </div>

            {/* Quick Links */}
            <div className="space-y-4 sm:space-y-5">
              <h3 className="text-sm sm:text-base font-semibold marketplace-text-primary">
                Quick Links
              </h3>
              <ul className="space-y-2 sm:space-y-3 text-sm sm:text-base">
                <li>
                  <Link
                    href="/products"
                    className="marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                  >
                    Products
                  </Link>
                </li>
                <li>
                  <Link
                    href="/categories"
                    className="marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                  >
                    Categories
                  </Link>
                </li>
                <li>
                  <Link
                    href="/about"
                    className="marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                  >
                    About Us
                  </Link>
                </li>
                <li>
                  <Link
                    href="/contact"
                    className="marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                  >
                    Contact
                  </Link>
                </li>
              </ul>
            </div>

            {/* For Sellers */}
            <div className="space-y-4 sm:space-y-5">
              <h3 className="text-sm sm:text-base font-semibold marketplace-text-primary">
                For Sellers
              </h3>
              <ul className="space-y-2 sm:space-y-3 text-sm sm:text-base">
                <li>
                  <Link
                    href="/seller/register"
                    className="marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                  >
                    Become a Seller
                  </Link>
                </li>
                <li>
                  <Link
                    href="/seller/dashboard"
                    className="marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                  >
                    Seller Dashboard
                  </Link>
                </li>
                <li>
                  <Link
                    href="/seller/help"
                    className="marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                  >
                    Seller Help
                  </Link>
                </li>
              </ul>
            </div>

            {/* Support */}
            <div className="space-y-4 sm:space-y-5">
              <h3 className="text-sm sm:text-base font-semibold marketplace-text-primary">
                Support
              </h3>
              <ul className="space-y-2 sm:space-y-3 text-sm sm:text-base">
                <li>
                  <Link
                    href="/help"
                    className="marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                  >
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link
                    href="/privacy"
                    className="marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    href="/terms"
                    className="marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                  >
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link
                    href="/returns"
                    className="marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                  >
                    Returns & Refunds
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-8 sm:mt-10 pt-6 sm:pt-8 border-t border-gray-200">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <p className="text-sm sm:text-base marketplace-text-muted">
                © 2024 Duka Kuu. All rights reserved.
              </p>
              <div className="flex space-x-4 sm:space-x-6">
                <Link
                  href="/privacy"
                  className="text-sm sm:text-base marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                >
                  Privacy
                </Link>
                <Link
                  href="/terms"
                  className="text-sm sm:text-base marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                >
                  Terms
                </Link>
                <Link
                  href="/cookies"
                  className="text-sm sm:text-base marketplace-text-muted hover:marketplace-text-warm transition-colors duration-200"
                >
                  Cookies
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
