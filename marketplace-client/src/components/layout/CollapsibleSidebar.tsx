"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { useSidebar } from "@/lib/contexts/SidebarContext";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";

interface CollapsibleSidebarProps {
  children: React.ReactNode;
  className?: string;
}

export function CollapsibleSidebar({
  children,
  className,
}: CollapsibleSidebarProps) {
  const { isCollapsed, isMobile, toggleSidebar } = useSidebar();

  return (
    <>
      {/* Mobile Overlay - Enhanced with better accessibility */}
      {isMobile && !isCollapsed && (
        <div
          className="fixed inset-0 bg-black bg-opacity-60 z-40 lg:hidden backdrop-blur-sm"
          onClick={toggleSidebar}
          role="button"
          tabIndex={0}
          aria-label="Close sidebar"
          onKeyDown={(e) => {
            if (e.key === "Escape" || e.key === "Enter" || e.key === " ") {
              toggleSidebar();
            }
          }}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "bg-white shadow-lg transition-all duration-300 ease-in-out z-50",
          // Desktop positioning
          "fixed left-0 top-16 bottom-0",
          // Width based on collapsed state
          isCollapsed ? "w-0 lg:w-16" : "w-64",
          // Mobile specific styles - slide in from left with better animation
          isMobile && !isCollapsed && "w-64 shadow-2xl",
          isMobile && isCollapsed && "-translate-x-full lg:translate-x-0",
          className
        )}
      >
        {/* Toggle Button - Enhanced for mobile */}
        <div
          className={cn(
            "absolute top-6 z-10 transition-all duration-300",
            isMobile ? "right-4" : "-right-3"
          )}
        >
          <Button
            variant="outline"
            size="icon"
            onClick={toggleSidebar}
            className={cn(
              "rounded-full bg-white border-gray-300 shadow-md hover:shadow-lg transition-all",
              isMobile ? "h-8 w-8" : "h-6 w-6",
              // Hide on mobile when collapsed
              isMobile && isCollapsed && "hidden"
            )}
            aria-label={isCollapsed ? "Open sidebar" : "Close sidebar"}
          >
            {isCollapsed ? (
              <Menu className={cn(isMobile ? "h-4 w-4" : "h-3 w-3")} />
            ) : (
              <X className={cn(isMobile ? "h-4 w-4" : "h-3 w-3")} />
            )}
          </Button>
        </div>

        {/* Sidebar Content */}
        <div
          className={cn(
            "h-full overflow-hidden transition-all duration-300",
            isCollapsed && !isMobile && "opacity-0",
            (!isCollapsed || isMobile) && "opacity-100"
          )}
        >
          {children}
        </div>
      </div>

      {/* Enhanced Mobile Toggle Button (when sidebar is collapsed) */}
      {isMobile && isCollapsed && (
        <Button
          variant="outline"
          onClick={toggleSidebar}
          className="fixed top-20 left-4 z-[9999] h-12 px-4 rounded-full bg-white border-gray-300 shadow-lg hover:shadow-xl lg:hidden transition-all duration-200 hover:scale-105 cursor-pointer"
          aria-label="Open navigation menu"
        >
          <Menu className="h-5 w-5 mr-2" />
          <span className="text-sm font-medium">Menu</span>
        </Button>
      )}
    </>
  );
}

// Hook to get the appropriate margin for main content
export function useSidebarMargin() {
  const { isCollapsed, isMobile } = useSidebar();

  if (isMobile) {
    return "ml-0"; // No margin on mobile
  }

  return isCollapsed ? "ml-16" : "ml-64";
}
