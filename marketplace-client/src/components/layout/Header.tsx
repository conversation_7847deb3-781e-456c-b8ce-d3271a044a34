"use client";

import Link from "next/link";
import { useSelector, useDispatch } from "react-redux";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  selectCurrentUser,
  selectIsAuthenticated,
  logOut,
} from "@/lib/features/auth/authSlice";
import { useNotifications } from "@/lib/hooks/useNotifications";
import {
  ShoppingCart,
  User,
  LogOut,
  Settings,
  Package,
  MessageCircle,
} from "lucide-react";
import CartSidebar from "@/components/cart/CartSidebar";

export default function Header() {
  const user = useSelector(selectCurrentUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const dispatch = useDispatch();
  const notifications = useNotifications();

  const handleLogout = () => {
    dispatch(logOut());
    notifications.logoutSuccess();
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-orange-500"; // Burnt orange theme
      case "seller":
        return "bg-indigo-500"; // Indigo theme
      case "buyer":
        return "bg-gray-600"; // Neutral theme
      default:
        return "bg-gray-500";
    }
  };

  return (
    <header className="marketplace-nav border-b border-gray-200 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/90 sticky top-0 z-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3">
              <Package className="h-7 w-7 marketplace-text-warm" />
              <span className="text-xl font-bold marketplace-heading">
                Duka Kuu
              </span>
            </Link>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              href="/products"
              className="marketplace-nav-item text-sm font-medium"
            >
              Products
            </Link>
            {isAuthenticated && (
              <Link
                href="/chat"
                className="marketplace-nav-item text-sm font-medium flex items-center gap-2"
              >
                <MessageCircle className="h-4 w-4" />
                Chat
              </Link>
            )}
            {user?.role === "seller" && (
              <Link
                href="/seller/dashboard"
                className="marketplace-nav-item text-sm font-medium"
              >
                Seller Dashboard
              </Link>
            )}
            {user?.role === "admin" && (
              <Link
                href="/admin/dashboard"
                className="marketplace-nav-item text-sm font-medium"
              >
                Admin Dashboard
              </Link>
            )}
          </nav>

          {/* User Actions */}
          <div className="flex items-center space-x-4">
            {/* Shopping Cart */}
            <CartSidebar />

            {/* User Menu */}
            {isAuthenticated && user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="relative h-8 w-8 rounded-full"
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.avatar} alt={user.name} />
                      <AvatarFallback>
                        {user.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {user.name}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user.email}
                      </p>
                      <Badge
                        className={`w-fit text-xs ${getRoleColor(user.role)}`}
                      >
                        {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                      </Badge>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/profile">
                      <User className="mr-2 h-4 w-4" />
                      Profile
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/settings">
                      <Settings className="mr-2 h-4 w-4" />
                      Settings
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Log out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  asChild
                  className="marketplace-nav-item"
                >
                  <Link href="/auth/login">Login</Link>
                </Button>
                <Button asChild className="marketplace-btn-primary">
                  <Link href="/auth/register">Sign Up</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
