"use client";

import { useAuth } from "@/lib/contexts/AuthContext";
import { useRouter, usePathname } from "next/navigation";
import { useEffect, useRef } from "react";
import type { UserRole } from "@/lib/features/auth/authSlice";

interface AuthGuardProps {
  children: React.ReactNode;
  allowedRoles?: UserRole[];
  fallbackPath?: string;
  requireAuth?: boolean;
}

export default function AuthGuard({
  children,
  allowedRoles = [],
  fallbackPath = "/auth/login",
  requireAuth = true,
}: AuthGuardProps) {
  const { user, isAuthenticated, isLoading, isInitialized } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const hasCheckedAuth = useRef(false);
  const lastAuthState = useRef({ isAuthenticated, userRole: user?.role });
  const lastPathname = useRef(pathname);

  useEffect(() => {
    // Only run auth check when loading is complete and auth state has changed
    // Don't re-run on pathname changes unless auth state actually changed
    const authStateChanged =
      lastAuthState.current.isAuthenticated !== isAuthenticated ||
      lastAuthState.current.userRole !== user?.role;

    const isFirstLoad = !hasCheckedAuth.current;
    const pathnameChanged = lastPathname.current !== pathname;

    // Skip auth checks for certain scenarios
    const isAuthPage = pathname.startsWith("/auth/");
    const isPublicPage = pathname === "/" || pathname.startsWith("/products");
    const isNavigatingBack =
      pathnameChanged && !authStateChanged && isAuthenticated;

    // Check if user is navigating back from a 404 or invalid route
    const isValidAuthenticatedNavigation =
      isAuthenticated &&
      ((pathname.startsWith("/admin/") && user?.role === "admin") ||
        (pathname.startsWith("/seller/") && user?.role === "seller") ||
        isPublicPage ||
        isAuthPage);

    // Debug logging (remove in production)
    if (process.env.NODE_ENV === "development") {
      console.log("AuthGuard check:", {
        pathname,
        isAuthenticated,
        userRole: user?.role,
        allowedRoles,
        isFirstLoad,
        authStateChanged,
        pathnameChanged,
        isLoading,
        isInitialized,
        isAuthPage,
        isPublicPage,
        isNavigatingBack,
        isValidAuthenticatedNavigation,
        requireAuth,
      });
    }

    // Don't run auth checks if:
    // 1. Still loading or not initialized
    // 2. On auth pages (login/register)
    // 3. On public pages and auth not required
    // 4. Just navigating back while authenticated (browser navigation)
    // 5. Valid authenticated navigation (user has correct role for the route)
    if (
      isLoading ||
      !isInitialized ||
      isAuthPage ||
      (!requireAuth && isPublicPage) ||
      (isNavigatingBack && allowedRoles.length === 0) ||
      (isNavigatingBack && isValidAuthenticatedNavigation)
    ) {
      // Update tracking even if we skip the check
      if (!isLoading && isInitialized) {
        lastPathname.current = pathname;
      }
      return;
    }

    // Only check auth on:
    // 1. First load
    // 2. Auth state changes (login/logout/role change)
    // 3. Pathname changes to protected routes (but only if not already authenticated)
    const shouldCheckAuth =
      isFirstLoad ||
      authStateChanged ||
      (pathnameChanged && !isAuthenticated && requireAuth);

    if (shouldCheckAuth) {
      hasCheckedAuth.current = true;
      lastAuthState.current = { isAuthenticated, userRole: user?.role };
      lastPathname.current = pathname;

      // Check authentication requirement
      if (requireAuth && !isAuthenticated) {
        // Only store redirect path if it's not a login/auth page or public page
        if (!isAuthPage && !isPublicPage) {
          sessionStorage.setItem("redirectAfterLogin", pathname);
        }
        console.log("AuthGuard: Redirecting to login from", pathname);
        router.replace(fallbackPath);
        return;
      }

      // Check role permissions - only redirect if user is authenticated but wrong role
      if (isAuthenticated && allowedRoles.length > 0 && user) {
        if (!allowedRoles.includes(user.role)) {
          // Redirect based on user role
          const roleRedirects: Record<UserRole, string> = {
            admin: "/admin/dashboard",
            seller: "/seller/dashboard",
            buyer: "/products",
          };

          const redirectPath = roleRedirects[user.role] || "/products";

          // Only redirect if we're not already on the correct path
          if (pathname !== redirectPath) {
            console.log(
              "AuthGuard: Wrong role, redirecting",
              user.role,
              "from",
              pathname,
              "to",
              redirectPath
            );
            router.replace(redirectPath);
            return;
          }
        }
      }
    } else {
      // Just update pathname tracking for non-auth-checking navigation
      lastPathname.current = pathname;
    }
  }, [
    isLoading,
    isInitialized,
    isAuthenticated,
    user?.role,
    requireAuth,
    allowedRoles,
    router,
    fallbackPath,
    pathname, // Re-add pathname to dependencies but with better logic
  ]);

  // Reset auth check when user logs out
  useEffect(() => {
    if (!isAuthenticated) {
      hasCheckedAuth.current = false;
    }
  }, [isAuthenticated]);

  // Show loading state while loading or not initialized
  if (isLoading || !isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Don't render if auth requirements aren't met
  if (requireAuth && !isAuthenticated) {
    return null;
  }

  if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
    return null;
  }

  return <>{children}</>;
}
