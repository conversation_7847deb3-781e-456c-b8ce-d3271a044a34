"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAuth } from "@/lib/contexts/AuthContext";
import { useSidebar } from "@/lib/contexts/SidebarContext";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  MessageSquare,
  BarChart3,
  Settings,
  Store,
  Plus,
  LogOut,
  Warehouse,
} from "lucide-react";
import { Button } from "@/components/ui/button";

const navigation = [
  {
    name: "Dashboard",
    href: "/seller/dashboard",
    icon: LayoutDashboard,
  },
  {
    name: "Products",
    href: "/seller/products",
    icon: Package,
    children: [
      {
        name: "All Products",
        href: "/seller/products",
      },
      {
        name: "Add Product",
        href: "/seller/products/new",
        icon: Plus,
      },
    ],
  },
  {
    name: "Inventory",
    href: "/seller/inventory",
    icon: Warehouse,
  },
  {
    name: "Orders",
    href: "/seller/orders",
    icon: ShoppingCart,
  },
  {
    name: "Messages",
    href: "/seller/messages",
    icon: MessageSquare,
  },
  {
    name: "Analytics",
    href: "/seller/analytics",
    icon: BarChart3,
  },
  {
    name: "Settings",
    href: "/seller/settings",
    icon: Settings,
  },
];

export default function SellerNavigation() {
  const pathname = usePathname();
  const { user, logout } = useAuth();
  const { isCollapsed, isMobile } = useSidebar();

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header - Fixed */}
      <div
        className={cn(
          "flex-shrink-0 border-b border-gray-200 transition-all duration-300",
          isCollapsed && !isMobile ? "p-2" : isMobile ? "p-3" : "p-6"
        )}
      >
        <div
          className={cn(
            "flex items-center transition-all duration-300",
            isCollapsed && !isMobile ? "justify-center" : "space-x-3"
          )}
        >
          <div
            className={cn(
              "bg-orange-100 rounded-lg transition-all duration-300",
              isCollapsed && !isMobile ? "p-2" : isMobile ? "p-2" : "p-3"
            )}
          >
            <Store
              className={cn(
                "marketplace-text-warm transition-all duration-300",
                isCollapsed && !isMobile
                  ? "h-5 w-5"
                  : isMobile
                  ? "h-5 w-5"
                  : "h-6 w-6"
              )}
            />
          </div>
          {(!isCollapsed || isMobile) && (
            <div>
              <h2
                className={cn(
                  "font-semibold marketplace-text-primary",
                  isMobile ? "text-lg" : "text-xl"
                )}
              >
                Seller Portal
              </h2>
              <p className="text-sm marketplace-text-muted">{user?.name}</p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation - Scrollable */}
      <nav
        className={cn(
          "flex-1 overflow-y-auto space-y-2 transition-all duration-300",
          isCollapsed && !isMobile ? "p-2" : isMobile ? "p-3" : "p-5"
        )}
      >
        {navigation.map((item) => {
          const isActive =
            pathname === item.href || pathname.startsWith(item.href + "/");

          return (
            <div key={item.name}>
              <Link
                href={item.href}
                title={isCollapsed && !isMobile ? item.name : undefined}
                className={cn(
                  "flex items-center font-medium rounded-md transition-all duration-300 group relative",
                  isCollapsed && !isMobile
                    ? "px-2 py-3 justify-center text-sm"
                    : isMobile
                    ? "px-3 py-2 text-sm"
                    : "px-4 py-3 text-base",
                  isActive
                    ? "bg-orange-100 marketplace-text-warm"
                    : "marketplace-text-muted hover:marketplace-text-warm hover:bg-orange-50"
                )}
              >
                <item.icon
                  className={cn(
                    "transition-all duration-300",
                    isCollapsed && !isMobile
                      ? "h-5 w-5 mr-0"
                      : isMobile
                      ? "h-5 w-5 mr-3"
                      : "h-6 w-6 mr-4"
                  )}
                />
                {(!isCollapsed || isMobile) && (
                  <span className="transition-all duration-300">
                    {item.name}
                  </span>
                )}

                {/* Tooltip for collapsed state */}
                {isCollapsed && !isMobile && (
                  <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                    {item.name}
                  </div>
                )}
              </Link>

              {/* Sub-navigation - only show when not collapsed or on mobile */}
              {item.children && isActive && (!isCollapsed || isMobile) && (
                <div className="ml-8 mt-2 space-y-1">
                  {item.children.map((child) => (
                    <Link
                      key={child.name}
                      href={child.href}
                      className={cn(
                        "flex items-center px-3 py-2 text-sm rounded-md transition-colors",
                        pathname === child.href
                          ? "bg-orange-50 marketplace-text-warm"
                          : "marketplace-text-muted hover:marketplace-text-primary hover:bg-orange-50"
                      )}
                    >
                      {child.icon && <child.icon className="mr-2 h-4 w-4" />}
                      {child.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </nav>

      {/* Footer - Fixed at bottom */}
      <div
        className={cn(
          "flex-shrink-0 border-t border-gray-200 transition-all duration-300",
          isCollapsed && !isMobile ? "p-2" : isMobile ? "p-3" : "p-5"
        )}
      >
        <Button
          variant="ghost"
          onClick={logout}
          title={isCollapsed && !isMobile ? "Sign out" : undefined}
          className={cn(
            "w-full marketplace-text-muted hover:marketplace-text-warm hover:bg-orange-50 transition-all duration-300 group relative",
            isCollapsed && !isMobile
              ? "justify-center px-2 py-3"
              : isMobile
              ? "justify-start px-3 py-2"
              : "justify-start px-4 py-3"
          )}
        >
          <LogOut
            className={cn(
              "transition-all duration-300",
              isCollapsed && !isMobile
                ? "h-5 w-5 mr-0"
                : isMobile
                ? "h-5 w-5 mr-3"
                : "h-6 w-6 mr-4"
            )}
          />
          {(!isCollapsed || isMobile) && <span>Sign out</span>}

          {/* Tooltip for collapsed state */}
          {isCollapsed && !isMobile && (
            <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
              Sign out
            </div>
          )}
        </Button>
      </div>
    </div>
  );
}
