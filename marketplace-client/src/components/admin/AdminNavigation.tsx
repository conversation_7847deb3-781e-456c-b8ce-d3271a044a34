"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAuth } from "@/lib/contexts/AuthContext";
import { useSidebar } from "@/lib/contexts/SidebarContext";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Users,
  Package,
  ShoppingCart,
  BarChart3,
  Settings,
  Shield,
  Tags,
  LogOut,
  Crown,
  FileText,
  MessageSquare,
  Zap,
  Sparkles,
} from "lucide-react";
import { Button } from "@/components/ui/button";

const navigation = [
  {
    name: "Dashboard",
    href: "/admin/dashboard",
    icon: LayoutDashboard,
  },
  {
    name: "Analytics",
    href: "/admin/analytics",
    icon: BarChart3,
  },
  {
    name: "Users",
    href: "/admin/users",
    icon: Users,
  },
  {
    name: "Categories",
    href: "/admin/categories",
    icon: Tags,
  },
  {
    name: "Inventory",
    href: "/admin/inventory",
    icon: Package,
  },
  {
    name: "Orders",
    href: "/admin/orders",
    icon: ShoppingCart,
  },
  {
    name: "Reports",
    href: "/admin/reports",
    icon: FileText,
  },
  {
    name: "Messages",
    href: "/admin/messages",
    icon: MessageSquare,
  },
  {
    name: "Settings",
    href: "/admin/settings",
    icon: Settings,
  },
];

export default function AdminNavigation() {
  const pathname = usePathname();
  const { user, logout } = useAuth();
  const { isCollapsed, isMobile } = useSidebar();

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header - Fixed */}
      <div
        className={cn(
          "flex-shrink-0 border-b border-gray-200 transition-all duration-300",
          isCollapsed && !isMobile ? "p-2" : isMobile ? "p-3" : "p-6"
        )}
      >
        <div
          className={cn(
            "flex items-center transition-all duration-300",
            isCollapsed && !isMobile ? "justify-center" : "space-x-3"
          )}
        >
          <div
            className={cn(
              "bg-orange-100 rounded-lg transition-all duration-300",
              isCollapsed && !isMobile ? "p-2" : isMobile ? "p-2" : "p-3"
            )}
          >
            <Crown
              className={cn(
                "marketplace-text-warm transition-all duration-300",
                isCollapsed && !isMobile
                  ? "h-5 w-5"
                  : isMobile
                  ? "h-5 w-5"
                  : "h-6 w-6"
              )}
            />
          </div>
          {(!isCollapsed || isMobile) && (
            <div>
              <h2
                className={cn(
                  "font-bold marketplace-heading",
                  isMobile ? "text-lg" : "text-xl"
                )}
              >
                Admin Portal
              </h2>
              <p className="text-sm marketplace-text-muted">
                {user?.name || "Administrator"}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation - Scrollable */}
      <nav
        className={cn(
          "flex-1 overflow-y-auto space-y-2 transition-all duration-300",
          isCollapsed && !isMobile ? "p-2" : isMobile ? "p-3" : "p-5"
        )}
      >
        {navigation.map((item) => {
          const isActive =
            pathname === item.href || pathname.startsWith(item.href + "/");

          return (
            <Link
              key={item.name}
              href={item.href}
              title={isCollapsed && !isMobile ? item.name : undefined}
              className={cn(
                "flex items-center font-medium rounded-lg transition-all duration-300 group relative",
                isCollapsed && !isMobile
                  ? "px-2 py-3 justify-center text-sm"
                  : isMobile
                  ? "px-4 py-3 text-sm"
                  : "px-5 py-4 text-base",
                isActive
                  ? "bg-orange-100 marketplace-text-warm border border-orange-200"
                  : "marketplace-text-muted hover:bg-orange-50 hover:marketplace-text-warm border border-transparent"
              )}
            >
              <item.icon
                className={cn(
                  "transition-all duration-300",
                  isCollapsed && !isMobile
                    ? "h-5 w-5 mr-0"
                    : isMobile
                    ? "h-5 w-5 mr-3"
                    : "h-6 w-6 mr-4",
                  isActive
                    ? "marketplace-text-warm"
                    : "marketplace-text-muted group-hover:marketplace-text-warm"
                )}
              />
              {(!isCollapsed || isMobile) && (
                <span className="transition-all duration-300">{item.name}</span>
              )}

              {/* Tooltip for collapsed state */}
              {isCollapsed && !isMobile && (
                <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                  {item.name}
                </div>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Footer - Fixed at bottom */}
      <div
        className={cn(
          "flex-shrink-0 border-t border-gray-200 transition-all duration-300",
          isCollapsed && !isMobile ? "p-2" : isMobile ? "p-3" : "p-5"
        )}
      >
        <Button
          variant="ghost"
          onClick={logout}
          title={isCollapsed && !isMobile ? "Sign out" : undefined}
          className={cn(
            "w-full marketplace-text-muted hover:marketplace-text-warm hover:bg-orange-50 transition-all duration-300 group relative",
            isCollapsed && !isMobile
              ? "justify-center px-2 py-3"
              : isMobile
              ? "justify-start px-4 py-3"
              : "justify-start px-5 py-4"
          )}
        >
          <LogOut
            className={cn(
              "transition-all duration-300",
              isCollapsed && !isMobile
                ? "h-5 w-5 mr-0"
                : isMobile
                ? "h-5 w-5 mr-3"
                : "h-6 w-6 mr-4"
            )}
          />
          {(!isCollapsed || isMobile) && <span>Sign out</span>}

          {/* Tooltip for collapsed state */}
          {isCollapsed && !isMobile && (
            <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
              Sign out
            </div>
          )}
        </Button>
      </div>
    </div>
  );
}
