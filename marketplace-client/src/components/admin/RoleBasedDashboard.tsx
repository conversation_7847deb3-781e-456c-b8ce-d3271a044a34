'use client';

import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Crown, 
  Users, 
  ShoppingCart, 
  Monitor, 
  Activity, 
  Settings,
  BarChart3,
  Shield,
  Zap,
  Database,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Mail
} from 'lucide-react';
import { UserRole } from '@/lib/features/auth/authSlice';
import { ROLE_DESCRIPTIONS } from '@/lib/utils/permissions';
import Link from 'next/link';

interface RoleBasedDashboardProps {
  userRole: UserRole;
  userName?: string;
}

export const RoleBasedDashboard: React.FC<RoleBasedDashboardProps> = ({ 
  userRole, 
  userName 
}) => {
  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return <Crown className="h-8 w-8 text-orange-600" />;
      case 'business_admin':
        return <Users className="h-8 w-8 text-blue-600" />;
      case 'technical_admin':
        return <Monitor className="h-8 w-8 text-purple-600" />;
      default:
        return <Shield className="h-8 w-8 text-gray-600" />;
    }
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return 'bg-orange-50 border-orange-200 text-orange-800';
      case 'business_admin':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'technical_admin':
        return 'bg-purple-50 border-purple-200 text-purple-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const getQuickActions = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return [
          { name: 'User Management', href: '/admin/users', icon: Users },
          { name: 'System Monitoring', href: '/admin/technical-monitoring', icon: Monitor },
          { name: 'Analytics', href: '/admin/analytics', icon: BarChart3 },
          { name: 'Settings', href: '/admin/settings', icon: Settings },
        ];
      case 'business_admin':
        return [
          { name: 'Manage Users', href: '/admin/users', icon: Users },
          { name: 'View Orders', href: '/admin/orders', icon: ShoppingCart },
          { name: 'Handle Disputes', href: '/admin/disputes', icon: Shield },
          { name: 'Settings', href: '/admin/settings', icon: Settings },
        ];
      case 'technical_admin':
        return [
          { name: 'System Monitoring', href: '/admin/technical-monitoring', icon: Monitor },
          { name: 'Performance Metrics', href: '/admin/technical-monitoring?tab=performance', icon: Activity },
          { name: 'Email & DLQ', href: '/admin/technical-monitoring?tab=email-dlq', icon: Mail },
          { name: 'Real-time Metrics', href: '/admin/technical-monitoring?tab=realtime', icon: TrendingUp },
        ];
      default:
        return [];
    }
  };

  const getWelcomeMessage = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return 'You have full system access and control. Monitor all aspects of the marketplace.';
      case 'business_admin':
        return 'Manage users, orders, disputes, and business operations efficiently.';
      case 'technical_admin':
        return 'Monitor system health, performance metrics, and technical infrastructure.';
      default:
        return 'Welcome to the admin portal.';
    }
  };

  const quickActions = getQuickActions(userRole);

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <Card className={`border-2 ${getRoleColor(userRole)}`}>
        <CardHeader>
          <div className="flex items-center gap-4">
            {getRoleIcon(userRole)}
            <div>
              <CardTitle className="text-2xl">
                Welcome back, {userName || 'Administrator'}!
              </CardTitle>
              <p className="text-sm opacity-80 mt-1">
                {getWelcomeMessage(userRole)}
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className={getRoleColor(userRole)}>
              {userRole.replace('_', ' ').toUpperCase()}
            </Badge>
            <p className="text-sm text-gray-600">
              {ROLE_DESCRIPTIONS[userRole]}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="marketplace-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-yellow-500" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <Link key={action.name} href={action.href}>
                <Button 
                  variant="outline" 
                  className="w-full h-20 flex flex-col items-center gap-2 hover:bg-orange-50 hover:border-orange-200"
                >
                  <action.icon className="h-6 w-6" />
                  <span className="text-sm font-medium">{action.name}</span>
                </Button>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Role-Specific Content */}
      {userRole === 'technical_admin' && (
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-500" />
              System Status Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div>
                  <p className="font-medium text-green-800">System Healthy</p>
                  <p className="text-sm text-green-600">All services operational</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg">
                <Database className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-800">Database Online</p>
                  <p className="text-sm text-blue-600">Response time: 45ms</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-4 bg-purple-50 rounded-lg">
                <Mail className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="font-medium text-purple-800">Email Service</p>
                  <p className="text-sm text-purple-600">98.5% success rate</p>
                </div>
              </div>
            </div>
            <div className="mt-4 text-center">
              <Link href="/admin/technical-monitoring">
                <Button className="bg-purple-600 hover:bg-purple-700">
                  <Monitor className="h-4 w-4 mr-2" />
                  View Full Monitoring Dashboard
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}

      {userRole === 'business_admin' && (
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-500" />
              Business Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-blue-800">1,234</p>
                <p className="text-sm text-blue-600">Total Users</p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <ShoppingCart className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-green-800">567</p>
                <p className="text-sm text-green-600">Active Orders</p>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <AlertTriangle className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-yellow-800">12</p>
                <p className="text-sm text-yellow-600">Pending Disputes</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {userRole === 'admin' && (
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5 text-orange-500" />
              System Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <Users className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-orange-800">1,234</p>
                <p className="text-sm text-orange-600">Total Users</p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <ShoppingCart className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-green-800">567</p>
                <p className="text-sm text-green-600">Orders</p>
              </div>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <Activity className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-blue-800">99.9%</p>
                <p className="text-sm text-blue-600">Uptime</p>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <Monitor className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-purple-800">Healthy</p>
                <p className="text-sm text-purple-600">System Status</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
