'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { 
  Activity, 
  Zap, 
  RefreshCw, 
  Pause,
  Play,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';

interface MetricPoint {
  timestamp: string;
  responseTime: number;
  requestCount: number;
  errorRate: number;
  memoryUsage: number;
  cpuUsage: number;
}

interface RealTimeMetricsProps {
  isLoading?: boolean;
}

const generateMockData = (): MetricPoint => {
  const now = new Date();
  return {
    timestamp: now.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    }),
    responseTime: Math.random() * 500 + 100, // 100-600ms
    requestCount: Math.floor(Math.random() * 50) + 10, // 10-60 requests
    errorRate: Math.random() * 5, // 0-5% error rate
    memoryUsage: Math.random() * 20 + 60, // 60-80% memory usage
    cpuUsage: Math.random() * 30 + 20, // 20-50% CPU usage
  };
};

const formatNumber = (num: number) => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
  return num.toFixed(0);
};

const getTrendIcon = (current: number, previous: number) => {
  if (current > previous * 1.05) return <TrendingUp className="h-4 w-4 text-red-500" />;
  if (current < previous * 0.95) return <TrendingDown className="h-4 w-4 text-green-500" />;
  return <Minus className="h-4 w-4 text-gray-500" />;
};

const getTrendColor = (current: number, previous: number, inverse = false) => {
  const isIncreasing = current > previous * 1.05;
  const isDecreasing = current < previous * 0.95;
  
  if (inverse) {
    if (isIncreasing) return 'text-green-600';
    if (isDecreasing) return 'text-red-600';
  } else {
    if (isIncreasing) return 'text-red-600';
    if (isDecreasing) return 'text-green-600';
  }
  return 'text-gray-600';
};

export const RealTimeMetrics: React.FC<RealTimeMetricsProps> = ({ isLoading }) => {
  const [metrics, setMetrics] = useState<MetricPoint[]>([]);
  const [isRealTime, setIsRealTime] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    if (!isRealTime) return;

    const interval = setInterval(() => {
      const newPoint = generateMockData();
      setMetrics(prev => {
        const updated = [...prev, newPoint];
        // Keep only last 20 points
        return updated.slice(-20);
      });
      setLastUpdate(new Date());
    }, 2000); // Update every 2 seconds

    return () => clearInterval(interval);
  }, [isRealTime]);

  // Initialize with some data
  useEffect(() => {
    const initialData = Array.from({ length: 10 }, () => generateMockData());
    setMetrics(initialData);
  }, []);

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-48 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const currentMetrics = metrics[metrics.length - 1];
  const previousMetrics = metrics[metrics.length - 2];

  if (!currentMetrics) return null;

  return (
    <div className="space-y-6">
      {/* Real-time Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold">Real-time Monitoring</h3>
          <Badge variant={isRealTime ? 'default' : 'secondary'}>
            {isRealTime ? 'LIVE' : 'PAUSED'}
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">
            Last update: {lastUpdate.toLocaleTimeString()}
          </span>
          <Button
            size="sm"
            variant="outline"
            onClick={() => setIsRealTime(!isRealTime)}
          >
            {isRealTime ? (
              <>
                <Pause className="h-4 w-4 mr-1" />
                Pause
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-1" />
                Resume
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Current Metrics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card className="marketplace-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">Response Time</p>
                <p className="text-lg font-bold">{currentMetrics.responseTime.toFixed(0)}ms</p>
              </div>
              {previousMetrics && getTrendIcon(currentMetrics.responseTime, previousMetrics.responseTime)}
            </div>
          </CardContent>
        </Card>

        <Card className="marketplace-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">Requests/Min</p>
                <p className="text-lg font-bold">{formatNumber(currentMetrics.requestCount * 3)}</p>
              </div>
              {previousMetrics && getTrendIcon(currentMetrics.requestCount, previousMetrics.requestCount, true)}
            </div>
          </CardContent>
        </Card>

        <Card className="marketplace-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">Error Rate</p>
                <p className="text-lg font-bold">{currentMetrics.errorRate.toFixed(1)}%</p>
              </div>
              {previousMetrics && getTrendIcon(currentMetrics.errorRate, previousMetrics.errorRate)}
            </div>
          </CardContent>
        </Card>

        <Card className="marketplace-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">Memory</p>
                <p className="text-lg font-bold">{currentMetrics.memoryUsage.toFixed(0)}%</p>
              </div>
              {previousMetrics && getTrendIcon(currentMetrics.memoryUsage, previousMetrics.memoryUsage)}
            </div>
          </CardContent>
        </Card>

        <Card className="marketplace-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">CPU Usage</p>
                <p className="text-lg font-bold">{currentMetrics.cpuUsage.toFixed(0)}%</p>
              </div>
              {previousMetrics && getTrendIcon(currentMetrics.cpuUsage, previousMetrics.cpuUsage)}
            </div>
          </CardContent>
        </Card>

        <Card className="marketplace-card">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">Status</p>
                <p className="text-lg font-bold text-green-600">Healthy</p>
              </div>
              <Activity className="h-4 w-4 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Real-time Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Response Time Chart */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-blue-500" />
              Response Time Trend
            </CardTitle>
            <CardDescription>
              Real-time response time monitoring
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={metrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tick={{ fontSize: 10 }}
                    interval="preserveStartEnd"
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip 
                    formatter={(value: any) => [`${value.toFixed(0)}ms`, 'Response Time']}
                    labelFormatter={(label) => `Time: ${label}`}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="responseTime" 
                    stroke="#3b82f6" 
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Request Volume Chart */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-500" />
              Request Volume
            </CardTitle>
            <CardDescription>
              Real-time request volume monitoring
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={metrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tick={{ fontSize: 10 }}
                    interval="preserveStartEnd"
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip 
                    formatter={(value: any) => [formatNumber(value * 3), 'Requests/Min']}
                    labelFormatter={(label) => `Time: ${label}`}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="requestCount" 
                    stroke="#10b981" 
                    fill="#10b981"
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Error Rate Chart */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-red-500" />
              Error Rate
            </CardTitle>
            <CardDescription>
              Real-time error rate monitoring
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={metrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tick={{ fontSize: 10 }}
                    interval="preserveStartEnd"
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip 
                    formatter={(value: any) => [`${value.toFixed(1)}%`, 'Error Rate']}
                    labelFormatter={(label) => `Time: ${label}`}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="errorRate" 
                    stroke="#ef4444" 
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* System Resources Chart */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-purple-500" />
              System Resources
            </CardTitle>
            <CardDescription>
              Memory and CPU usage monitoring
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={metrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tick={{ fontSize: 10 }}
                    interval="preserveStartEnd"
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip 
                    formatter={(value: any, name: string) => [
                      `${value.toFixed(1)}%`, 
                      name === 'memoryUsage' ? 'Memory' : 'CPU'
                    ]}
                    labelFormatter={(label) => `Time: ${label}`}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="memoryUsage" 
                    stroke="#8b5cf6" 
                    strokeWidth={2}
                    dot={false}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="cpuUsage" 
                    stroke="#f59e0b" 
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
