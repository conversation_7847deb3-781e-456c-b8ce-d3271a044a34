'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { 
  TrendingUp, 
  Zap, 
  Clock, 
  Activity,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { PerformanceMetrics, TimeoutAnalytics } from '@/lib/api/monitoringApi';

interface PerformanceChartsProps {
  performanceData: PerformanceMetrics;
  timeoutData: TimeoutAnalytics;
  isLoading?: boolean;
}

const COLORS = {
  primary: '#3b82f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  purple: '#8b5cf6',
  indigo: '#6366f1',
  pink: '#ec4899',
  teal: '#14b8a6',
};

const formatDuration = (ms: number) => {
  if (ms < 1000) return `${ms.toFixed(0)}ms`;
  return `${(ms / 1000).toFixed(1)}s`;
};

const formatNumber = (num: number) => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
  return num.toString();
};

export const PerformanceCharts: React.FC<PerformanceChartsProps> = ({ 
  performanceData, 
  timeoutData, 
  isLoading 
}) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Prepare endpoint performance data for charts
  const endpointData = performanceData.endpointAnalysis.slice(0, 10).map(([endpoint, stats]) => ({
    endpoint: endpoint.replace('/api/', '').substring(0, 20) + (endpoint.length > 20 ? '...' : ''),
    fullEndpoint: endpoint,
    avgDuration: stats.avgDuration,
    count: stats.count,
    slowRequests: stats.slowRequests,
    minDuration: stats.minDuration,
    maxDuration: stats.maxDuration,
  }));

  // Prepare performance overview data
  const performanceOverview = [
    {
      name: 'Fast Requests',
      value: performanceData.stats.totalRequests - performanceData.stats.slowRequests - performanceData.stats.verySlowRequests,
      color: COLORS.success,
    },
    {
      name: 'Slow Requests',
      value: performanceData.stats.slowRequests,
      color: COLORS.warning,
    },
    {
      name: 'Very Slow Requests',
      value: performanceData.stats.verySlowRequests,
      color: COLORS.danger,
    },
  ];

  // Prepare timeout trends data
  const timeoutTrendsData = timeoutData.trends.slice(-20).map((trend, index) => ({
    time: new Date(trend.timestamp).toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit' 
    }),
    count: trend.count,
    type: trend.type,
    index,
  }));

  // Prepare timeout by type data
  const timeoutByTypeData = Object.entries(timeoutData.timeoutsByType).map(([type, stats]) => ({
    type: type.charAt(0).toUpperCase() + type.slice(1),
    count: stats.count,
    avgDuration: stats.avgDuration,
    color: type === 'route' ? COLORS.danger : type === 'controller' ? COLORS.warning : COLORS.purple,
  }));

  return (
    <div className="space-y-6">
      {/* Performance Overview Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Response Time Distribution */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-500" />
              Response Time Distribution
            </CardTitle>
            <CardDescription>
              Request performance breakdown ({formatNumber(performanceData.stats.totalRequests)} total)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={performanceOverview}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {performanceOverview.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value: any) => [formatNumber(value), 'Requests']} />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {((performanceOverview[0].value / performanceData.stats.totalRequests) * 100).toFixed(1)}%
                </div>
                <div className="text-xs text-gray-600">Fast (&lt;500ms)</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-yellow-600">
                  {((performanceOverview[1].value / performanceData.stats.totalRequests) * 100).toFixed(1)}%
                </div>
                <div className="text-xs text-gray-600">Slow (500ms-2s)</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {((performanceOverview[2].value / performanceData.stats.totalRequests) * 100).toFixed(1)}%
                </div>
                <div className="text-xs text-gray-600">Very Slow (&gt;2s)</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Metrics Summary */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-500" />
              Performance Metrics
            </CardTitle>
            <CardDescription>
              Current system performance indicators
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatDuration(performanceData.stats.avgResponseTime)}
                  </div>
                  <div className="text-sm text-gray-600">Average Response Time</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {performanceData.stats.requestsPerMinute.toFixed(1)}
                  </div>
                  <div className="text-sm text-gray-600">Requests/Minute</div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {formatDuration(performanceData.stats.minResponseTime)}
                  </div>
                  <div className="text-sm text-gray-600">Fastest Response</div>
                </div>
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {formatDuration(performanceData.stats.maxResponseTime)}
                  </div>
                  <div className="text-sm text-gray-600">Slowest Response</div>
                </div>
              </div>

              <div className="flex items-center justify-center gap-2 pt-2">
                {performanceData.healthStatus.status === 'healthy' ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                )}
                <Badge variant={performanceData.healthStatus.status === 'healthy' ? 'default' : 'destructive'}>
                  {performanceData.healthStatus.status.toUpperCase()}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Slowest Endpoints */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-purple-500" />
              Slowest Endpoints
            </CardTitle>
            <CardDescription>
              Top 10 endpoints by average response time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={endpointData} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" tick={{ fontSize: 12 }} />
                  <YAxis 
                    type="category" 
                    dataKey="endpoint" 
                    tick={{ fontSize: 10 }}
                    width={100}
                  />
                  <Tooltip 
                    formatter={(value: any, name: string) => [
                      name === 'avgDuration' ? formatDuration(value) : formatNumber(value),
                      name === 'avgDuration' ? 'Avg Duration' : 'Request Count'
                    ]}
                    labelFormatter={(label: any, payload: any) => {
                      const data = payload?.[0]?.payload;
                      return data ? data.fullEndpoint : label;
                    }}
                  />
                  <Bar dataKey="avgDuration" fill={COLORS.purple} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Timeout Analysis */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-red-500" />
              Timeout Analysis
            </CardTitle>
            <CardDescription>
              Timeout occurrences by type and trends
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Timeout by Type */}
              <div className="h-32">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={timeoutByTypeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="type" tick={{ fontSize: 12 }} />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip formatter={(value: any) => [formatNumber(value), 'Count']} />
                    <Bar dataKey="count" fill={COLORS.danger} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              
              {/* Timeout Stats */}
              <div className="grid grid-cols-3 gap-2 text-center">
                <div className="p-2 bg-red-50 rounded">
                  <div className="text-lg font-bold text-red-600">
                    {timeoutData.stats.totalTimeouts}
                  </div>
                  <div className="text-xs text-gray-600">Total</div>
                </div>
                <div className="p-2 bg-yellow-50 rounded">
                  <div className="text-lg font-bold text-yellow-600">
                    {timeoutData.stats.timeoutRate.toFixed(2)}%
                  </div>
                  <div className="text-xs text-gray-600">Rate</div>
                </div>
                <div className="p-2 bg-purple-50 rounded">
                  <div className="text-lg font-bold text-purple-600">
                    {timeoutData.stats.timeoutsLastHour}
                  </div>
                  <div className="text-xs text-gray-600">Last Hour</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
