'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { 
  Mail, 
  Database, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  Activity
} from 'lucide-react';
import { EmailMetrics, DLQStats } from '@/lib/api/monitoringApi';

interface EmailDLQChartsProps {
  emailData: EmailMetrics;
  dlqData: DLQStats;
  isLoading?: boolean;
  onProcessDLQ?: () => void;
  onCleanupDLQ?: () => void;
}

const COLORS = {
  primary: '#3b82f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  purple: '#8b5cf6',
  indigo: '#6366f1',
  pink: '#ec4899',
  teal: '#14b8a6',
  gray: '#6b7280',
};

const formatNumber = (num: number) => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
  return num.toString();
};

const formatDuration = (ms: number) => {
  if (ms < 1000) return `${ms.toFixed(0)}ms`;
  return `${(ms / 1000).toFixed(1)}s`;
};

export const EmailDLQCharts: React.FC<EmailDLQChartsProps> = ({ 
  emailData, 
  dlqData, 
  isLoading,
  onProcessDLQ,
  onCleanupDLQ
}) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Prepare email performance by type data
  const emailTypeData = Object.entries(emailData.performanceByType).map(([type, stats]) => ({
    type: type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    count: stats.count,
    avgDuration: stats.avgDuration,
    successRate: stats.successRate,
    lastSent: stats.lastSent,
  }));

  // Prepare email success/failure data
  const emailOverviewData = [
    {
      name: 'Successful',
      value: emailData.metrics.totalSent - emailData.metrics.totalFailed,
      color: COLORS.success,
    },
    {
      name: 'Failed',
      value: emailData.metrics.totalFailed,
      color: COLORS.danger,
    },
  ];

  // Prepare DLQ status distribution
  const dlqStatusData = [
    {
      name: 'Pending',
      value: dlqData.stats.pendingEntries,
      color: COLORS.warning,
    },
    {
      name: 'Processing',
      value: dlqData.stats.processingEntries,
      color: COLORS.primary,
    },
    {
      name: 'Resolved',
      value: dlqData.stats.resolvedEntries,
      color: COLORS.success,
    },
    {
      name: 'Failed Permanently',
      value: dlqData.stats.failedPermanently,
      color: COLORS.danger,
    },
    {
      name: 'Cancelled',
      value: dlqData.stats.cancelledEntries,
      color: COLORS.gray,
    },
  ].filter(item => item.value > 0);

  // Prepare DLQ by email type data
  const dlqByEmailTypeData = Object.entries(dlqData.stats.byEmailType).map(([type, count]) => ({
    type: type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    count,
  }));

  // Prepare DLQ by failure reason data
  const dlqByFailureReasonData = Object.entries(dlqData.stats.byFailureReason).map(([reason, count]) => ({
    reason: reason.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    count,
  }));

  return (
    <div className="space-y-6">
      {/* Email Service Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Email Success Rate */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5 text-blue-500" />
              Email Service Performance
            </CardTitle>
            <CardDescription>
              Overall email delivery success rate
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="h-48">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={emailOverviewData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(1)}%`}
                      outerRadius={60}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {emailOverviewData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: any) => [formatNumber(value), 'Emails']} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-xl font-bold text-green-600">
                    {emailData.metrics.successRate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-600">Success Rate</div>
                </div>
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-xl font-bold text-blue-600">
                    {formatDuration(emailData.metrics.avgResponseTime)}
                  </div>
                  <div className="text-sm text-gray-600">Avg Response</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-xl font-bold text-purple-600">
                    {formatNumber(emailData.metrics.lastHourSent)}
                  </div>
                  <div className="text-sm text-gray-600">Last Hour Sent</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="text-xl font-bold text-red-600">
                    {formatNumber(emailData.metrics.lastHourFailed)}
                  </div>
                  <div className="text-sm text-gray-600">Last Hour Failed</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Email Performance by Type */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-purple-500" />
              Performance by Email Type
            </CardTitle>
            <CardDescription>
              Email delivery performance breakdown by type
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={emailTypeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="type" 
                    tick={{ fontSize: 10 }}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip 
                    formatter={(value: any, name: string) => [
                      name === 'avgDuration' ? formatDuration(value) : 
                      name === 'successRate' ? `${value.toFixed(1)}%` :
                      formatNumber(value),
                      name === 'avgDuration' ? 'Avg Duration' :
                      name === 'successRate' ? 'Success Rate' : 'Count'
                    ]}
                  />
                  <Bar dataKey="count" fill={COLORS.primary} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Dead Letter Queue Management */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* DLQ Status Distribution */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-red-500" />
              Dead Letter Queue Status
            </CardTitle>
            <CardDescription className="flex items-center justify-between">
              <span>Current DLQ entry distribution</span>
              <div className="flex gap-2">
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={onProcessDLQ}
                  className="text-xs"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Process
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={onCleanupDLQ}
                  className="text-xs"
                >
                  <XCircle className="h-3 w-3 mr-1" />
                  Cleanup
                </Button>
              </div>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="h-48">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={dlqStatusData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={60}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {dlqStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: any) => [formatNumber(value), 'Entries']} />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              <div className="grid grid-cols-2 gap-2 text-center">
                <div className="p-2 bg-yellow-50 rounded">
                  <div className="text-lg font-bold text-yellow-600">
                    {dlqData.stats.pendingEntries}
                  </div>
                  <div className="text-xs text-gray-600">Pending</div>
                </div>
                <div className="p-2 bg-red-50 rounded">
                  <div className="text-lg font-bold text-red-600">
                    {dlqData.stats.failedPermanently}
                  </div>
                  <div className="text-xs text-gray-600">Failed</div>
                </div>
              </div>

              <div className="flex items-center justify-center gap-2">
                {dlqData.healthStatus.status === 'healthy' ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : dlqData.healthStatus.status === 'warning' ? (
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <Badge variant={dlqData.healthStatus.status === 'healthy' ? 'default' : 'destructive'}>
                  {dlqData.healthStatus.status.toUpperCase()}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* DLQ Failure Analysis */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-orange-500" />
              Failure Analysis
            </CardTitle>
            <CardDescription>
              DLQ entries by failure reason and email type
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Failure Reasons */}
              <div>
                <h4 className="text-sm font-medium mb-2">By Failure Reason</h4>
                <div className="h-32">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={dlqByFailureReasonData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="reason" 
                        tick={{ fontSize: 10 }}
                        angle={-45}
                        textAnchor="end"
                        height={40}
                      />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip formatter={(value: any) => [formatNumber(value), 'Count']} />
                      <Bar dataKey="count" fill={COLORS.danger} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Email Types */}
              <div>
                <h4 className="text-sm font-medium mb-2">By Email Type</h4>
                <div className="h-32">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={dlqByEmailTypeData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="type" 
                        tick={{ fontSize: 10 }}
                        angle={-45}
                        textAnchor="end"
                        height={40}
                      />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip formatter={(value: any) => [formatNumber(value), 'Count']} />
                      <Bar dataKey="count" fill={COLORS.warning} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
