"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Activity,
  Mail,
  RefreshCw,
  Zap,
  Clock,
  Database,
  Server,
  MemoryStick,
  AlertTriangle,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { SystemHealth } from "@/lib/api/monitoringApi";
import { formatDistanceToNow } from "date-fns";

interface SystemHealthCardsProps {
  data: SystemHealth;
  isLoading?: boolean;
}

const getStatusColor = (status: "healthy" | "warning" | "critical") => {
  switch (status) {
    case "healthy":
      return "text-green-600 bg-green-50 border-green-200";
    case "warning":
      return "text-yellow-600 bg-yellow-50 border-yellow-200";
    case "critical":
      return "text-red-600 bg-red-50 border-red-200";
    default:
      return "text-gray-600 bg-gray-50 border-gray-200";
  }
};

const getStatusIcon = (status: "healthy" | "warning" | "critical") => {
  switch (status) {
    case "healthy":
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case "warning":
      return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    case "critical":
      return <XCircle className="h-4 w-4 text-red-600" />;
    default:
      return <Activity className="h-4 w-4 text-gray-600" />;
  }
};

const formatBytes = (bytes: number) => {
  const sizes = ["Bytes", "KB", "MB", "GB"];
  if (bytes === 0) return "0 Bytes";
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
};

const formatUptime = (seconds: number) => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) return `${days}d ${hours}h ${minutes}m`;
  if (hours > 0) return `${hours}h ${minutes}m`;
  return `${minutes}m`;
};

export const SystemHealthCards: React.FC<SystemHealthCardsProps> = ({
  data,
  isLoading,
}) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const memoryUsagePercent =
    (data.memory.heapUsed / data.memory.heapTotal) * 100;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {/* Overall System Health */}
      <Card className={`border-2 ${getStatusColor(data.overall.status)}`}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Activity className="h-4 w-4" />
            System Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 mb-2">
            {getStatusIcon(data.overall.status)}
            <Badge
              variant={
                data.overall.status === "healthy" ? "default" : "destructive"
              }
            >
              {data.overall.status.toUpperCase()}
            </Badge>
          </div>
          <p className="text-xs text-gray-600">
            Last updated:{" "}
            {formatDistanceToNow(new Date(data.overall.timestamp), {
              addSuffix: true,
            })}
          </p>
        </CardContent>
      </Card>

      {/* Email Service */}
      <Card className={`border ${getStatusColor(data.email.status)}`}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Email Service
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              {getStatusIcon(data.email.status)}
              <span className="text-sm font-medium">{data.email.status}</span>
            </div>
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span>Success Rate:</span>
                <span className="font-medium">
                  {data.email.metrics.successRate.toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between">
                <span>Total Sent:</span>
                <span className="font-medium">
                  {data.email.metrics.totalSent.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Failed:</span>
                <span className="font-medium text-red-600">
                  {data.email.metrics.totalFailed.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Retry Service */}
      <Card className={`border ${getStatusColor(data.retryService.status)}`}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Retry Service
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              {getStatusIcon(data.retryService.status)}
              <span className="text-sm font-medium">
                {data.retryService.status}
              </span>
            </div>
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span>Total Retries:</span>
                <span className="font-medium">
                  {data.retryService.metrics.totalRetries.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Successful:</span>
                <span className="font-medium text-green-600">
                  {data.retryService.metrics.successfulRetries.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Failed:</span>
                <span className="font-medium text-red-600">
                  {data.retryService.metrics.failedRetries.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance */}
      <Card className={`border ${getStatusColor(data.performance.status)}`}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              {getStatusIcon(data.performance.status)}
              <span className="text-sm font-medium">
                {data.performance.status}
              </span>
            </div>
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span>Avg Response:</span>
                <span className="font-medium">
                  {data.performance.metrics.avgResponseTime.toFixed(0)}ms
                </span>
              </div>
              <div className="flex justify-between">
                <span>Total Requests:</span>
                <span className="font-medium">
                  {data.performance.metrics.totalRequests.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Slow Requests:</span>
                <span className="font-medium text-yellow-600">
                  {data.performance.metrics.slowRequests.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Timeouts */}
      <Card className={`border ${getStatusColor(data.timeouts.status)}`}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Timeouts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              {getStatusIcon(data.timeouts.status)}
              <span className="text-sm font-medium">
                {data.timeouts.status}
              </span>
            </div>
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span>Total Timeouts:</span>
                <span className="font-medium">
                  {data.timeouts.metrics.totalTimeouts.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Timeout Rate:</span>
                <span className="font-medium">
                  {data.timeouts.metrics.timeoutRate.toFixed(2)}%
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dead Letter Queue */}
      <Card className={`border ${getStatusColor(data.deadLetterQueue.status)}`}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Database className="h-4 w-4" />
            Dead Letter Queue
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              {getStatusIcon(data.deadLetterQueue.status)}
              <span className="text-sm font-medium">
                {data.deadLetterQueue.status}
              </span>
            </div>
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span>Total Entries:</span>
                <span className="font-medium">
                  {data.deadLetterQueue.metrics.totalEntries.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Pending:</span>
                <span className="font-medium text-yellow-600">
                  {data.deadLetterQueue.metrics.pendingEntries.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Failed:</span>
                <span className="font-medium text-red-600">
                  {data.deadLetterQueue.metrics.failedPermanently.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Server Uptime */}
      <Card className="border border-blue-200 bg-blue-50">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Server className="h-4 w-4 text-blue-600" />
            Server Uptime
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="text-2xl font-bold text-blue-600">
              {formatUptime(data.uptime)}
            </div>
            <p className="text-xs text-gray-600">
              Server has been running continuously
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Memory Usage */}
      <Card className="border border-purple-200 bg-purple-50">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <MemoryStick className="h-4 w-4 text-purple-600" />
            Memory Usage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span>Heap Used:</span>
              <span className="font-medium">
                {formatBytes(data.memory.heapUsed)}
              </span>
            </div>
            <Progress value={memoryUsagePercent} className="h-2" />
            <div className="flex justify-between text-xs text-gray-600">
              <span>{memoryUsagePercent.toFixed(1)}% used</span>
              <span>{formatBytes(data.memory.heapTotal)} total</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
