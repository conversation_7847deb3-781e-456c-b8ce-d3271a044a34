"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertTriangle,
  Package,
  Plus,
  Minus,
  Eye,
  X,
  RefreshCw,
  TrendingDown,
} from "lucide-react";
import {
  useGetLowStockItemsQuery,
  useAdjustStockMutation,
  Inventory,
} from "@/lib/api/inventory";
import { useNotifications } from "@/lib/providers/NotificationProvider";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface LowStockAlertProps {
  onClose?: () => void;
  showHeader?: boolean;
  maxItems?: number;
}

export function LowStockAlert({
  onClose,
  showHeader = true,
  maxItems = 5,
}: LowStockAlertProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const notifications = useNotifications();

  const {
    data: lowStockData,
    isLoading,
    error,
    refetch,
  } = useGetLowStockItemsQuery();

  const [adjustStock] = useAdjustStockMutation();

  const lowStockItems = lowStockData?.data || [];
  const displayItems = isExpanded
    ? lowStockItems
    : lowStockItems.slice(0, maxItems);

  const handleQuickRestock = async (
    inventoryId: string,
    currentStock: number,
    reorderQuantity: number
  ) => {
    try {
      const restockAmount = reorderQuantity || 10; // Default to 10 if no reorder quantity set
      await adjustStock({
        inventoryId,
        newQuantity: currentStock + restockAmount,
        reason: `Quick restock: +${restockAmount} units`,
      }).unwrap();

      notifications.success(
        `Stock updated successfully! Added ${restockAmount} units.`
      );
      refetch();
    } catch (error: any) {
      notifications.error(error?.data?.message || "Failed to update stock");
    }
  };

  const getUrgencyLevel = (currentStock: number, threshold: number) => {
    if (currentStock === 0) return "critical";
    if (currentStock <= threshold / 2) return "high";
    return "medium";
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "critical":
        return "bg-red-100 text-red-800 border-red-200";
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  if (isLoading) {
    return (
      <Card className="marketplace-card">
        <CardContent className="p-6 text-center">
          <LoadingSpinner size="sm" />
          <p className="text-sm marketplace-text-muted mt-2">
            Checking stock levels...
          </p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="marketplace-card border-red-200">
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
          <p className="text-sm text-red-600 mb-3">
            Failed to load stock alerts
          </p>
          <Button size="sm" variant="outline" onClick={() => refetch()}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (lowStockItems.length === 0) {
    return (
      <Card className="marketplace-card border-green-200">
        <CardContent className="p-6 text-center">
          <Package className="h-8 w-8 text-green-500 mx-auto mb-2" />
          <p className="text-sm text-green-600 font-medium">
            All products are well stocked!
          </p>
          <p className="text-xs marketplace-text-muted mt-1">
            No low stock alerts at this time
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white border border-orange-200 shadow-sm">
      {showHeader && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <CardTitle className="text-lg text-gray-900">
                  Low Stock Alert
                </CardTitle>
                <CardDescription className="text-gray-600">
                  {lowStockItems.length} product
                  {lowStockItems.length !== 1 ? "s" : ""} need
                  {lowStockItems.length === 1 ? "s" : ""} attention
                </CardDescription>
              </div>
            </div>
            {onClose && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardHeader>
      )}

      <CardContent className="p-0">
        <div className="space-y-3 p-4 sm:p-6">
          {displayItems.map((item) => {
            const urgency = getUrgencyLevel(
              item.currentStock,
              item.lowStockThreshold
            );

            return (
              <div
                key={item.id}
                className="flex items-center justify-between p-3 sm:p-4 bg-orange-50 rounded-lg border border-orange-100"
              >
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white rounded-lg flex items-center justify-center border border-orange-200">
                    {item.product.images?.[0] ? (
                      <img
                        src={item.product.images[0]}
                        alt={item.product.title}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <Package className="h-5 w-5 sm:h-6 sm:w-6 text-orange-400" />
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold marketplace-text-primary text-sm sm:text-base truncate">
                      {item.product.title}
                    </h4>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={`text-xs ${getUrgencyColor(urgency)}`}>
                        {urgency.toUpperCase()}
                      </Badge>
                      <span className="text-xs marketplace-text-muted">
                        {item.currentStock} / {item.lowStockThreshold} threshold
                      </span>
                    </div>
                    {item.sku && (
                      <p className="text-xs marketplace-text-muted font-mono mt-1">
                        SKU: {item.sku}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-2 ml-3">
                  <div className="text-right">
                    <p className="text-lg sm:text-xl font-bold text-orange-600">
                      {item.currentStock}
                    </p>
                    <p className="text-xs marketplace-text-muted">in stock</p>
                  </div>

                  <div className="flex flex-col gap-1">
                    <Button
                      size="sm"
                      onClick={() =>
                        handleQuickRestock(
                          item.id,
                          item.currentStock,
                          item.reorderQuantity
                        )
                      }
                      className="h-8 px-3 bg-orange-500 hover:bg-orange-600 text-white"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      <span className="hidden sm:inline">Restock</span>
                    </Button>

                    <Button
                      size="sm"
                      variant="outline"
                      className="h-8 px-3 border-orange-300 text-orange-600 hover:bg-orange-50"
                    >
                      <Eye className="h-3 w-3" />
                      <span className="hidden sm:inline ml-1">View</span>
                    </Button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {lowStockItems.length > maxItems && (
          <div className="border-t border-orange-200 p-4 text-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
            >
              {isExpanded ? (
                <>Show Less</>
              ) : (
                <>
                  Show {lowStockItems.length - maxItems} More
                  <TrendingDown className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
