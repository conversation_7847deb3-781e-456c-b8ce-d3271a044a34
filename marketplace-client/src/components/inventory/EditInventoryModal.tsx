"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Package,
  Save,
  X,
  AlertTriangle,
  DollarSign,
  Hash,
  MapPin,
} from "lucide-react";
import { useUpdateInventoryMutation, Inventory } from "@/lib/api/inventory";
import { useNotifications } from "@/lib/providers/NotificationProvider";

interface EditInventoryModalProps {
  inventory: Inventory | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export function EditInventoryModal({
  inventory,
  isOpen,
  onClose,
  onSuccess,
}: EditInventoryModalProps) {
  const [formData, setFormData] = useState({
    currentStock: inventory?.currentStock || 0,
    lowStockThreshold: inventory?.lowStockThreshold || 5,
    reorderPoint: inventory?.reorderPoint || 3,
    reorderQuantity: inventory?.reorderQuantity || 20,
    sku: inventory?.sku || "",
    location: inventory?.location || "",
    costPrice: inventory?.costPrice || 0,
    notes: "",
  });

  const [updateInventory, { isLoading }] = useUpdateInventoryMutation();
  const notifications = useNotifications();

  // Update form data when inventory changes
  useState(() => {
    if (inventory) {
      setFormData({
        currentStock: inventory.currentStock,
        lowStockThreshold: inventory.lowStockThreshold,
        reorderPoint: inventory.reorderPoint,
        reorderQuantity: inventory.reorderQuantity,
        sku: inventory.sku || "",
        location: inventory.location || "",
        costPrice: inventory.costPrice || 0,
        notes: "",
      });
    }
  }, [inventory]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!inventory) return;

    try {
      await updateInventory({
        inventoryId: inventory.id,
        newQuantity: formData.currentStock,
        lowStockThreshold: formData.lowStockThreshold,
        reorderPoint: formData.reorderPoint,
        reorderQuantity: formData.reorderQuantity,
        sku: formData.sku,
        location: formData.location,
        costPrice: formData.costPrice,
        reason: formData.notes || "Inventory details updated",
      }).unwrap();

      notifications.success("Inventory updated successfully!");
      onSuccess?.();
      onClose();
    } catch (error: any) {
      notifications.error(error?.data?.message || "Failed to update inventory");
    }
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const getStockStatus = (stock: number, threshold: number) => {
    if (stock === 0)
      return { label: "Out of Stock", color: "bg-red-100 text-red-800" };
    if (stock <= threshold)
      return { label: "Low Stock", color: "bg-yellow-100 text-yellow-800" };
    return { label: "In Stock", color: "bg-green-100 text-green-800" };
  };

  if (!inventory) return null;

  const status = getStockStatus(
    formData.currentStock,
    formData.lowStockThreshold
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-orange-50 rounded-lg">
              <Package className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <span className="text-gray-900">Edit Inventory</span>
              <p className="text-sm text-gray-600 font-normal mt-1">
                {inventory.product.title}
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Product Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">
                  {inventory.product.title}
                </h3>
                <p className="text-sm text-gray-600">
                  {new Intl.NumberFormat("en-KE", {
                    style: "currency",
                    currency: "KES",
                  }).format(inventory.product.price)}
                </p>
              </div>
              <Badge className={status.color}>{status.label}</Badge>
            </div>
          </div>

          {/* Stock Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="currentStock" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Current Stock
              </Label>
              <Input
                id="currentStock"
                type="number"
                min="0"
                value={formData.currentStock}
                onChange={(e) =>
                  handleInputChange(
                    "currentStock",
                    parseInt(e.target.value) || 0
                  )
                }
                className="mt-1"
                required
              />
            </div>

            <div>
              <Label
                htmlFor="lowStockThreshold"
                className="flex items-center gap-2"
              >
                <AlertTriangle className="h-4 w-4" />
                Low Stock Threshold
              </Label>
              <Input
                id="lowStockThreshold"
                type="number"
                min="0"
                value={formData.lowStockThreshold}
                onChange={(e) =>
                  handleInputChange(
                    "lowStockThreshold",
                    parseInt(e.target.value) || 0
                  )
                }
                className="mt-1"
                required
              />
            </div>

            <div>
              <Label htmlFor="reorderPoint">Reorder Point</Label>
              <Input
                id="reorderPoint"
                type="number"
                min="0"
                value={formData.reorderPoint}
                onChange={(e) =>
                  handleInputChange(
                    "reorderPoint",
                    parseInt(e.target.value) || 0
                  )
                }
                className="mt-1"
                required
              />
            </div>

            <div>
              <Label htmlFor="reorderQuantity">Reorder Quantity</Label>
              <Input
                id="reorderQuantity"
                type="number"
                min="1"
                value={formData.reorderQuantity}
                onChange={(e) =>
                  handleInputChange(
                    "reorderQuantity",
                    parseInt(e.target.value) || 1
                  )
                }
                className="mt-1"
                required
              />
            </div>
          </div>

          {/* Additional Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="sku" className="flex items-center gap-2">
                <Hash className="h-4 w-4" />
                SKU
              </Label>
              <Input
                id="sku"
                value={formData.sku}
                onChange={(e) => handleInputChange("sku", e.target.value)}
                className="mt-1"
                placeholder="Product SKU"
              />
            </div>

            <div>
              <Label htmlFor="location" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Location
              </Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange("location", e.target.value)}
                className="mt-1"
                placeholder="Storage location"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="costPrice" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Cost Price (KES)
            </Label>
            <Input
              id="costPrice"
              type="number"
              min="0"
              step="0.01"
              value={formData.costPrice}
              onChange={(e) =>
                handleInputChange("costPrice", parseFloat(e.target.value) || 0)
              }
              className="mt-1"
              placeholder="0.00"
            />
          </div>

          <div>
            <Label htmlFor="notes">Update Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              className="mt-1"
              placeholder="Reason for this update..."
              rows={3}
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-orange-600 hover:bg-orange-700"
            >
              <Save className="mr-2 h-4 w-4" />
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
