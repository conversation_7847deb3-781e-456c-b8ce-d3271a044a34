"use client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Package,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  DollarSign,
  BarChart3,
  PieChart,
  Activity,
} from "lucide-react";
import {
  useGetInventoryStatsQuery,
  useGetLowStockItemsQuery,
} from "@/lib/api/inventory";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface InventoryAnalyticsProps {
  className?: string;
}

export function InventoryAnalytics({ className }: InventoryAnalyticsProps) {
  const {
    data: statsData,
    isLoading: statsLoading,
    error: statsError,
  } = useGetInventoryStatsQuery();

  const { data: lowStockData, isLoading: lowStockLoading } =
    useGetLowStockItemsQuery();

  const stats = statsData?.data;
  const lowStockItems = lowStockData?.data || [];

  if (statsLoading || lowStockLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="marketplace-card">
            <CardContent className="p-6 text-center">
              <LoadingSpinner size="sm" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (statsError || !stats) {
    return (
      <Card className="marketplace-card border-red-200">
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
          <p className="text-sm text-red-600">
            Failed to load inventory analytics
          </p>
        </CardContent>
      </Card>
    );
  }

  const stockHealthPercentage =
    stats.totalProducts > 0
      ? Math.round((stats.inStock / stats.totalProducts) * 100)
      : 0;

  const lowStockPercentage =
    stats.totalProducts > 0
      ? Math.round((stats.lowStock / stats.totalProducts) * 100)
      : 0;

  const outOfStockPercentage =
    stats.totalProducts > 0
      ? Math.round((stats.outOfStock / stats.totalProducts) * 100)
      : 0;

  const averageValuePerProduct =
    stats.totalProducts > 0 ? stats.totalValue / stats.totalProducts : 0;

  const getHealthColor = (percentage: number) => {
    if (percentage >= 80) return "text-green-600";
    if (percentage >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getHealthIcon = (percentage: number) => {
    if (percentage >= 80)
      return <TrendingUp className="h-5 w-5 text-green-500" />;
    if (percentage >= 60)
      return <Activity className="h-5 w-5 text-yellow-500" />;
    return <TrendingDown className="h-5 w-5 text-red-500" />;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        {/* Total Products */}
        <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Products</p>
                <p className="text-2xl sm:text-3xl font-bold text-gray-900">
                  {stats.totalProducts}
                </p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <Package className="h-6 w-6 text-orange-600" />
              </div>
            </div>
            <div className="mt-3 sm:mt-4">
              <p className="text-xs text-gray-500">Active inventory items</p>
            </div>
          </CardContent>
        </Card>

        {/* Stock Health */}
        <Card className="marketplace-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm marketplace-text-muted">Stock Health</p>
                <p
                  className={`text-2xl sm:text-3xl font-bold ${getHealthColor(
                    stockHealthPercentage
                  )}`}
                >
                  {stockHealthPercentage}%
                </p>
              </div>
              {getHealthIcon(stockHealthPercentage)}
            </div>
            <div className="mt-3 sm:mt-4">
              <p className="text-xs marketplace-text-muted">
                Products in good stock
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Total Value */}
        <Card className="marketplace-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm marketplace-text-muted">Total Value</p>
                <p className="text-2xl sm:text-3xl font-bold marketplace-text-primary">
                  {new Intl.NumberFormat("en-KE", {
                    style: "currency",
                    currency: "KES",
                    minimumFractionDigits: 0,
                  }).format(stats.totalValue)}
                </p>
              </div>
              <DollarSign className="h-8 w-8 sm:h-10 sm:w-10 marketplace-text-cool" />
            </div>
            <div className="mt-3 sm:mt-4">
              <p className="text-xs marketplace-text-muted">
                Current inventory value
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Average Value */}
        <Card className="marketplace-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm marketplace-text-muted">Avg. Value</p>
                <p className="text-2xl sm:text-3xl font-bold marketplace-text-primary">
                  {new Intl.NumberFormat("en-KE", {
                    style: "currency",
                    currency: "KES",
                    minimumFractionDigits: 0,
                  }).format(averageValuePerProduct)}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 sm:h-10 sm:w-10 text-purple-500" />
            </div>
            <div className="mt-3 sm:mt-4">
              <p className="text-xs marketplace-text-muted">
                Per product average
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Stock Status Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Stock Status Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Card className="marketplace-card border-green-200">
            <CardContent className="p-4 text-center">
              <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <p className="text-2xl font-bold text-green-600">
                {stats.inStock}
              </p>
              <p className="text-sm text-green-700">In Stock</p>
              <p className="text-xs marketplace-text-muted mt-1">
                {stockHealthPercentage}% of total
              </p>
            </CardContent>
          </Card>

          <Card className="marketplace-card border-yellow-200">
            <CardContent className="p-4 text-center">
              <AlertTriangle className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
              <p className="text-2xl font-bold text-yellow-600">
                {stats.lowStock}
              </p>
              <p className="text-sm text-yellow-700">Low Stock</p>
              <p className="text-xs marketplace-text-muted mt-1">
                {lowStockPercentage}% of total
              </p>
            </CardContent>
          </Card>

          <Card className="marketplace-card border-red-200">
            <CardContent className="p-4 text-center">
              <XCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <p className="text-2xl font-bold text-red-600">
                {stats.outOfStock}
              </p>
              <p className="text-sm text-red-700">Out of Stock</p>
              <p className="text-xs marketplace-text-muted mt-1">
                {outOfStockPercentage}% of total
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Insights Card */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Inventory Insights
            </CardTitle>
            <CardDescription>
              Key metrics and recommendations for your inventory
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {stockHealthPercentage >= 80 && (
                <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-green-800">
                      Excellent Stock Health
                    </p>
                    <p className="text-xs text-green-600">
                      Your inventory levels are well maintained
                    </p>
                  </div>
                </div>
              )}

              {lowStockItems.length > 0 && (
                <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                  <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800">
                      {lowStockItems.length} Item
                      {lowStockItems.length !== 1 ? "s" : ""} Need Attention
                    </p>
                    <p className="text-xs text-yellow-600">
                      Consider restocking soon to avoid stockouts
                    </p>
                  </div>
                </div>
              )}

              {stats.outOfStock > 0 && (
                <div className="flex items-start gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                  <XCircle className="h-5 w-5 text-red-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-red-800">
                      {stats.outOfStock} Product
                      {stats.outOfStock !== 1 ? "s" : ""} Out of Stock
                    </p>
                    <p className="text-xs text-red-600">
                      Restock immediately to avoid lost sales
                    </p>
                  </div>
                </div>
              )}

              {stats.totalProducts === 0 && (
                <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <Package className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-blue-800">
                      Get Started
                    </p>
                    <p className="text-xs text-blue-600">
                      Add products to start tracking your inventory
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
