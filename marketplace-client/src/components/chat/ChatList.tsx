"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  MessageCircle,
  Search,
  Plus,
  Clock,
  CheckCheck,
  Check,
} from "lucide-react";
import { useGetConversationsQuery, Conversation } from "@/lib/api/chat";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";

interface ChatListProps {
  selectedConversationId?: string;
  onConversationSelect: (conversation: Conversation) => void;
  onNewChat: () => void;
  currentUserId: string;
}

export function ChatList({
  selectedConversationId,
  onConversationSelect,
  onNewChat,
  currentUserId,
}: ChatListProps) {
  const [searchTerm, setSearchTerm] = useState("");

  const {
    data: conversations = [],
    isLoading,
    error,
    refetch,
  } = useGetConversationsQuery();

  const filteredConversations = conversations.filter((conversation) => {
    const otherParticipant =
      conversation.participantOne.id === currentUserId
        ? conversation.participantTwo
        : conversation.participantOne;

    return (
      otherParticipant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      conversation.lastMessagePreview
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase())
    );
  });

  const getOtherParticipant = (conversation: Conversation) => {
    return conversation.participantOne.id === currentUserId
      ? conversation.participantTwo
      : conversation.participantOne;
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "seller":
        return "bg-orange-100 text-orange-800";
      case "buyer":
        return "bg-blue-100 text-blue-800";
      case "admin":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Messages
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3 animate-pulse">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Messages
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">Failed to load conversations</p>
            <Button onClick={() => refetch()} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Messages
          </CardTitle>
          <Button size="sm" onClick={onNewChat} className="bg-orange-600 hover:bg-orange-700">
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search conversations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden p-0">
        <div className="h-full overflow-y-auto">
          {filteredConversations.length === 0 ? (
            <div className="text-center py-8 px-4">
              <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">
                {searchTerm ? "No conversations found" : "No conversations yet"}
              </p>
              {!searchTerm && (
                <Button onClick={onNewChat} variant="outline">
                  Start a conversation
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-1 p-2">
              {filteredConversations.map((conversation) => {
                const otherParticipant = getOtherParticipant(conversation);
                const isSelected = conversation.id === selectedConversationId;

                return (
                  <div
                    key={conversation.id}
                    onClick={() => onConversationSelect(conversation)}
                    className={cn(
                      "flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors",
                      isSelected
                        ? "bg-orange-50 border border-orange-200"
                        : "hover:bg-gray-50"
                    )}
                  >
                    <div className="relative">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src="" alt={otherParticipant.name} />
                        <AvatarFallback className="bg-gradient-to-br from-orange-400 to-blue-500 text-white">
                          {getInitials(otherParticipant.name)}
                        </AvatarFallback>
                      </Avatar>
                      {/* Online status indicator - placeholder for future */}
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium text-gray-900 truncate">
                            {otherParticipant.name}
                          </h3>
                          <Badge
                            variant="secondary"
                            className={cn("text-xs", getRoleColor(otherParticipant.role))}
                          >
                            {otherParticipant.role}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <Clock className="h-3 w-3" />
                          {formatDistanceToNow(new Date(conversation.updatedAt), {
                            addSuffix: true,
                          })}
                        </div>
                      </div>

                      <div className="flex items-center justify-between mt-1">
                        <p className="text-sm text-gray-600 truncate">
                          {conversation.lastMessagePreview || "No messages yet"}
                        </p>
                        
                        {/* Message status indicators - placeholder for future */}
                        <div className="flex items-center gap-1">
                          {/* Unread count badge */}
                          <Badge variant="destructive" className="h-5 w-5 p-0 text-xs">
                            2
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
