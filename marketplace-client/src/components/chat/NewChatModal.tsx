"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Search, MessageCircle, Loader2 } from "lucide-react";
import { useGetUsersQuery } from "@/lib/api/users";
import { useStartConversationMutation } from "@/lib/api/chat";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

interface NewChatModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentUserId: string;
  onConversationCreated: (conversationId: string) => void;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: "buyer" | "seller" | "admin";
}

export function NewChatModal({
  open,
  onOpenChange,
  currentUserId,
  onConversationCreated,
}: NewChatModalProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const {
    data: users = [],
    isLoading: usersLoading,
    error: usersError,
  } = useGetUsersQuery(
    { search: searchTerm },
    { skip: !open } // Only fetch when modal is open
  );

  const [startConversation, { isLoading: isCreating }] = useStartConversationMutation();

  // Filter out current user and apply search
  const filteredUsers = users.filter((user: User) => {
    if (user.id === currentUserId) return false;
    
    if (!searchTerm) return true;
    
    return (
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const handleStartConversation = async (user: User) => {
    try {
      setSelectedUser(user);
      
      const conversation = await startConversation({
        participantId: user.id,
      }).unwrap();

      toast.success(`Started conversation with ${user.name}`);
      onConversationCreated(conversation.id);
      onOpenChange(false);
      
      // Reset state
      setSearchTerm("");
      setSelectedUser(null);
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to start conversation");
      setSelectedUser(null);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "seller":
        return "bg-orange-100 text-orange-800";
      case "buyer":
        return "bg-blue-100 text-blue-800";
      case "admin":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "seller":
        return "🏪";
      case "buyer":
        return "🛒";
      case "admin":
        return "👑";
      default:
        return "👤";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Start New Conversation
          </DialogTitle>
          <DialogDescription>
            Search for users to start a conversation with.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Users List */}
          <div className="max-h-80 overflow-y-auto space-y-2">
            {usersLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : usersError ? (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">Failed to load users</p>
                <Button variant="outline" size="sm">
                  Try Again
                </Button>
              </div>
            ) : filteredUsers.length === 0 ? (
              <div className="text-center py-8">
                <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  {searchTerm ? "No users found" : "No users available"}
                </p>
              </div>
            ) : (
              filteredUsers.map((user: User) => (
                <div
                  key={user.id}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg border transition-colors",
                    selectedUser?.id === user.id
                      ? "bg-orange-50 border-orange-200"
                      : "hover:bg-gray-50 cursor-pointer"
                  )}
                  onClick={() => !isCreating && handleStartConversation(user)}
                >
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src="" alt={user.name} />
                      <AvatarFallback className="bg-gradient-to-br from-orange-400 to-blue-500 text-white">
                        {getInitials(user.name)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium text-gray-900 truncate">
                          {user.name}
                        </h3>
                        <Badge
                          variant="secondary"
                          className={cn("text-xs", getRoleColor(user.role))}
                        >
                          {getRoleIcon(user.role)} {user.role}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500 truncate">
                        {user.email}
                      </p>
                    </div>
                  </div>

                  {isCreating && selectedUser?.id === user.id ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Button size="sm" variant="ghost">
                      <MessageCircle className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))
            )}
          </div>

          {/* Quick Actions */}
          {!searchTerm && filteredUsers.length > 0 && (
            <div className="border-t pt-4">
              <p className="text-sm text-gray-600 mb-3">Quick suggestions:</p>
              <div className="grid grid-cols-1 gap-2">
                {filteredUsers.slice(0, 2).map((user: User) => (
                  <Button
                    key={user.id}
                    variant="outline"
                    size="sm"
                    className="justify-start"
                    onClick={() => handleStartConversation(user)}
                    disabled={isCreating}
                  >
                    <Avatar className="h-6 w-6 mr-2">
                      <AvatarImage src="" alt={user.name} />
                      <AvatarFallback className="bg-gradient-to-br from-orange-400 to-blue-500 text-white text-xs">
                        {getInitials(user.name)}
                      </AvatarFallback>
                    </Avatar>
                    Chat with {user.name}
                    <Badge
                      variant="secondary"
                      className={cn("ml-auto text-xs", getRoleColor(user.role))}
                    >
                      {user.role}
                    </Badge>
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
