"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MessageCircle,
  Send,
  MoreVertical,
  Trash2,
  Copy,
  Phone,
  Video,
  Info,
  Paperclip,
  Smile,
  Check,
  CheckCheck,
} from "lucide-react";
import {
  useGetMessagesQuery,
  useSendMessageMutation,
  useDeleteMessageForUserMutation,
  useDeleteMessageForEveryoneMutation,
  useMarkMessageAsReadMutation,
  Conversation,
  Message,
} from "@/lib/api/chat";
import { formatDistanceToNow, format, isToday, isYesterday } from "date-fns";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface ChatWindowProps {
  conversation: Conversation;
  currentUserId: string;
}

export function ChatWindow({ conversation, currentUserId }: ChatWindowProps) {
  const [messageText, setMessageText] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const {
    data: messages = [],
    isLoading,
    error,
    refetch,
  } = useGetMessagesQuery(conversation.id, {
    pollingInterval: 3000, // Poll every 3 seconds for new messages
  });

  const [sendMessage, { isLoading: isSending }] = useSendMessageMutation();
  const [deleteMessageForUser] = useDeleteMessageForUserMutation();
  const [deleteMessageForEveryone] = useDeleteMessageForEveryoneMutation();
  const [markAsRead] = useMarkMessageAsReadMutation();

  const otherParticipant =
    conversation.participantOne.id === currentUserId
      ? conversation.participantTwo
      : conversation.participantOne;

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Focus input when conversation changes
  useEffect(() => {
    inputRef.current?.focus();
  }, [conversation.id]);

  // Mark messages as read when they come into view
  useEffect(() => {
    const unreadMessages = messages.filter(
      (msg) => !msg.isRead && msg.sender.id !== currentUserId
    );
    
    unreadMessages.forEach((msg) => {
      markAsRead(msg.id);
    });
  }, [messages, currentUserId, markAsRead]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!messageText.trim() || isSending) return;

    try {
      await sendMessage({
        conversationId: conversation.id,
        content: messageText.trim(),
      }).unwrap();
      
      setMessageText("");
    } catch (error) {
      toast.error("Failed to send message");
    }
  };

  const handleDeleteMessage = async (messageId: string, forEveryone = false) => {
    try {
      if (forEveryone) {
        await deleteMessageForEveryone(messageId).unwrap();
        toast.success("Message deleted for everyone");
      } else {
        await deleteMessageForUser(messageId).unwrap();
        toast.success("Message deleted for you");
      }
    } catch (error) {
      toast.error("Failed to delete message");
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const formatMessageTime = (dateString: string) => {
    const date = new Date(dateString);
    
    if (isToday(date)) {
      return format(date, "HH:mm");
    } else if (isYesterday(date)) {
      return `Yesterday ${format(date, "HH:mm")}`;
    } else {
      return format(date, "MMM d, HH:mm");
    }
  };

  const groupMessagesByDate = (messages: Message[]) => {
    const groups: { [key: string]: Message[] } = {};
    
    messages.forEach((message) => {
      const date = new Date(message.createdAt);
      let dateKey: string;
      
      if (isToday(date)) {
        dateKey = "Today";
      } else if (isYesterday(date)) {
        dateKey = "Yesterday";
      } else {
        dateKey = format(date, "MMMM d, yyyy");
      }
      
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(message);
    });
    
    return groups;
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "seller":
        return "bg-orange-100 text-orange-800";
      case "buyer":
        return "bg-blue-100 text-blue-800";
      case "admin":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return (
      <Card className="h-full flex flex-col">
        <CardHeader className="border-b">
          <div className="flex items-center space-x-3 animate-pulse">
            <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              <div className="h-3 bg-gray-200 rounded w-1/4"></div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <MessageCircle className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="h-full flex flex-col">
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">Failed to load messages</p>
            <Button onClick={() => refetch()} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const messageGroups = groupMessagesByDate(messages);

  return (
    <Card className="h-full flex flex-col">
      {/* Chat Header */}
      <CardHeader className="border-b bg-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Avatar className="h-10 w-10">
                <AvatarImage src="" alt={otherParticipant.name} />
                <AvatarFallback className="bg-gradient-to-br from-orange-400 to-blue-500 text-white">
                  {getInitials(otherParticipant.name)}
                </AvatarFallback>
              </Avatar>
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
            </div>
            
            <div>
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-gray-900">
                  {otherParticipant.name}
                </h3>
                <Badge
                  variant="secondary"
                  className={cn("text-xs", getRoleColor(otherParticipant.role))}
                >
                  {otherParticipant.role}
                </Badge>
              </div>
              <p className="text-sm text-green-600">Online</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button size="sm" variant="ghost">
              <Phone className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="ghost">
              <Video className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="ghost">
              <Info className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      {/* Messages Area */}
      <CardContent className="flex-1 overflow-hidden p-0">
        <div className="h-full overflow-y-auto p-4 space-y-4">
          {Object.entries(messageGroups).map(([dateKey, dateMessages]) => (
            <div key={dateKey}>
              {/* Date separator */}
              <div className="flex items-center justify-center my-4">
                <div className="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
                  {dateKey}
                </div>
              </div>

              {/* Messages for this date */}
              {dateMessages.map((message, index) => {
                const isOwnMessage = message.sender.id === currentUserId;
                const showAvatar = 
                  index === 0 || 
                  dateMessages[index - 1].sender.id !== message.sender.id;

                return (
                  <div
                    key={message.id}
                    className={cn(
                      "flex items-end space-x-2",
                      isOwnMessage ? "justify-end" : "justify-start"
                    )}
                  >
                    {!isOwnMessage && (
                      <Avatar className={cn("h-6 w-6", !showAvatar && "invisible")}>
                        <AvatarImage src="" alt={message.sender.name} />
                        <AvatarFallback className="bg-gradient-to-br from-orange-400 to-blue-500 text-white text-xs">
                          {getInitials(message.sender.name)}
                        </AvatarFallback>
                      </Avatar>
                    )}

                    <div
                      className={cn(
                        "max-w-xs lg:max-w-md px-4 py-2 rounded-lg relative group",
                        isOwnMessage
                          ? "bg-orange-600 text-white"
                          : "bg-gray-100 text-gray-900"
                      )}
                    >
                      <p className="text-sm">{message.content}</p>
                      
                      <div className={cn(
                        "flex items-center justify-between mt-1 text-xs",
                        isOwnMessage ? "text-orange-100" : "text-gray-500"
                      )}>
                        <span>{formatMessageTime(message.createdAt)}</span>
                        
                        {isOwnMessage && (
                          <div className="flex items-center ml-2">
                            {message.isRead ? (
                              <CheckCheck className="h-3 w-3" />
                            ) : (
                              <Check className="h-3 w-3" />
                            )}
                          </div>
                        )}
                      </div>

                      {/* Message actions */}
                      <div className="absolute -top-8 right-0 opacity-0 group-hover:opacity-100 transition-opacity">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                              <MoreVertical className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => navigator.clipboard.writeText(message.content)}>
                              <Copy className="h-4 w-4 mr-2" />
                              Copy
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDeleteMessage(message.id)}>
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete for me
                            </DropdownMenuItem>
                            {isOwnMessage && (
                              <DropdownMenuItem 
                                onClick={() => handleDeleteMessage(message.id, true)}
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete for everyone
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>

                    {isOwnMessage && (
                      <Avatar className={cn("h-6 w-6", !showAvatar && "invisible")}>
                        <AvatarImage src="" alt={message.sender.name} />
                        <AvatarFallback className="bg-gradient-to-br from-orange-400 to-blue-500 text-white text-xs">
                          {getInitials(message.sender.name)}
                        </AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                );
              })}
            </div>
          ))}
          
          {/* Typing indicator */}
          {isTyping && (
            <div className="flex items-center space-x-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src="" alt={otherParticipant.name} />
                <AvatarFallback className="bg-gradient-to-br from-orange-400 to-blue-500 text-white text-xs">
                  {getInitials(otherParticipant.name)}
                </AvatarFallback>
              </Avatar>
              <div className="bg-gray-100 px-4 py-2 rounded-lg">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
      </CardContent>

      {/* Message Input */}
      <div className="border-t bg-white p-4">
        <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
          <Button type="button" size="sm" variant="ghost">
            <Paperclip className="h-4 w-4" />
          </Button>
          
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
              placeholder="Type a message..."
              className="pr-10"
              disabled={isSending}
            />
            <Button
              type="button"
              size="sm"
              variant="ghost"
              className="absolute right-1 top-1/2 transform -translate-y-1/2"
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>
          
          <Button
            type="submit"
            size="sm"
            disabled={!messageText.trim() || isSending}
            className="bg-orange-600 hover:bg-orange-700"
          >
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </div>
    </Card>
  );
}
