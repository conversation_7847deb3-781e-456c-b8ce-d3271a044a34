"use client";

import React, { useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ImagePreview } from "@/components/ui/image-preview";
import { cn } from "@/lib/utils";
import { Upload, X, Image as ImageIcon, AlertCircle } from "lucide-react";

interface ImageUploadProps {
  value?: File[];
  onChange: (files: File[]) => void;
  maxFiles?: number;
  maxSize?: number; // in MB
  accept?: string[];
  className?: string;
  disabled?: boolean;
}

export function ImageUpload({
  value = [],
  onChange,
  maxFiles = 5,
  maxSize = 5,
  accept = ["image/jpeg", "image/png", "image/webp"],
  className,
  disabled = false,
}: ImageUploadProps) {
  const [errors, setErrors] = useState<string[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateAndProcessFiles = (files: File[]) => {
    setErrors([]);
    const newErrors: string[] = [];

    // Validate file types
    const validFiles = files.filter((file) => {
      if (!accept.includes(file.type)) {
        newErrors.push(`${file.name} is not a supported image format.`);
        return false;
      }
      if (file.size > maxSize * 1024 * 1024) {
        newErrors.push(`${file.name} is too large. Max size is ${maxSize}MB.`);
        return false;
      }
      return true;
    });

    // Check total file count
    const totalFiles = value.length + validFiles.length;
    if (totalFiles > maxFiles) {
      newErrors.push(`Maximum ${maxFiles} images allowed.`);
      const allowedCount = maxFiles - value.length;
      validFiles.splice(allowedCount);
    }

    if (newErrors.length > 0) {
      setErrors(newErrors);
    }

    if (validFiles.length > 0) {
      onChange([...value, ...validFiles]);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    validateAndProcessFiles(files);

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    // Only set drag over to false if we're leaving the drop zone entirely
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX;
    const y = event.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setIsDragOver(false);
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragOver(false);

    if (disabled) return;

    const files = Array.from(event.dataTransfer.files);
    validateAndProcessFiles(files);
  };

  const removeFile = (index: number) => {
    const newFiles = value.filter((_, i) => i !== index);
    onChange(newFiles);
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Area */}
      <Card
        className={cn(
          "border-2 border-dashed transition-colors cursor-pointer",
          isDragOver
            ? "border-blue-500 bg-blue-50"
            : "border-gray-300 hover:border-gray-400",
          disabled && "opacity-50 cursor-not-allowed"
        )}
        onClick={() => !disabled && fileInputRef.current?.click()}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <CardContent className="flex flex-col items-center justify-center p-6 text-center">
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={accept.join(",")}
            onChange={handleFileSelect}
            className="hidden"
            disabled={disabled}
          />
          <Upload
            className={cn(
              "h-10 w-10 mb-4",
              isDragOver ? "text-blue-500" : "text-gray-400"
            )}
          />
          <div className="space-y-2">
            <p
              className={cn(
                "text-sm font-medium",
                isDragOver ? "text-blue-700" : "text-gray-700"
              )}
            >
              {isDragOver
                ? "Drop images here"
                : "Click to select images or drag and drop"}
            </p>
            <p className="text-xs text-gray-500">
              Supports: {accept.map((type) => type.split("/")[1]).join(", ")} •
              Max {maxSize}MB each • Up to {maxFiles} images
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="space-y-2">
          {errors.map((error, index) => (
            <div
              key={index}
              className="flex items-center space-x-2 text-sm text-red-600"
            >
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          ))}
        </div>
      )}

      {/* Image Previews */}
      {value.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">
              Selected Images ({value.length})
            </h4>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => onChange([])}
              className="text-xs"
            >
              Clear All
            </Button>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {value.map((file, index) => (
              <div key={index} className="relative group">
                <div className="relative border-2 border-gray-200 hover:border-gray-300 transition-colors rounded-lg overflow-hidden">
                  <ImagePreview
                    file={file}
                    alt={`Preview ${index + 1}`}
                    className="w-full"
                    aspectRatio="square"
                  />

                  {/* Remove button overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFile(index);
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Image index indicator */}
                  <div className="absolute top-2 left-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded z-10">
                    {index + 1}
                  </div>
                </div>

                {/* File name */}
                <p
                  className="text-xs text-gray-500 mt-1 truncate text-center"
                  title={file.name}
                >
                  {file.name}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload Status */}
      {value.length > 0 && (
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>
            {value.length} of {maxFiles} images selected
          </span>
          {value.length === maxFiles && (
            <span className="text-amber-600">Maximum images reached</span>
          )}
        </div>
      )}
    </div>
  );
}
