"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, Tooltip<PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  Edit,
  Eye,
  Trash2,
  <PERSON><PERSON>hart3,
  <PERSON>ert<PERSON>riangle,
  CheckCircle,
} from "lucide-react";
import Link from "next/link";

interface Product {
  id: string;
  title: string;
  quantity: number;
  price: number;
}

interface ProductQuickActionsProps {
  product: Product;
  onDelete: (product: { id: string; title: string }) => void;
  showAnalytics?: boolean;
  compact?: boolean;
}

export function ProductQuickActions({
  product,
  onDelete,
  showAnalytics = true,
  compact = false,
}: ProductQuickActionsProps) {
  const isOutOfStock = product.quantity === 0;
  const isLowStock = product.quantity > 0 && product.quantity <= 5;

  const handleDelete = () => {
    onDelete({ id: product.id, title: product.title });
  };

  if (compact) {
    return (
      <TooltipProvider>
        <div className="flex items-center space-x-1">
          {/* Stock Status Indicator */}
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center">
                {isOutOfStock ? (
                  <AlertTriangle className="h-3 w-3 text-red-500" />
                ) : isLowStock ? (
                  <AlertTriangle className="h-3 w-3 text-yellow-500" />
                ) : (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                )}
              </div>
            </TooltipTrigger>
            <TooltipContent>
              {isOutOfStock
                ? "Out of stock"
                : isLowStock
                ? `Low stock: ${product.quantity} remaining`
                : `In stock: ${product.quantity} available`}
            </TooltipContent>
          </Tooltip>

          {/* Quick Actions */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button size="sm" variant="ghost" className="h-6 w-6 p-0" asChild>
                <Link href={`/products/${product.id}`}>
                  <Eye className="h-3 w-3" />
                </Link>
              </Button>
            </TooltipTrigger>
            <TooltipContent>View product</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button size="sm" variant="ghost" className="h-6 w-6 p-0" asChild>
                <Link href={`/seller/products/${product.id}/edit`}>
                  <Edit className="h-3 w-3" />
                </Link>
              </Button>
            </TooltipTrigger>
            <TooltipContent>Edit product</TooltipContent>
          </Tooltip>

          {showAnalytics && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button size="sm" variant="ghost" className="h-6 w-6 p-0" asChild>
                  <Link href={`/seller/analytics/products/${product.id}`}>
                    <BarChart3 className="h-3 w-3" />
                  </Link>
                </Button>
              </TooltipTrigger>
              <TooltipContent>View analytics</TooltipContent>
            </Tooltip>
          )}

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                onClick={handleDelete}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Delete product</TooltipContent>
          </Tooltip>
        </div>
      </TooltipProvider>
    );
  }

  return (
    <div className="space-y-2">
      {/* Stock Status */}
      <div className="flex items-center space-x-2">
        <Badge
          variant={isOutOfStock ? "destructive" : isLowStock ? "secondary" : "default"}
          className="text-xs"
        >
          {isOutOfStock
            ? "Out of Stock"
            : isLowStock
            ? `Low Stock (${product.quantity})`
            : `In Stock (${product.quantity})`}
        </Badge>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center space-x-2">
        <Button size="sm" variant="outline" asChild>
          <Link href={`/products/${product.id}`}>
            <Eye className="h-3 w-3 mr-1" />
            View
          </Link>
        </Button>

        <Button size="sm" variant="outline" asChild>
          <Link href={`/seller/products/${product.id}/edit`}>
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Link>
        </Button>

        {showAnalytics && (
          <Button size="sm" variant="outline" asChild>
            <Link href={`/seller/analytics/products/${product.id}`}>
              <BarChart3 className="h-3 w-3 mr-1" />
              Analytics
            </Link>
          </Button>
        )}

        <Button
          size="sm"
          variant="destructive"
          onClick={handleDelete}
        >
          <Trash2 className="h-3 w-3 mr-1" />
          Delete
        </Button>
      </div>
    </div>
  );
}
