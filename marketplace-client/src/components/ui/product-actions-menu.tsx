"use client";

import { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  MoreVertical,
  Edit,
  Eye,
  Trash2,
  Copy,
  Share,
  BarChart3,
  Package,
  Loader2,
} from "lucide-react";
import Link from "next/link";

interface Product {
  id: string;
  title: string;
  quantity: number;
  price: number;
}

interface ProductActionsMenuProps {
  product: Product;
  onDelete: (product: { id: string; title: string }) => void;
  onDuplicate?: (productId: string) => void;
  onShare?: (productId: string) => void;
  isLoading?: boolean;
  disabled?: boolean;
}

export function ProductActionsMenu({
  product,
  onDelete,
  onDuplicate,
  onShare,
  isLoading = false,
  disabled = false,
}: ProductActionsMenuProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleDelete = () => {
    onDelete({ id: product.id, title: product.title });
    setIsOpen(false);
  };

  const handleDuplicate = () => {
    if (onDuplicate) {
      onDuplicate(product.id);
    }
    setIsOpen(false);
  };

  const handleShare = () => {
    if (onShare) {
      onShare(product.id);
    }
    setIsOpen(false);
  };

  const isOutOfStock = product.quantity === 0;

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          disabled={disabled || isLoading}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <MoreVertical className="h-4 w-4" />
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {/* Quick Info */}
        <div className="px-2 py-1.5 text-xs text-gray-500 border-b">
          <div className="flex items-center justify-between">
            <span>ID: {product.id.slice(0, 8)}...</span>
            <Badge variant={isOutOfStock ? "destructive" : "default"} className="text-xs">
              {isOutOfStock ? "Out of Stock" : "In Stock"}
            </Badge>
          </div>
        </div>

        {/* View Actions */}
        <DropdownMenuItem asChild>
          <Link href={`/products/${product.id}`} className="flex items-center">
            <Eye className="mr-2 h-4 w-4" />
            View Product
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link href={`/seller/products/${product.id}/edit`} className="flex items-center">
            <Edit className="mr-2 h-4 w-4" />
            Edit Product
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Management Actions */}
        <DropdownMenuItem asChild>
          <Link href={`/seller/analytics/products/${product.id}`} className="flex items-center">
            <BarChart3 className="mr-2 h-4 w-4" />
            View Analytics
          </Link>
        </DropdownMenuItem>

        {onDuplicate && (
          <DropdownMenuItem onClick={handleDuplicate} className="flex items-center">
            <Copy className="mr-2 h-4 w-4" />
            Duplicate Product
          </DropdownMenuItem>
        )}

        {onShare && (
          <DropdownMenuItem onClick={handleShare} className="flex items-center">
            <Share className="mr-2 h-4 w-4" />
            Share Product
          </DropdownMenuItem>
        )}

        <DropdownMenuSeparator />

        {/* Inventory Actions */}
        <DropdownMenuItem asChild>
          <Link href={`/seller/inventory/${product.id}`} className="flex items-center">
            <Package className="mr-2 h-4 w-4" />
            Manage Inventory
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Destructive Actions */}
        <DropdownMenuItem
          onClick={handleDelete}
          className="flex items-center text-red-600 focus:text-red-600"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete Product
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
