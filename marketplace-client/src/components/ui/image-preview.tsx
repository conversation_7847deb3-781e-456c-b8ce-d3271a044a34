"use client";

import React from 'react';
import { ImageIcon, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useImagePreview } from '@/lib/hooks/useImagePreview';

interface ImagePreviewProps {
  file: File;
  alt?: string;
  className?: string;
  aspectRatio?: 'square' | 'auto';
  showFileName?: boolean;
  onLoad?: () => void;
  onError?: (error: string) => void;
}

export function ImagePreview({
  file,
  alt = 'Image preview',
  className,
  aspectRatio = 'square',
  showFileName = false,
  onLoad,
  onError,
}: ImagePreviewProps) {
  const { previewUrl, isLoading, error } = useImagePreview(file);

  React.useEffect(() => {
    if (previewUrl && onLoad) {
      onLoad();
    }
  }, [previewUrl, onLoad]);

  React.useEffect(() => {
    if (error && onError) {
      onError(error);
    }
  }, [error, onError]);

  if (error) {
    return (
      <div className={cn(
        'flex items-center justify-center bg-red-50 border border-red-200 rounded-lg',
        aspectRatio === 'square' ? 'aspect-square' : '',
        className
      )}>
        <div className="text-center p-4">
          <AlertCircle className="h-8 w-8 text-red-400 mx-auto mb-2" />
          <p className="text-xs text-red-600">Failed to load image</p>
          {showFileName && (
            <p className="text-xs text-gray-500 mt-1 truncate" title={file.name}>
              {file.name}
            </p>
          )}
        </div>
      </div>
    );
  }

  if (isLoading || !previewUrl) {
    return (
      <div className={cn(
        'flex items-center justify-center bg-gray-100 border border-gray-200 rounded-lg',
        aspectRatio === 'square' ? 'aspect-square' : '',
        className
      )}>
        <div className="text-center p-4">
          <ImageIcon className="h-8 w-8 text-gray-400 mx-auto mb-2 animate-pulse" />
          <p className="text-xs text-gray-500">Loading...</p>
          {showFileName && (
            <p className="text-xs text-gray-500 mt-1 truncate" title={file.name}>
              {file.name}
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      'relative overflow-hidden rounded-lg',
      aspectRatio === 'square' ? 'aspect-square' : '',
      className
    )}>
      <img
        src={previewUrl}
        alt={alt}
        className="w-full h-full object-cover"
        onLoad={(e) => {
          e.currentTarget.style.opacity = '1';
        }}
        style={{ opacity: 0, transition: 'opacity 0.3s ease-in-out' }}
      />
      {showFileName && (
        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white p-2">
          <p className="text-xs truncate" title={file.name}>
            {file.name}
          </p>
        </div>
      )}
    </div>
  );
}
