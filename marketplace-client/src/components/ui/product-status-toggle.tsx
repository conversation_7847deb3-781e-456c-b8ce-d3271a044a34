"use client";

import { useState } from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Loader2 } from "lucide-react";

interface ProductStatusToggleProps {
  productId: string;
  productTitle: string;
  isActive: boolean;
  onToggle: (productId: string, isActive: boolean) => Promise<void>;
  disabled?: boolean;
  size?: "sm" | "default";
}

export function ProductStatusToggle({
  productId,
  productTitle,
  isActive,
  onToggle,
  disabled = false,
  size = "default",
}: ProductStatusToggleProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [currentStatus, setCurrentStatus] = useState(isActive);

  const handleToggle = async (checked: boolean) => {
    setIsLoading(true);
    try {
      await onToggle(productId, checked);
      setCurrentStatus(checked);
    } catch (error) {
      // Revert the toggle on error
      setCurrentStatus(!checked);
    } finally {
      setIsLoading(false);
    }
  };

  if (size === "sm") {
    return (
      <div className="flex items-center space-x-2">
        {isLoading ? (
          <Loader2 className="h-3 w-3 animate-spin" />
        ) : (
          <Switch
            id={`status-${productId}`}
            checked={currentStatus}
            onCheckedChange={handleToggle}
            disabled={disabled || isLoading}
            className="scale-75"
          />
        )}
        <Badge variant={currentStatus ? "default" : "secondary"} className="text-xs">
          {currentStatus ? "Active" : "Inactive"}
        </Badge>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-3">
      <div className="flex items-center space-x-2">
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Switch
            id={`status-${productId}`}
            checked={currentStatus}
            onCheckedChange={handleToggle}
            disabled={disabled || isLoading}
          />
        )}
        <Label htmlFor={`status-${productId}`} className="text-sm font-medium">
          {currentStatus ? "Active" : "Inactive"}
        </Label>
      </div>
      <Badge variant={currentStatus ? "default" : "secondary"}>
        {currentStatus ? "Available" : "Unavailable"}
      </Badge>
    </div>
  );
}
