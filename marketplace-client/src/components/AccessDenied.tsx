"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Shield, ArrowLeft, Home, LogIn, AlertTriangle } from "lucide-react";

interface AccessDeniedProps {
  title?: string;
  description?: string;
  redirectPath?: string;
  redirectLabel?: string;
  showLoginButton?: boolean;
  countdown?: number;
}

export default function AccessDenied({
  title = "Access Denied",
  description = "You don't have permission to access this page.",
  redirectPath = "/",
  redirectLabel = "Go Home",
  showLoginButton = true,
  countdown = 5,
}: AccessDeniedProps) {
  const router = useRouter();
  const [timeLeft, setTimeLeft] = useState(countdown);

  useEffect(() => {
    if (countdown > 0) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            router.push(redirectPath);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [router, redirectPath, countdown]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        <Card className="border-red-200 shadow-lg">
          <CardHeader className="text-center space-y-4">
            <div className="flex justify-center">
              <div className="p-4 bg-red-100 rounded-full">
                <Shield className="h-12 w-12 text-red-600" />
              </div>
            </div>
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center justify-center gap-2">
                <AlertTriangle className="h-6 w-6 text-red-600" />
                {title}
              </CardTitle>
              <CardDescription className="text-lg mt-2 text-gray-600">
                {description}
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center justify-center space-x-2 text-red-800">
                <Shield className="h-5 w-5" />
                <span className="font-mono text-lg">403</span>
              </div>
              <p className="text-sm text-red-600 mt-1 text-center">
                Forbidden - Insufficient Permissions
              </p>
            </div>

            {countdown > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-800 text-sm text-center">
                  Redirecting in{" "}
                  <span className="font-bold text-lg">{timeLeft}</span> seconds...
                </p>
              </div>
            )}

            <div className="space-y-3">
              <Button
                onClick={() => router.push(redirectPath)}
                className="w-full bg-red-600 hover:bg-red-700"
              >
                <Home className="mr-2 h-4 w-4" />
                {redirectLabel}
              </Button>

              {showLoginButton && (
                <Button
                  onClick={() => router.push("/auth/login")}
                  variant="outline"
                  className="w-full border-red-200 text-red-700 hover:bg-red-50"
                >
                  <LogIn className="mr-2 h-4 w-4" />
                  Sign In
                </Button>
              )}

              <Button
                onClick={() => router.back()}
                variant="ghost"
                className="w-full text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-50 border-gray-200">
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <p className="text-sm text-gray-600">
                Need help? Contact your administrator.
              </p>
              <div className="flex justify-center space-x-4 text-xs text-gray-500">
                <span>Error Code: 403</span>
                <span>•</span>
                <span>Access Denied</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
