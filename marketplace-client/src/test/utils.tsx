import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { apiSlice } from '@/lib/api/apiSlice'
import authReducer from '@/lib/features/auth/authSlice'
import notificationReducer from '@/lib/features/notifications/notificationSlice'
import { AuthProvider } from '@/lib/contexts/AuthContext'
import type { User, UserRole } from '@/lib/features/auth/authSlice'

// Create a test store
export function createTestStore(preloadedState?: any) {
  return configureStore({
    reducer: {
      [apiSlice.reducerPath]: apiSlice.reducer,
      auth: authReducer,
      notifications: notificationReducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(apiSlice.middleware),
    preloadedState,
  })
}

// Create mock user
export function createMockUser(overrides: Partial<User> = {}): User {
  return {
    id: '1',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'buyer' as UserRole,
    ...overrides,
  }
}

// Create authenticated state
export function createAuthenticatedState(user: Partial<User> = {}) {
  const mockUser = createMockUser(user)
  return {
    auth: {
      user: mockUser,
      token: 'mock-token',
      refreshToken: 'mock-refresh-token',
      isAuthenticated: true,
      isLoading: false,
    },
    notifications: {
      notifications: [],
    },
  }
}

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  preloadedState?: any
  store?: ReturnType<typeof createTestStore>
}

export function renderWithProviders(
  ui: ReactElement,
  {
    preloadedState = {},
    store = createTestStore(preloadedState),
    ...renderOptions
  }: CustomRenderOptions = {}
) {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <Provider store={store}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </Provider>
    )
  }

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) }
}

// Mock API responses
export const mockApiResponses = {
  login: {
    user: createMockUser(),
    token: 'mock-jwt-token',
    refreshToken: 'mock-refresh-token',
  },
  products: [
    {
      id: '1',
      name: 'Test Product',
      description: 'A test product',
      price: 99.99,
      category: 'Electronics',
      sellerId: '1',
    },
  ],
}

// Test helpers
export const testHelpers = {
  // Wait for async operations
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Mock console methods
  mockConsole: () => {
    const originalConsole = { ...console }
    console.error = vi.fn()
    console.warn = vi.fn()
    console.log = vi.fn()
    
    return () => {
      Object.assign(console, originalConsole)
    }
  },
}

// Re-export everything from testing-library
export * from '@testing-library/react'
export { default as userEvent } from '@testing-library/user-event'
