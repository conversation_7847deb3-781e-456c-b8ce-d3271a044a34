import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { StoreProvider } from "@/lib/providers/StoreProvider";
import { AuthProvider } from "@/lib/contexts/AuthContext";
import { CartProvider } from "@/lib/contexts/CartContext";
import { NotificationProvider } from "@/lib/providers/NotificationProvider";
import { Toaster } from "@/components/ui/sonner";
import ErrorBoundary from "@/components/common/ErrorBoundary";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Duka Kuu - Buy & Sell with Confidence",
  description:
    "Kenya's premier marketplace for buyers and sellers with secure transactions and quality products countrywide.",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ErrorBoundary>
          <StoreProvider>
            <AuthProvider>
              <CartProvider>
                <NotificationProvider>
                  {children}
                  <Toaster />
                </NotificationProvider>
              </CartProvider>
            </AuthProvider>
          </StoreProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
