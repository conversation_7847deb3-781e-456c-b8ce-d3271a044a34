import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import Layout from "@/components/layout/Layout";
import {
  ShoppingBag,
  Users,
  Shield,
  Star,
  Globe,
  ArrowRight,
} from "lucide-react";

export default function Home() {
  return (
    <div className="marketplace-theme min-h-screen">
      <Layout>
        {/* Hero Section - Modern & Clean */}
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-gray-50 to-orange-50">
          {/* Subtle Background Pattern */}
          <div className="marketplace-pattern-subtle"></div>

          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div className="text-center marketplace-fade-in">
              {/* Main Heading */}
              <div className="mb-12">
                <h1 className="text-5xl md:text-7xl lg:text-8xl mb-6">
                  <span className="marketplace-heading marketplace-heading-accent block">
                    <PERSON><PERSON> Kuu
                  </span>
                </h1>
                <div className="flex items-center justify-center gap-6 mb-8">
                  <div className="w-16 h-0.5 bg-gradient-to-r from-transparent to-orange-400"></div>
                  <span className="text-xl md:text-2xl marketplace-text-muted font-light tracking-wide">
                    Where Culture Meets Commerce
                  </span>
                  <div className="w-16 h-0.5 bg-gradient-to-l from-transparent to-indigo-400"></div>
                </div>
              </div>

              {/* Subtitle */}
              <p className="text-lg md:text-xl marketplace-text-muted mb-12 max-w-4xl mx-auto leading-relaxed">
                Discover exceptional products from trusted sellers countrywide.
                From{" "}
                <span className="marketplace-text-warm font-medium">
                  electronics
                </span>{" "}
                and
                <span className="marketplace-text-warm font-medium">
                  {" "}
                  fashion
                </span>{" "}
                to
                <span className="marketplace-text-warm font-medium">
                  {" "}
                  furniture
                </span>{" "}
                and
                <span className="marketplace-text-warm font-medium">
                  {" "}
                  automotive parts
                </span>{" "}
                - find everything you need in our premium marketplace.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
                <Button
                  asChild
                  size="lg"
                  className="marketplace-btn-primary text-lg px-10 py-4 rounded-lg"
                >
                  <Link href="/products">
                    <ShoppingBag className="mr-3 h-5 w-5" />
                    Shop Now
                    <ArrowRight className="ml-3 h-5 w-5" />
                  </Link>
                </Button>
                <Button
                  asChild
                  size="lg"
                  className="marketplace-btn-secondary text-lg px-10 py-4 rounded-lg"
                >
                  <Link href="/auth/register">
                    <Users className="mr-3 h-5 w-5" />
                    Become a Seller
                  </Link>
                </Button>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                <div className="marketplace-card p-8 text-center marketplace-hover-lift">
                  <div className="text-4xl font-bold marketplace-text-warm mb-3">
                    25K+
                  </div>
                  <div className="marketplace-text-muted text-lg">
                    Trusted Sellers
                  </div>
                </div>
                <div className="marketplace-card p-8 text-center marketplace-hover-lift">
                  <div className="text-4xl font-bold marketplace-text-cool mb-3">
                    150K+
                  </div>
                  <div className="marketplace-text-muted text-lg">
                    Quality Products
                  </div>
                </div>
                <div className="marketplace-card p-8 text-center marketplace-hover-lift">
                  <div className="text-4xl font-bold text-gray-700 mb-3">
                    500K+
                  </div>
                  <div className="marketplace-text-muted text-lg">
                    Happy Customers
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section - Modern Clean */}
        <section className="py-20 relative bg-white">
          <div className="marketplace-pattern-textile"></div>
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl marketplace-heading mb-6">
                Why Choose{" "}
                <span className="marketplace-heading-accent">Duka Kuu</span>
              </h2>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
                Experience a premium shopping platform built on trust, quality,
                and exceptional service. We connect buyers and sellers
                countrywide with cutting-edge technology and personalized care.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="marketplace-card-warm p-8 text-center marketplace-hover-lift">
                <div className="mb-6">
                  <Shield className="h-16 w-16 marketplace-text-warm mx-auto mb-4" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">
                  Secure Transactions
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Industry-leading security protocols protect every purchase.
                  Shop with confidence knowing your data and payments are
                  completely secure.
                </p>
              </div>

              <div className="marketplace-card-cool p-8 text-center marketplace-hover-lift">
                <div className="mb-6">
                  <Star className="h-16 w-16 marketplace-text-cool mx-auto mb-4" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">
                  Premium Quality
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Curated selection of high-quality products from verified
                  sellers. Every item meets our strict quality standards and
                  customer satisfaction guarantee.
                </p>
              </div>

              <div className="marketplace-card p-8 text-center marketplace-hover-lift">
                <div className="mb-6">
                  <Globe className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">
                  Local Community
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Connect with sellers and buyers from across the country. Join
                  a thriving marketplace community built on trust, respect, and
                  shared success.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section - Modern Clean */}
        <section className="py-20 relative overflow-hidden bg-gradient-to-br from-orange-50 to-indigo-50">
          <div className="marketplace-pattern-subtle"></div>
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-orange-400 to-indigo-400"></div>

          <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-4xl md:text-6xl marketplace-heading mb-8">
                Ready to Start{" "}
                <span className="marketplace-heading-accent">Shopping</span>?
              </h2>

              <p className="text-xl md:text-2xl text-gray-700 mb-12 leading-relaxed">
                Join thousands of satisfied customers who trust Duka Kuu for
                quality products and exceptional service. Your perfect purchase
                is just a{" "}
                <span className="marketplace-text-warm font-semibold">
                  click away
                </span>
                .
              </p>

              <div className="flex flex-col sm:flex-row gap-8 justify-center mb-12">
                <Button
                  size="lg"
                  className="marketplace-btn-primary text-xl px-12 py-6"
                  asChild
                >
                  <Link href="/products">
                    <ShoppingBag className="mr-3 h-6 w-6" />
                    Browse Products
                  </Link>
                </Button>
                <Button
                  size="lg"
                  className="marketplace-btn-secondary text-xl px-12 py-6"
                  asChild
                >
                  <Link href="/auth/register">
                    <Users className="mr-3 h-6 w-6" />
                    Start Selling Today
                  </Link>
                </Button>
              </div>

              {/* Testimonial */}
              <div className="marketplace-card p-8 max-w-2xl mx-auto">
                <p className="text-lg text-gray-700 italic mb-4">
                  "Outstanding marketplace with incredible variety and quality.
                  The customer service is exceptional, and I've found everything
                  I need in one place. Highly recommended!"
                </p>
                <div className="flex items-center justify-center gap-2">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <Star className="h-5 w-5 text-yellow-500" />
                  <Star className="h-5 w-5 text-yellow-500" />
                  <Star className="h-5 w-5 text-yellow-500" />
                  <Star className="h-5 w-5 text-yellow-500" />
                  <span className="ml-2 marketplace-text-warm font-semibold">
                    - Sarah M., Verified Customer
                  </span>
                </div>
              </div>
            </div>
          </div>
        </section>
      </Layout>
    </div>
  );
}
