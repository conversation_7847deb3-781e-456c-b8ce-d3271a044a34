"use client";

import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/lib/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Home,
  ArrowLeft,
  Search,
  AlertTriangle,
  Crown,
  Store,
  ShoppingBag,
} from "lucide-react";
import Link from "next/link";

export default function NotFound() {
  const { user, isAuthenticated, isLoading, isInitialized } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [countdown, setCountdown] = useState(10);

  // Determine the appropriate home page based on user role
  const getHomePage = () => {
    if (!isAuthenticated) return "/";

    switch (user?.role) {
      case "admin":
        return "/admin/dashboard";
      case "seller":
        return "/seller/dashboard";
      case "buyer":
      default:
        return "/products";
    }
  };

  // Determine the context of the 404 (admin, seller, or public)
  const getContext = () => {
    if (pathname.startsWith("/admin/")) return "admin";
    if (pathname.startsWith("/seller/")) return "seller";
    return "public";
  };

  const context = getContext();
  const homePage = getHomePage();

  useEffect(() => {
    if (!isLoading && isInitialized) {
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            const redirectPath = homePage;
            console.log(
              "NotFound: Auto-redirecting to dashboard:",
              redirectPath
            );
            router.push(redirectPath);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [router, homePage, isLoading, isInitialized]);
  if (isLoading || !isInitialized) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const getContextIcon = () => {
    switch (context) {
      case "admin":
        return <Crown className="h-16 w-16 text-red-500" />;
      case "seller":
        return <Store className="h-16 w-16 text-blue-500" />;
      default:
        return <ShoppingBag className="h-16 w-16 text-green-500" />;
    }
  };

  const getContextTitle = () => {
    switch (context) {
      case "admin":
        return "Admin Page Not Found";
      case "seller":
        return "Seller Page Not Found";
      default:
        return "Page Not Found";
    }
  };

  const getContextDescription = () => {
    switch (context) {
      case "admin":
        return "The admin page you're looking for doesn't exist or may have been moved.";
      case "seller":
        return "The seller page you're looking for doesn't exist or may have been moved.";
      default:
        return "The page you're looking for doesn't exist or may have been moved.";
    }
  };

  const getSuggestedLinks = () => {
    switch (context) {
      case "admin":
        return [
          { href: "/admin/dashboard", label: "Admin Dashboard", icon: Crown },
          { href: "/admin/users", label: "User Management", icon: Crown },
          { href: "/admin/settings", label: "Admin Settings", icon: Crown },
        ];
      case "seller":
        return [
          { href: "/seller/dashboard", label: "Seller Dashboard", icon: Store },
          { href: "/seller/products", label: "My Products", icon: Store },
          { href: "/seller/settings", label: "Seller Settings", icon: Store },
        ];
      default:
        return [
          { href: "/", label: "Home Page", icon: Home },
          { href: "/products", label: "Browse Products", icon: ShoppingBag },
          { href: "/auth/login", label: "Sign In", icon: ShoppingBag },
        ];
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-6">
        <Card className="text-center">
          <CardHeader className="space-y-4">
            <div className="flex justify-center">{getContextIcon()}</div>
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900">
                {getContextTitle()}
              </CardTitle>
              <CardDescription className="text-lg mt-2">
                {getContextDescription()}
              </CardDescription>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-gray-100 rounded-lg p-4">
              <div className="flex items-center justify-center space-x-2 text-gray-600">
                <AlertTriangle className="h-5 w-5" />
                <span className="font-mono text-lg">404</span>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Requested:{" "}
                <code className="bg-white px-2 py-1 rounded text-xs">
                  {pathname}
                </code>
              </p>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-blue-800 text-sm">
                Redirecting to your dashboard in{" "}
                <span className="font-bold">{countdown}</span> seconds...
              </p>
            </div>
            <div className="space-y-3">
              <Button
                onClick={() => router.push(homePage)}
                variant="outline"
                className="w-full"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go to Dashboard
              </Button>

              <Button asChild className="w-full">
                <Link href={homePage}>
                  <Home className="mr-2 h-4 w-4" />
                  Go to Dashboard
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Suggested Pages</CardTitle>
            <CardDescription>
              Here are some pages you might be looking for:
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {getSuggestedLinks().map((link) => (
                <Button
                  key={link.href}
                  asChild
                  variant="ghost"
                  className="w-full justify-start"
                >
                  <Link href={link.href}>
                    <link.icon className="mr-2 h-4 w-4" />
                    {link.label}
                  </Link>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <p className="text-sm text-gray-600">
                Still can't find what you're looking for?
              </p>
              <Button variant="outline" size="sm">
                <Search className="mr-2 h-4 w-4" />
                Search Help
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
