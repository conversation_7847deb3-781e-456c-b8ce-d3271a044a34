"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/contexts/AuthContext";
import { useNotifications } from "@/lib/hooks/useNotifications";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  User,
  Mail,
  Shield,
  MapPin,
  Phone,
  Calendar,
  Edit,
  Save,
  X,
  ArrowLeft,
} from "lucide-react";
import Link from "next/link";

interface ShippingAddress {
  fullName: string;
  phone: string;
  street: string;
  city: string;
  county: string;
  postalCode: string;
  country: string;
}

interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: "buyer" | "seller" | "admin";
  isVerified: boolean;
  defaultShippingAddress?: ShippingAddress;
  createdAt: string;
  updatedAt: string;
}

export default function ProfilePage() {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [editForm, setEditForm] = useState({
    name: "",
    defaultShippingAddress: {
      fullName: "",
      phone: "",
      street: "",
      city: "",
      county: "",
      postalCode: "",
      country: "Kenya",
    },
  });

  const { isAuthenticated, token } = useAuth();
  const notifications = useNotifications();

  useEffect(() => {
    if (isAuthenticated && token) {
      fetchProfile();
    }
  }, [isAuthenticated, token]);

  const fetchProfile = async () => {
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/auth/profile`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch profile");
      }

      const data = await response.json();
      setProfile(data.data);

      // Initialize edit form with current data
      setEditForm({
        name: data.data.name,
        defaultShippingAddress: data.data.defaultShippingAddress || {
          fullName: "",
          phone: "",
          street: "",
          city: "",
          county: "",
          postalCode: "",
          country: "Kenya",
        },
      });
    } catch (error) {
      console.error("Error fetching profile:", error);
      notifications.error("Failed to load profile");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/auth/profile`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(editForm),
      });

      if (!response.ok) {
        throw new Error("Failed to update profile");
      }

      const data = await response.json();
      setProfile(data.data);
      setIsEditing(false);
      notifications.success("Profile updated successfully");
    } catch (error) {
      console.error("Error updating profile:", error);
      notifications.error("Failed to update profile");
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (profile) {
      setEditForm({
        name: profile.name,
        defaultShippingAddress: profile.defaultShippingAddress || {
          fullName: "",
          phone: "",
          street: "",
          city: "",
          county: "",
          postalCode: "",
          country: "Kenya",
        },
      });
    }
    setIsEditing(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-red-100 text-red-800";
      case "seller":
        return "bg-blue-100 text-blue-800";
      case "buyer":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <User className="mx-auto h-16 w-16 marketplace-text-muted mb-6" />
            <h1 className="text-3xl marketplace-heading mb-4">
              Sign In to View Profile
            </h1>
            <p className="marketplace-text-muted mb-8">
              Please sign in to access your profile.
            </p>
            <Button asChild className="marketplace-btn-primary">
              <Link href="/auth/login">Sign In</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <User className="mx-auto h-16 w-16 text-red-500 mb-6" />
            <h2 className="text-2xl marketplace-heading mb-4">
              Profile Not Found
            </h2>
            <p className="marketplace-text-muted mb-8">
              Unable to load your profile information.
            </p>
            <Button onClick={fetchProfile} className="marketplace-btn-primary">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="marketplace-theme min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl marketplace-heading mb-2">
              My <span className="marketplace-heading-accent">Profile</span>
            </h1>
            <p className="marketplace-text-muted">
              Manage your account information and preferences
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              asChild
              variant="outline"
              className="marketplace-btn-outline"
            >
              <Link href="/orders">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Orders
              </Link>
            </Button>
            {!isEditing ? (
              <Button
                onClick={() => setIsEditing(true)}
                className="marketplace-btn-primary"
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit Profile
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  onClick={handleCancel}
                  variant="outline"
                  className="marketplace-btn-outline"
                >
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="marketplace-btn-primary"
                >
                  <Save className="mr-2 h-4 w-4" />
                  {isSaving ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name" className="marketplace-text-primary">
                      Full Name
                    </Label>
                    {isEditing ? (
                      <Input
                        id="name"
                        value={editForm.name}
                        onChange={(e) =>
                          setEditForm({ ...editForm, name: e.target.value })
                        }
                        className="mt-1"
                      />
                    ) : (
                      <p className="mt-1 marketplace-text-primary font-medium">
                        {profile.name}
                      </p>
                    )}
                  </div>
                  <div>
                    <Label className="marketplace-text-primary">Email</Label>
                    <div className="mt-1 flex items-center gap-2">
                      <Mail className="h-4 w-4 marketplace-text-muted" />
                      <span className="marketplace-text-primary">
                        {profile.email}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Shipping Address */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary flex items-center">
                  <MapPin className="mr-2 h-5 w-5" />
                  Default Shipping Address
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {isEditing ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label
                          htmlFor="fullName"
                          className="marketplace-text-primary"
                        >
                          Full Name
                        </Label>
                        <Input
                          id="fullName"
                          value={editForm.defaultShippingAddress.fullName}
                          onChange={(e) =>
                            setEditForm({
                              ...editForm,
                              defaultShippingAddress: {
                                ...editForm.defaultShippingAddress,
                                fullName: e.target.value,
                              },
                            })
                          }
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label
                          htmlFor="phone"
                          className="marketplace-text-primary"
                        >
                          Phone Number
                        </Label>
                        <Input
                          id="phone"
                          value={editForm.defaultShippingAddress.phone}
                          onChange={(e) =>
                            setEditForm({
                              ...editForm,
                              defaultShippingAddress: {
                                ...editForm.defaultShippingAddress,
                                phone: e.target.value,
                              },
                            })
                          }
                          placeholder="e.g., +254 700 000 000"
                          className="mt-1"
                        />
                      </div>
                    </div>

                    <div>
                      <Label
                        htmlFor="street"
                        className="marketplace-text-primary"
                      >
                        Street Address
                      </Label>
                      <Input
                        id="street"
                        value={editForm.defaultShippingAddress.street}
                        onChange={(e) =>
                          setEditForm({
                            ...editForm,
                            defaultShippingAddress: {
                              ...editForm.defaultShippingAddress,
                              street: e.target.value,
                            },
                          })
                        }
                        className="mt-1"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label
                          htmlFor="city"
                          className="marketplace-text-primary"
                        >
                          City
                        </Label>
                        <Input
                          id="city"
                          value={editForm.defaultShippingAddress.city}
                          onChange={(e) =>
                            setEditForm({
                              ...editForm,
                              defaultShippingAddress: {
                                ...editForm.defaultShippingAddress,
                                city: e.target.value,
                              },
                            })
                          }
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label
                          htmlFor="county"
                          className="marketplace-text-primary"
                        >
                          County
                        </Label>
                        <Input
                          id="county"
                          value={editForm.defaultShippingAddress.county}
                          onChange={(e) =>
                            setEditForm({
                              ...editForm,
                              defaultShippingAddress: {
                                ...editForm.defaultShippingAddress,
                                county: e.target.value,
                              },
                            })
                          }
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label
                          htmlFor="postalCode"
                          className="marketplace-text-primary"
                        >
                          Postal Code
                        </Label>
                        <Input
                          id="postalCode"
                          value={editForm.defaultShippingAddress.postalCode}
                          onChange={(e) =>
                            setEditForm({
                              ...editForm,
                              defaultShippingAddress: {
                                ...editForm.defaultShippingAddress,
                                postalCode: e.target.value,
                              },
                            })
                          }
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </div>
                ) : profile.defaultShippingAddress ? (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 marketplace-text-muted" />
                        <span className="marketplace-text-primary font-medium">
                          {profile.defaultShippingAddress.fullName}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 marketplace-text-muted" />
                        <span className="marketplace-text-primary">
                          {profile.defaultShippingAddress.phone}
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <MapPin className="h-4 w-4 marketplace-text-muted mt-0.5" />
                        <div className="marketplace-text-primary">
                          <div>{profile.defaultShippingAddress.street}</div>
                          <div>
                            {profile.defaultShippingAddress.city},{" "}
                            {profile.defaultShippingAddress.county}
                          </div>
                          <div>
                            {profile.defaultShippingAddress.postalCode},{" "}
                            {profile.defaultShippingAddress.country}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <MapPin className="mx-auto h-12 w-12 marketplace-text-muted mb-3" />
                    <p className="marketplace-text-muted">
                      No default shipping address set
                    </p>
                    <p className="text-sm marketplace-text-muted">
                      Add an address to speed up checkout
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Account Details */}
          <div className="space-y-6">
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary flex items-center">
                  <Shield className="mr-2 h-5 w-5" />
                  Account Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="marketplace-text-primary">Role</Label>
                  <div className="mt-1">
                    <Badge className={getRoleBadgeColor(profile.role)}>
                      {profile.role.charAt(0).toUpperCase() +
                        profile.role.slice(1)}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label className="marketplace-text-primary">Status</Label>
                  <div className="mt-1">
                    <Badge
                      className={
                        profile.isVerified
                          ? "bg-green-100 text-green-800"
                          : "bg-yellow-100 text-yellow-800"
                      }
                    >
                      {profile.isVerified ? "Verified" : "Unverified"}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label className="marketplace-text-primary">
                    Member Since
                  </Label>
                  <div className="mt-1 flex items-center gap-2">
                    <Calendar className="h-4 w-4 marketplace-text-muted" />
                    <span className="marketplace-text-primary text-sm">
                      {formatDate(profile.createdAt)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
