import { NextRequest, NextResponse } from 'next/server'
import { mockUsers } from '@/lib/mocks/data'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Find user by email
    const user = mockUsers.find(u => u.email === email)

    if (!user) {
      return NextResponse.json(
        { message: 'Invalid email or password' },
        { status: 401 }
      )
    }

    // For demo purposes, accept any password
    // In real app, you'd verify the password hash
    if (!password) {
      return NextResponse.json(
        { message: 'Password is required' },
        { status: 400 }
      )
    }

    // Generate mock tokens
    const token = `mock-jwt-token-${user.id}-${Date.now()}`
    const refreshToken = `mock-refresh-token-${user.id}-${Date.now()}`

    return NextResponse.json({
      user,
      token,
      refreshToken,
      message: 'Login successful'
    })
  } catch (error) {
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
