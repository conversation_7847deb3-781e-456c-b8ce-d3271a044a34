import { NextRequest, NextResponse } from 'next/server'
import { mockUsers } from '@/lib/mocks/data'
import type { User, UserRole } from '@/lib/features/auth/authSlice'

export async function POST(request: NextRequest) {
  try {
    const { email, password, name, role } = await request.json()

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Validate required fields
    if (!email || !password || !name || !role) {
      return NextResponse.json(
        { message: 'All fields are required' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email === email)
    if (existingUser) {
      return NextResponse.json(
        { message: 'User with this email already exists' },
        { status: 409 }
      )
    }

    // Validate role
    const validRoles: UserRole[] = ['buyer', 'seller', 'admin']
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { message: 'Invalid role' },
        { status: 400 }
      )
    }

    // Create new user
    const newUser: User = {
      id: (mockUsers.length + 1).toString(),
      email,
      name,
      role,
      avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random`,
    }

    // Add to mock users (in real app, save to database)
    mockUsers.push(newUser)

    // Generate mock tokens
    const token = `mock-jwt-token-${newUser.id}-${Date.now()}`
    const refreshToken = `mock-refresh-token-${newUser.id}-${Date.now()}`

    return NextResponse.json({
      user: newUser,
      token,
      refreshToken,
      message: 'Registration successful'
    }, { status: 201 })
  } catch (error) {
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
