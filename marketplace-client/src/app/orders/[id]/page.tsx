"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useAuth } from "@/lib/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Package,
  Truck,
  CheckCircle,
  Clock,
  XCircle,
  ArrowLeft,
  MapPin,
  Calendar,
  User,
  Phone,
  Mail,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { Order } from "../page";
import { getFirstImageUrl } from "@/lib/utils/imageUtils";

export default function OrderDetailsPage() {
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, token } = useAuth();
  const params = useParams();
  const router = useRouter();
  const orderId = params.id as string;

  useEffect(() => {
    if (isAuthenticated && token && orderId) {
      fetchOrderDetails();
    }
  }, [isAuthenticated, token, orderId]);

  const fetchOrderDetails = async () => {
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/orders/${orderId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch order details");
      }

      const data = await response.json();
      setOrder(data.data);
    } catch (error) {
      console.error("Error fetching order details:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load order details"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case "paid":
        return <CheckCircle className="h-5 w-5 text-blue-500" />;
      case "shipped":
        return <Truck className="h-5 w-5 text-orange-500" />;
      case "fulfilled":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "cancelled":
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "paid":
        return "bg-blue-100 text-blue-800";
      case "shipped":
        return "bg-orange-100 text-orange-800";
      case "fulfilled":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getItemStatusColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return "bg-yellow-100 text-yellow-800";
      case "SHIPPED":
        return "bg-orange-100 text-orange-800";
      case "DELIVERED":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (!isAuthenticated) {
    router.push("/auth/login");
    return null;
  }

  if (isLoading) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <XCircle className="mx-auto h-16 w-16 text-red-500 mb-6" />
            <h2 className="text-2xl marketplace-heading mb-4">
              Order Not Found
            </h2>
            <p className="marketplace-text-muted mb-8">{error}</p>
            <Button asChild className="marketplace-btn-primary">
              <Link href="/orders">Back to Orders</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="marketplace-theme min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl marketplace-heading mb-2">
              Order{" "}
              <span className="marketplace-heading-accent">
                #{order.id.slice(-8).toUpperCase()}
              </span>
            </h1>
            <p className="marketplace-text-muted">
              Placed on {formatDate(order.createdAt)}
            </p>
          </div>
          <Button asChild variant="outline" className="marketplace-btn-outline">
            <Link href="/orders">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Orders
            </Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Items */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Status */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary flex items-center justify-between">
                  <span>Order Status</span>
                  <Badge className={getStatusColor(order.status)}>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(order.status)}
                      {order.status.charAt(0).toUpperCase() +
                        order.status.slice(1)}
                    </div>
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-5 w-5 marketplace-text-muted" />
                    <div>
                      <p className="font-medium marketplace-text-primary">
                        Order Placed
                      </p>
                      <p className="text-sm marketplace-text-muted">
                        {formatDate(order.createdAt)}
                      </p>
                    </div>
                  </div>

                  {order.status === "shipped" && (
                    <div className="flex items-center gap-3">
                      <Truck className="h-5 w-5 marketplace-text-muted" />
                      <div>
                        <p className="font-medium marketplace-text-primary">
                          Shipped
                        </p>
                        <p className="text-sm marketplace-text-muted">
                          Tracking: TRK
                          {Math.floor(100000 + Math.random() * 900000)}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Order Items */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary">
                  Order Items ({order.items.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {order.items.map((item) => (
                  <div
                    key={item.id}
                    className="flex gap-4 p-4 border rounded-lg"
                  >
                    {/* Product Image */}
                    <div className="relative w-20 h-20 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        src={getFirstImageUrl(item.product.images)}
                        alt={item.product.title}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          // Fallback to placeholder if image fails to load
                          e.currentTarget.src = "/placeholder-product.svg";
                        }}
                      />
                    </div>

                    {/* Product Details */}
                    <div className="flex-1">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="font-semibold marketplace-text-primary">
                            {item.product.title}
                          </h4>
                          <p className="text-sm marketplace-text-muted">
                            Sold by{" "}
                            {item.product.seller?.name || "Unknown Seller"}
                          </p>
                        </div>
                        <Badge className={getItemStatusColor(item.status)}>
                          {item.status}
                        </Badge>
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="text-sm marketplace-text-muted">
                          Quantity: {item.quantity} ×{" "}
                          {formatPrice(item.unitPrice)}
                        </div>
                        <div className="font-semibold marketplace-text-warm">
                          {formatPrice(item.totalPrice)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1 space-y-6">
            {/* Payment Summary */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary">
                  Payment Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="marketplace-text-muted">
                      Subtotal ({order.items.length} items)
                    </span>
                    <span className="marketplace-text-primary">
                      {formatPrice(order.total)}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="marketplace-text-muted">Shipping</span>
                    <span className="marketplace-text-primary">Free</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="marketplace-text-muted">Tax</span>
                    <span className="marketplace-text-primary">$0.00</span>
                  </div>
                </div>

                <Separator />

                <div className="flex justify-between text-lg font-semibold">
                  <span className="marketplace-text-primary">Total Paid</span>
                  <span className="marketplace-text-warm">
                    {formatPrice(order.total)}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Need Help */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary">
                  Need Help?
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 marketplace-text-muted" />
                    <div>
                      <p className="text-sm font-medium marketplace-text-primary">
                        Call Support
                      </p>
                      <p className="text-sm marketplace-text-muted">
                        1-800-MARKETPLACE
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 marketplace-text-muted" />
                    <div>
                      <p className="text-sm font-medium marketplace-text-primary">
                        Email Support
                      </p>
                      <p className="text-sm marketplace-text-muted">
                        <EMAIL>
                      </p>
                    </div>
                  </div>
                </div>

                <Button
                  variant="outline"
                  className="w-full marketplace-btn-outline"
                >
                  Contact Support
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
