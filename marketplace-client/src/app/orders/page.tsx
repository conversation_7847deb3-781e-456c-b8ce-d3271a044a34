"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ShoppingCart,
  Package,
  Truck,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  ArrowLeft,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { getFirstImageUrl } from "@/lib/utils/imageUtils";

export interface OrderItem {
  id: string;
  product: {
    id: string;
    title: string;
    price: number;
    images: string[];
    seller: {
      id: string;
      name: string;
    };
  };
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  status: "PENDING" | "SHIPPED" | "DELIVERED";
}

export interface Order {
  id: string;
  status: "pending" | "paid" | "shipped" | "fulfilled" | "cancelled";
  total: number;
  createdAt: string;
  items: OrderItem[];
}

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, token } = useAuth();

  useEffect(() => {
    if (isAuthenticated && token) {
      fetchOrders();
    }
  }, [isAuthenticated, token]);

  const fetchOrders = async () => {
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/orders/my`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch orders");
      }

      const data = await response.json();
      setOrders(data.orders || []);
    } catch (error) {
      console.error("Error fetching orders:", error);
      setError("Failed to load orders");
    } finally {
      setIsLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "paid":
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case "shipped":
        return <Truck className="h-4 w-4 text-orange-500" />;
      case "fulfilled":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "cancelled":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "paid":
        return "bg-blue-100 text-blue-800";
      case "shipped":
        return "bg-orange-100 text-orange-800";
      case "fulfilled":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <ShoppingCart className="mx-auto h-16 w-16 marketplace-text-muted mb-6" />
            <h1 className="text-3xl marketplace-heading mb-4">
              Sign In to View Orders
            </h1>
            <p className="marketplace-text-muted mb-8">
              Please sign in to access your order history.
            </p>
            <div className="space-x-4">
              <Button asChild className="marketplace-btn-primary">
                <Link href="/auth/login">Sign In</Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="marketplace-btn-outline"
              >
                <Link href="/products">Browse Products</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="marketplace-theme min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl marketplace-heading mb-2">
              My <span className="marketplace-heading-accent">Orders</span>
            </h1>
            <p className="marketplace-text-muted">
              Track and manage your orders
            </p>
          </div>
          <Button asChild variant="outline" className="marketplace-btn-outline">
            <Link href="/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Continue Shopping
            </Link>
          </Button>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <XCircle className="mx-auto h-16 w-16 text-red-500 mb-6" />
            <h2 className="text-2xl marketplace-heading mb-4">
              Error Loading Orders
            </h2>
            <p className="marketplace-text-muted mb-8">{error}</p>
            <Button onClick={fetchOrders} className="marketplace-btn-primary">
              Try Again
            </Button>
          </div>
        ) : orders.length === 0 ? (
          <div className="text-center py-16">
            <Package className="mx-auto h-16 w-16 marketplace-text-muted mb-6" />
            <h2 className="text-2xl marketplace-heading mb-4">No Orders Yet</h2>
            <p className="marketplace-text-muted mb-8 max-w-md mx-auto">
              You haven't placed any orders yet. Start shopping to see your
              orders here!
            </p>
            <Button asChild className="marketplace-btn-primary">
              <Link href="/products">Start Shopping</Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {orders.map((order) => (
              <Card key={order.id} className="marketplace-card">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="marketplace-text-primary">
                        Order #{order.id.slice(-8).toUpperCase()}
                      </CardTitle>
                      <p className="marketplace-text-muted">
                        Placed on {formatDate(order.createdAt)}
                      </p>
                    </div>
                    <div className="text-right">
                      <Badge className={getStatusColor(order.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(order.status)}
                          {order.status.charAt(0).toUpperCase() +
                            order.status.slice(1)}
                        </div>
                      </Badge>
                      <p className="text-lg font-bold marketplace-text-warm mt-2">
                        {formatPrice(order.total)}
                      </p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Order Items Preview */}
                    <div className="space-y-3">
                      {order.items.slice(0, 2).map((item) => (
                        <div
                          key={item.id}
                          className="flex gap-3 p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="relative w-12 h-12 bg-white rounded overflow-hidden flex-shrink-0">
                            <Image
                              src={getFirstImageUrl(item.product.images)}
                              alt={item.product.title}
                              fill
                              className="object-cover"
                              onError={(e) => {
                                e.currentTarget.src =
                                  "/placeholder-product.svg";
                              }}
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium marketplace-text-primary text-sm truncate">
                              {item.product.title}
                            </p>
                            <p className="text-xs marketplace-text-muted">
                              Qty: {item.quantity} •{" "}
                              {formatPrice(item.unitPrice)} each
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold marketplace-text-primary text-sm">
                              {formatPrice(item.totalPrice)}
                            </p>
                          </div>
                        </div>
                      ))}

                      {order.items.length > 2 && (
                        <p className="text-sm marketplace-text-muted text-center">
                          +{order.items.length - 2} more items
                        </p>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex justify-between items-center pt-4 border-t">
                      <div className="text-sm marketplace-text-muted">
                        {order.items.length} item
                        {order.items.length !== 1 ? "s" : ""}
                      </div>
                      <Button
                        asChild
                        variant="outline"
                        size="sm"
                        className="marketplace-btn-outline"
                      >
                        <Link href={`/orders/${order.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
