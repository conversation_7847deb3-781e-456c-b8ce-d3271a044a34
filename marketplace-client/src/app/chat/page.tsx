"use client";

import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useSearchParams } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MessageCircle, ArrowLeft } from "lucide-react";
import { ChatList } from "@/components/chat/ChatList";
import { ChatWindow } from "@/components/chat/ChatWindow";
import {
  Conversation,
  useStartConversationMutation,
  useGetConversationsQuery,
} from "@/lib/api/chat";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

export default function ChatPage() {
  const [selectedConversation, setSelectedConversation] =
    useState<Conversation | null>(null);
  const [isMobileView, setIsMobileView] = useState(false);
  const [showChatList, setShowChatList] = useState(true);

  const currentUser = useSelector((state: any) => state.auth.user);
  const [startConversation] = useStartConversationMutation();
  const searchParams = useSearchParams();

  // Get conversations to find the one from URL params
  const { data: conversations = [] } = useGetConversationsQuery();

  // Handle URL conversation parameter
  useEffect(() => {
    const conversationId = searchParams.get("conversation");
    if (conversationId && conversations.length > 0) {
      const conversation = conversations.find((c) => c.id === conversationId);
      if (conversation) {
        setSelectedConversation(conversation);
      }
    }
  }, [searchParams, conversations]);

  // Handle responsive design
  useEffect(() => {
    const checkMobile = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Mobile view logic
  useEffect(() => {
    if (isMobileView) {
      setShowChatList(!selectedConversation);
    } else {
      setShowChatList(true);
    }
  }, [selectedConversation, isMobileView]);

  const handleConversationSelect = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    if (isMobileView) {
      setShowChatList(false);
    }
  };

  const handleBackToList = () => {
    setSelectedConversation(null);
    setShowChatList(true);
  };

  const handleNewChat = async () => {
    // For now, show a placeholder
    toast.info("New chat feature coming soon!");

    // Future implementation:
    // - Show user selection modal
    // - Allow searching for users
    // - Start conversation with selected user
  };

  if (!currentUser) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 to-blue-50">
        <Card className="w-full max-w-md">
          <CardContent className="text-center py-8">
            <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Please Sign In</h2>
            <p className="text-gray-600 mb-4">
              You need to be signed in to access the chat feature.
            </p>
            <Button onClick={() => (window.location.href = "/auth/signin")}>
              Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-blue-50">
      <div className="container mx-auto p-4 h-screen">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-blue-600 bg-clip-text text-transparent">
                💬 Duka Kuu Chat
              </h1>
              <p className="text-gray-600 mt-1">
                Connect with buyers and sellers
              </p>
            </div>

            {/* Mobile back button */}
            {isMobileView && !showChatList && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToList}
                className="md:hidden"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            )}
          </div>
        </div>

        {/* Chat Interface */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-[calc(100vh-140px)]">
          {/* Chat List - Left Panel */}
          <div
            className={cn(
              "md:col-span-1",
              isMobileView && !showChatList && "hidden"
            )}
          >
            <ChatList
              selectedConversationId={selectedConversation?.id}
              onConversationSelect={handleConversationSelect}
              onNewChat={handleNewChat}
              currentUserId={currentUser.id}
            />
          </div>

          {/* Chat Window - Right Panel */}
          <div
            className={cn(
              "md:col-span-2",
              isMobileView && showChatList && "hidden"
            )}
          >
            {selectedConversation ? (
              <ChatWindow
                conversation={selectedConversation}
                currentUserId={currentUser.id}
              />
            ) : (
              <Card className="h-full flex items-center justify-center">
                <CardContent className="text-center">
                  <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-700 mb-2">
                    Welcome to Duka Kuu Chat
                  </h3>
                  <p className="text-gray-500 mb-6 max-w-md">
                    Select a conversation from the list to start chatting, or
                    start a new conversation.
                  </p>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-md mx-auto">
                      <div className="bg-orange-50 p-4 rounded-lg">
                        <div className="text-orange-600 font-medium mb-1">
                          🛒 For Buyers
                        </div>
                        <div className="text-sm text-gray-600">
                          Chat with sellers about products, ask questions, and
                          negotiate prices.
                        </div>
                      </div>
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <div className="text-blue-600 font-medium mb-1">
                          🏪 For Sellers
                        </div>
                        <div className="text-sm text-gray-600">
                          Respond to buyer inquiries, provide product details,
                          and close sales.
                        </div>
                      </div>
                    </div>
                    <Button
                      onClick={handleNewChat}
                      className="bg-orange-600 hover:bg-orange-700"
                    >
                      Start New Conversation
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
