"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Package,
  Search,
  Filter,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  Eye,
  Edit,
  RefreshCw,
  Download,
  Clock,
  Zap,
} from "lucide-react";
import {
  useGetAllAdminInventoryQuery,
  useGetAdminInventoryStatsQuery,
  useGetLowStockAlertsQuery,
  useBulkUpdateInventoryMutation,
  useUpdateInventoryStatusMutation,
  useAutoRestockMutation,
} from "@/lib/api/inventory";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";

export default function AdminInventory() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [page, setPage] = useState(1);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // API queries
  const {
    data: inventoryData,
    isLoading: inventoryLoading,
    error: inventoryError,
    refetch: refetchInventory,
  } = useGetAllAdminInventoryQuery({
    page,
    limit: 20,
    search: searchTerm || undefined,
    status: filterStatus !== "all" ? filterStatus : undefined,
  });

  const { data: statsData, isLoading: statsLoading } =
    useGetAdminInventoryStatsQuery();

  const { data: alertsData, isLoading: alertsLoading } =
    useGetLowStockAlertsQuery({ severity: "all" });

  // Mutations
  const [updateInventoryStatus] = useUpdateInventoryStatusMutation();
  const [bulkUpdateInventory] = useBulkUpdateInventoryMutation();
  const [autoRestock] = useAutoRestockMutation();

  const inventoryItems = inventoryData?.data?.inventories || [];
  const stats = statsData?.data?.overview;
  const alerts = alertsData?.data || [];

  // Helper functions
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      in_stock: {
        label: "In Stock",
        variant: "default" as const,
        icon: CheckCircle,
      },
      low_stock: {
        label: "Low Stock",
        variant: "destructive" as const,
        icon: AlertTriangle,
      },
      out_of_stock: {
        label: "Out of Stock",
        variant: "secondary" as const,
        icon: XCircle,
      },
      discontinued: {
        label: "Discontinued",
        variant: "outline" as const,
        icon: Clock,
      },
    };
    return (
      statusConfig[status as keyof typeof statusConfig] || statusConfig.in_stock
    );
  };

  const handleStatusUpdate = async (inventoryId: string, newStatus: string) => {
    try {
      await updateInventoryStatus({
        id: inventoryId,
        status: newStatus,
        reason: `Status updated to ${newStatus} by admin`,
      }).unwrap();
      toast.success("Inventory status updated successfully");
    } catch (error) {
      toast.error("Failed to update inventory status");
    }
  };

  const handleBulkRestock = async () => {
    if (selectedItems.length === 0) {
      toast.error("Please select items to restock");
      return;
    }

    try {
      await autoRestock({
        inventoryIds: selectedItems,
        restockQuantity: 50, // Default restock quantity
      }).unwrap();
      toast.success(`Successfully restocked ${selectedItems.length} items`);
      setSelectedItems([]);
    } catch (error) {
      toast.error("Failed to restock items");
    }
  };

  const filteredItems = inventoryItems.filter((item) => {
    const matchesSearch =
      !searchTerm ||
      item.product?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.sku?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterStatus === "all" || item.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  if (inventoryLoading || statsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6 bg-gradient-to-br from-orange-50 to-blue-50">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-orange-600 to-blue-600 bg-clip-text text-transparent">
            🏪 Duka Kuu Admin Inventory
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Comprehensive inventory management for the marketplace
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.totalProducts || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Across all categories
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">In Stock</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {stats?.inStock || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {stats?.totalProducts
                  ? ((stats.inStock / stats.totalProducts) * 100).toFixed(1)
                  : 0}
                % of total
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-yellow-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {stats?.lowStock || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Need attention
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-red-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
              <XCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {stats?.outOfStock || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Require restocking
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Inventory Management</CardTitle>
            <CardDescription>
              Search, filter, and manage your inventory items
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by product name or SKU..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full md:w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="in_stock">In Stock</SelectItem>
                  <SelectItem value="low_stock">Low Stock</SelectItem>
                  <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                  <SelectItem value="discontinued">Discontinued</SelectItem>
                </SelectContent>
              </Select>
              <Button
                onClick={handleBulkRestock}
                disabled={selectedItems.length === 0}
                className="bg-orange-600 hover:bg-orange-700"
              >
                <Zap className="h-4 w-4 mr-2" />
                Bulk Restock ({selectedItems.length})
              </Button>
              <Button variant="outline" onClick={() => refetchInventory()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>

            {/* Inventory Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <input
                        type="checkbox"
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedItems(inventoryItems.map((item) => item.id));
                          } else {
                            setSelectedItems([]);
                          }
                        }}
                        checked={selectedItems.length === inventoryItems.length}
                      />
                    </TableHead>
                    <TableHead>Product</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Stock</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Seller</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredItems.map((item) => {
                    const statusConfig = getStatusBadge(item.status);
                    const StatusIcon = statusConfig.icon;
                    
                    return (
                      <TableRow key={item.id}>
                        <TableCell>
                          <input
                            type="checkbox"
                            checked={selectedItems.includes(item.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedItems([...selectedItems, item.id]);
                              } else {
                                setSelectedItems(
                                  selectedItems.filter((id) => id !== item.id)
                                );
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-gray-200 rounded-md flex items-center justify-center">
                              <Package className="h-5 w-5 text-gray-500" />
                            </div>
                            <div>
                              <div className="font-medium">
                                {item.product?.title || "Unknown Product"}
                              </div>
                              <div className="text-sm text-gray-500">
                                ID: {item.id.slice(0, 8)}...
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {item.sku || "N/A"}
                        </TableCell>
                        <TableCell>
                          {item.product?.category?.name || "Uncategorized"}
                        </TableCell>
                        <TableCell>
                          <div className="text-center">
                            <div className="font-semibold">{item.currentStock}</div>
                            <div className="text-xs text-gray-500">
                              Threshold: {item.lowStockThreshold}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={statusConfig.variant} className="flex items-center gap-1">
                            <StatusIcon className="h-3 w-3" />
                            {statusConfig.label}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {formatCurrency(item.costPrice || 0)}
                        </TableCell>
                        <TableCell>
                          {item.product?.seller?.name || "Unknown Seller"}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>

            {filteredItems.length === 0 && (
              <div className="text-center py-8">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No inventory items found</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
