"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Activity,
  RefreshCw,
  Download,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
} from "lucide-react";
import { SystemHealthCards } from "@/components/admin/monitoring/SystemHealthCards";
import { PerformanceCharts } from "@/components/admin/monitoring/PerformanceCharts";
import { EmailDLQCharts } from "@/components/admin/monitoring/EmailDLQCharts";
import { RealTimeMetrics } from "@/components/admin/monitoring/RealTimeMetrics";
import {
  useGetSystemHealthQuery,
  useGetEmailMetricsQuery,
  useGetPerformanceMetricsQuery,
  useGetTimeoutAnalyticsQuery,
  useGetDLQStatsQuery,
  useProcessDLQMutation,
  useCleanupDLQMutation,
} from "@/lib/api/monitoringApi";
import { toast } from "sonner";

export default function TechnicalMonitoringPage() {
  const [refreshInterval, setRefreshInterval] = useState(60000); // 60 seconds (reduced from 30s to reduce server load)
  const [autoRefresh, setAutoRefresh] = useState(true);

  // API queries with polling
  const {
    data: healthData,
    isLoading: healthLoading,
    refetch: refetchHealth,
  } = useGetSystemHealthQuery(undefined, {
    pollingInterval: autoRefresh ? refreshInterval : 0,
  });

  const {
    data: emailData,
    isLoading: emailLoading,
    refetch: refetchEmail,
  } = useGetEmailMetricsQuery(undefined, {
    pollingInterval: autoRefresh ? refreshInterval : 0,
  });

  const {
    data: performanceData,
    isLoading: performanceLoading,
    refetch: refetchPerformance,
  } = useGetPerformanceMetricsQuery(
    { timeWindow: 3600 },
    {
      pollingInterval: autoRefresh ? refreshInterval : 0,
    }
  );

  const {
    data: timeoutData,
    isLoading: timeoutLoading,
    refetch: refetchTimeout,
  } = useGetTimeoutAnalyticsQuery(undefined, {
    pollingInterval: autoRefresh ? refreshInterval : 0,
  });

  const {
    data: dlqData,
    isLoading: dlqLoading,
    refetch: refetchDLQ,
  } = useGetDLQStatsQuery(undefined, {
    pollingInterval: autoRefresh ? refreshInterval : 0,
  });

  // Mutations
  const [processDLQ, { isLoading: processingDLQ }] = useProcessDLQMutation();
  const [cleanupDLQ, { isLoading: cleaningDLQ }] = useCleanupDLQMutation();

  const handleRefreshAll = () => {
    refetchHealth();
    refetchEmail();
    refetchPerformance();
    refetchTimeout();
    refetchDLQ();
    toast.success("All monitoring data refreshed");
  };

  const handleProcessDLQ = async () => {
    try {
      const result = await processDLQ().unwrap();
      toast.success(
        `DLQ processing completed. Processed ${result.data.processed} entries.`
      );
    } catch (error) {
      toast.error("Failed to process DLQ entries");
    }
  };

  const handleCleanupDLQ = async () => {
    try {
      const result = await cleanupDLQ({ olderThanDays: 30 }).unwrap();
      toast.success(
        `DLQ cleanup completed. Cleaned ${result.data.cleaned} entries.`
      );
    } catch (error) {
      toast.error("Failed to cleanup DLQ entries");
    }
  };

  const getOverallStatus = () => {
    if (!healthData?.data) return "unknown";
    return healthData.data.overall.status;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy":
        return "text-green-600 bg-green-50 border-green-200";
      case "warning":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "critical":
        return "text-red-600 bg-red-50 border-red-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "healthy":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "warning":
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case "critical":
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default:
        return <Activity className="h-5 w-5 text-gray-600" />;
    }
  };

  const overallStatus = getOverallStatus();

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h1 className="text-3xl font-bold">Technical Monitoring Dashboard</h1>
          <div
            className={`flex items-center gap-2 px-3 py-1 rounded-lg border ${getStatusColor(
              overallStatus
            )}`}
          >
            {getStatusIcon(overallStatus)}
            <Badge
              variant={overallStatus === "healthy" ? "default" : "destructive"}
            >
              System {overallStatus.toUpperCase()}
            </Badge>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <Clock className="h-4 w-4 mr-1" />
            Auto-refresh: {autoRefresh ? "ON" : "OFF"}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshAll}
            disabled={healthLoading}
          >
            <RefreshCw
              className={`h-4 w-4 mr-1 ${healthLoading ? "animate-spin" : ""}`}
            />
            Refresh All
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-1" />
            Export Report
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-1" />
            Settings
          </Button>
        </div>
      </div>

      {/* System Health Overview */}
      {healthData?.data && (
        <SystemHealthCards data={healthData.data} isLoading={healthLoading} />
      )}

      {/* Main Dashboard Tabs */}
      <Tabs defaultValue="realtime" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="realtime">Real-time Metrics</TabsTrigger>
          <TabsTrigger value="performance">Performance Analytics</TabsTrigger>
          <TabsTrigger value="email-dlq">Email & DLQ Management</TabsTrigger>
          <TabsTrigger value="system-logs">System Logs</TabsTrigger>
        </TabsList>

        {/* Real-time Metrics Tab */}
        <TabsContent value="realtime" className="space-y-6">
          <RealTimeMetrics isLoading={healthLoading} />
        </TabsContent>

        {/* Performance Analytics Tab */}
        <TabsContent value="performance" className="space-y-6">
          {performanceData?.data && timeoutData?.data && (
            <PerformanceCharts
              performanceData={performanceData.data}
              timeoutData={timeoutData.data}
              isLoading={performanceLoading || timeoutLoading}
            />
          )}
        </TabsContent>

        {/* Email & DLQ Management Tab */}
        <TabsContent value="email-dlq" className="space-y-6">
          {emailData?.data && dlqData?.data && (
            <EmailDLQCharts
              emailData={emailData.data}
              dlqData={dlqData.data}
              isLoading={emailLoading || dlqLoading}
              onProcessDLQ={handleProcessDLQ}
              onCleanupDLQ={handleCleanupDLQ}
            />
          )}
        </TabsContent>

        {/* System Logs Tab */}
        <TabsContent value="system-logs" className="space-y-6">
          <Card className="marketplace-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-blue-500" />
                System Logs & Events
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">
                  System Logs Coming Soon
                </h3>
                <p className="text-sm">
                  Real-time system logs, error tracking, and event monitoring
                  will be available in the next update.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Actions Footer */}
      <Card className="marketplace-card">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium">Quick Actions:</span>
              <Button
                size="sm"
                variant="outline"
                onClick={handleProcessDLQ}
                disabled={processingDLQ}
              >
                <RefreshCw
                  className={`h-4 w-4 mr-1 ${
                    processingDLQ ? "animate-spin" : ""
                  }`}
                />
                Process DLQ
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleCleanupDLQ}
                disabled={cleaningDLQ}
              >
                <RefreshCw
                  className={`h-4 w-4 mr-1 ${
                    cleaningDLQ ? "animate-spin" : ""
                  }`}
                />
                Cleanup Old Entries
              </Button>
            </div>
            <div className="text-sm text-gray-600">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
