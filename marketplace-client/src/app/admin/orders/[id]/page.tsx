"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/contexts/AuthContext";
import { useParams, useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Truck,
  XCircle,
  Package,
  User,
  MapPin,
  CreditCard,
  ShoppingCart,
} from "lucide-react";
import Link from "next/link";
import { formatPrice } from "@/lib/utils/formatPrice";

interface OrderDetail {
  id: string;
  buyer: {
    id: string;
    name: string;
    email: string;
  };
  items: Array<{
    id: string;
    product: {
      id: string;
      title: string;
      price: number;
      images: string[];
      seller: {
        id: string;
        name: string;
      };
    };
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    status: string;
  }>;
  total: number;
  status: "pending" | "paid" | "shipped" | "fulfilled" | "cancelled";
  createdAt: string;
  updatedAt: string;
  shippingAddress?: {
    fullName: string;
    phone: string;
    street: string;
    city: string;
    county: string;
    postalCode: string;
    country: string;
  };
  paymentInfo?: {
    method: string;
    status: string;
    transactionId?: string;
  };
}

export default function AdminOrderDetailPage() {
  const { isAuthenticated, token } = useAuth();
  const params = useParams();
  const router = useRouter();
  const orderId = params.id as string;

  const [order, setOrder] = useState<OrderDetail | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  useEffect(() => {
    if (isAuthenticated && token && orderId) {
      fetchOrderDetail();
    }
  }, [isAuthenticated, token, orderId]);

  const fetchOrderDetail = async () => {
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/orders/${orderId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch order details");
      }

      const data = await response.json();
      setOrder(data.order);
    } catch (error) {
      console.error("Error fetching order details:", error);
      setError("Failed to load order details");
    } finally {
      setIsLoading(false);
    }
  };

  const updateOrderStatus = async (newStatus: string) => {
    if (!order) return;

    try {
      setIsUpdatingStatus(true);
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(
        `${API_BASE}/admin/orders/${order.id}/status`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ status: newStatus }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update order status");
      }

      setOrder((prev) => (prev ? { ...prev, status: newStatus as any } : null));
    } catch (error) {
      console.error("Error updating order status:", error);
      alert("Failed to update order status");
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "fulfilled":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "paid":
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case "shipped":
        return <Truck className="h-5 w-5 text-blue-500" />;
      case "cancelled":
        return <XCircle className="h-5 w-5 text-red-500" />;
      case "pending":
        return <Clock className="h-5 w-5 text-orange-500" />;
      default:
        return <Package className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "fulfilled":
        return "text-green-600 bg-green-100";
      case "paid":
        return "text-yellow-600 bg-yellow-100";
      case "shipped":
        return "text-blue-600 bg-blue-100";
      case "cancelled":
        return "text-red-600 bg-red-100";
      case "pending":
        return "text-orange-600 bg-orange-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  if (isLoading) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <AlertTriangle className="mx-auto h-16 w-16 text-red-500 mb-6" />
            <h2 className="text-2xl marketplace-heading mb-4">
              Error Loading Order
            </h2>
            <p className="marketplace-text-muted mb-8">
              {error || "Order not found"}
            </p>
            <div className="flex gap-4 justify-center">
              <Button
                onClick={fetchOrderDetail}
                className="marketplace-btn-primary"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
              <Button
                asChild
                variant="outline"
                className="marketplace-btn-outline"
              >
                <Link href="/admin/orders">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Orders
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="marketplace-theme min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl marketplace-heading mb-2">
              Order{" "}
              <span className="marketplace-heading-accent">
                #{order.id.slice(-8).toUpperCase()}
              </span>
            </h1>
            <p className="marketplace-text-muted">
              Order details and management
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              asChild
              variant="outline"
              className="marketplace-btn-outline"
            >
              <Link href="/admin/orders">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Orders
              </Link>
            </Button>
            <Button
              onClick={fetchOrderDetail}
              variant="outline"
              className="marketplace-btn-outline"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Order Status and Actions */}
        <Card className="marketplace-card mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <CardTitle>Order Status</CardTitle>
                <Badge className={getStatusColor(order.status)}>
                  <span className="flex items-center gap-1">
                    {getStatusIcon(order.status)}
                    {order.status.charAt(0).toUpperCase() +
                      order.status.slice(1)}
                  </span>
                </Badge>
              </div>
              <div className="flex gap-2">
                {order.status === "pending" && (
                  <Button
                    onClick={() => updateOrderStatus("paid")}
                    disabled={isUpdatingStatus}
                    className="text-green-600 hover:text-green-700"
                    variant="outline"
                  >
                    {isUpdatingStatus ? (
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <CheckCircle className="mr-2 h-4 w-4" />
                    )}
                    Mark as Paid
                  </Button>
                )}
                {order.status === "paid" && (
                  <Button
                    onClick={() => updateOrderStatus("shipped")}
                    disabled={isUpdatingStatus}
                    className="text-blue-600 hover:text-blue-700"
                    variant="outline"
                  >
                    {isUpdatingStatus ? (
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Truck className="mr-2 h-4 w-4" />
                    )}
                    Mark as Shipped
                  </Button>
                )}
                {order.status === "shipped" && (
                  <Button
                    onClick={() => updateOrderStatus("fulfilled")}
                    disabled={isUpdatingStatus}
                    className="text-green-600 hover:text-green-700"
                    variant="outline"
                  >
                    {isUpdatingStatus ? (
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <CheckCircle className="mr-2 h-4 w-4" />
                    )}
                    Mark as Fulfilled
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="marketplace-text-muted">Order Date:</span>
                <p className="font-medium">
                  {new Date(order.createdAt).toLocaleDateString()}
                </p>
              </div>
              <div>
                <span className="marketplace-text-muted">Last Updated:</span>
                <p className="font-medium">
                  {new Date(order.updatedAt).toLocaleDateString()}
                </p>
              </div>
              <div>
                <span className="marketplace-text-muted">Total Amount:</span>
                <p className="font-bold marketplace-text-warm text-lg">
                  {formatPrice(order.total)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Customer Information */}
          <Card className="marketplace-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <span className="marketplace-text-muted">Name:</span>
                <p className="font-medium">{order.buyer.name}</p>
              </div>
              <div>
                <span className="marketplace-text-muted">Email:</span>
                <p className="font-medium">{order.buyer.email}</p>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Address */}
          {order.shippingAddress && (
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Shipping Address
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <p className="font-medium">{order.shippingAddress.fullName}</p>
                <p>{order.shippingAddress.street}</p>
                <p>
                  {order.shippingAddress.city}, {order.shippingAddress.county}
                </p>
                <p>{order.shippingAddress.postalCode}</p>
                <p>{order.shippingAddress.country}</p>
                <p className="marketplace-text-muted">
                  Phone: {order.shippingAddress.phone}
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Order Items */}
        <Card className="marketplace-card mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Order Items ({order.items.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {order.items.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center gap-4 p-4 border rounded-lg"
                >
                  <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                    {item.product.images && item.product.images.length > 0 ? (
                      <img
                        src={item.product.images[0]}
                        alt={item.product.title}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <Package className="h-8 w-8 text-gray-400" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold marketplace-text-primary">
                      {item.product.title}
                    </h3>
                    <p className="text-sm marketplace-text-muted">
                      Sold by: {item.product.seller.name}
                    </p>
                    <div className="flex items-center gap-4 mt-2">
                      <span className="text-sm marketplace-text-muted">
                        Quantity: {item.quantity}
                      </span>
                      <span className="text-sm marketplace-text-muted">
                        Unit Price: {formatPrice(item.unitPrice)}
                      </span>
                      <span className="font-semibold marketplace-text-warm">
                        Total: {formatPrice(item.totalPrice)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Order Summary */}
            <div className="border-t pt-4 mt-6">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold marketplace-text-primary">
                  Order Total:
                </span>
                <span className="text-xl font-bold marketplace-text-warm">
                  {formatPrice(order.total)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Information */}
        {order.paymentInfo && (
          <Card className="marketplace-card mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Payment Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <span className="marketplace-text-muted">Payment Method:</span>
                <p className="font-medium">{order.paymentInfo.method}</p>
              </div>
              <div>
                <span className="marketplace-text-muted">Payment Status:</span>
                <Badge className={getStatusColor(order.paymentInfo.status)}>
                  {order.paymentInfo.status}
                </Badge>
              </div>
              {order.paymentInfo.transactionId && (
                <div>
                  <span className="marketplace-text-muted">
                    Transaction ID:
                  </span>
                  <p className="font-mono text-sm">
                    {order.paymentInfo.transactionId}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
