"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/contexts/AuthContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  ShoppingCart,
  Search,
  Filter,
  CheckCircle,
  Clock,
  Truck,
  XCircle,
  Eye,
  Edit,
  Crown,
  Zap,
  Star,
  Package,
  DollarSign,
  RefreshCw,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { getFirstImageUrl } from "@/lib/utils/imageUtils";

interface AdminOrder {
  id: string;
  buyer: {
    id: string;
    name: string;
    email: string;
  };
  items: Array<{
    id: string;
    product: {
      id: string;
      title: string;
      price: number;
      images: string[];
      seller: {
        id: string;
        name: string;
      };
    };
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    status: string;
  }>;
  total: number;
  status: "pending" | "paid" | "shipped" | "fulfilled" | "cancelled";
  createdAt: string;
  shippingAddress?: {
    fullName: string;
    phone: string;
    street: string;
    city: string;
    county: string;
    postalCode: string;
    country: string;
  };
  paymentInfo?: {
    method: string;
    status: string;
    transactionId?: string;
  };
}

export default function AdminOrders() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [orders, setOrders] = useState<AdminOrder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updatingOrderId, setUpdatingOrderId] = useState<string | null>(null);
  const { isAuthenticated, token } = useAuth();

  useEffect(() => {
    if (isAuthenticated && token) {
      fetchAllOrders();
    }
  }, [isAuthenticated, token]);

  const fetchAllOrders = async () => {
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/orders/admin/all`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch orders");
      }

      const data = await response.json();
      setOrders(data.orders || []);
    } catch (error) {
      console.error("Error fetching admin orders:", error);
      setError("Failed to load orders");
    } finally {
      setIsLoading(false);
    }
  };

  const updateOrderStatus = async (orderId: string, newStatus: string) => {
    try {
      setUpdatingOrderId(orderId);
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(
        `${API_BASE}/admin/orders/${orderId}/status`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ status: newStatus }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update order status");
      }

      // Update the order in the local state
      setOrders((prevOrders) =>
        prevOrders.map((order) =>
          order.id === orderId ? { ...order, status: newStatus as any } : order
        )
      );
    } catch (error) {
      console.error("Error updating order status:", error);
      alert("Failed to update order status");
    } finally {
      setUpdatingOrderId(null);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Mock orders data for fallback
  const mockOrders = [
    {
      id: "ORD-001",
      customer: "Amara Okafor",
      email: "<EMAIL>",
      items: 3,
      total: 299.97,
      status: "completed",
      date: "2024-01-15",
      products: ["Kente Smart Fabric", "Ubuntu Watch", "Digital Dashiki"],
    },
    {
      id: "ORD-002",
      customer: "Kwame Asante",
      email: "<EMAIL>",
      items: 1,
      total: 159.99,
      status: "processing",
      date: "2024-01-14",
      products: ["Afro-Tech Headphones"],
    },
    {
      id: "ORD-003",
      customer: "Zara Mwangi",
      email: "<EMAIL>",
      items: 2,
      total: 549.98,
      status: "shipped",
      date: "2024-01-13",
      products: ["Wakanda VR Headset", "Smart Textile"],
    },
    {
      id: "ORD-004",
      customer: "Kofi Mensah",
      email: "<EMAIL>",
      items: 1,
      total: 79.99,
      status: "cancelled",
      date: "2024-01-12",
      products: ["Digital Dashiki"],
    },
    {
      id: "ORD-005",
      customer: "Asha Kone",
      email: "<EMAIL>",
      items: 4,
      total: 899.96,
      status: "pending",
      date: "2024-01-11",
      products: ["Ubuntu Watch", "Kente Fabric", "VR Headset", "Smart Textile"],
    },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case "processing":
        return <Clock className="h-5 w-5 text-yellow-400" />;
      case "shipped":
        return <Truck className="h-5 w-5 text-blue-400" />;
      case "cancelled":
        return <XCircle className="h-5 w-5 text-red-400" />;
      case "pending":
        return <Clock className="h-5 w-5 text-orange-400" />;
      default:
        return <Package className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-400";
      case "processing":
        return "text-yellow-400";
      case "shipped":
        return "text-blue-400";
      case "cancelled":
        return "text-red-400";
      case "pending":
        return "text-orange-400";
      default:
        return "text-gray-400";
    }
  };

  const filteredOrders = orders.filter((order) => {
    const matchesSearch =
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.buyer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.buyer.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter =
      filterStatus === "all" || order.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const totalOrders = orders.length;
  const completedOrders = orders.filter(
    (order) => order.status === "fulfilled"
  ).length;
  const processingOrders = orders.filter(
    (order) => order.status === "paid" || order.status === "shipped"
  ).length;
  const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);

  if (isLoading) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <XCircle className="mx-auto h-16 w-16 text-red-500 mb-6" />
            <h2 className="text-2xl marketplace-heading mb-4">
              Error Loading Orders
            </h2>
            <p className="marketplace-text-muted mb-8">{error}</p>
            <Button
              onClick={fetchAllOrders}
              className="marketplace-btn-primary"
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="marketplace-theme min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl marketplace-heading mb-2">
              Admin <span className="marketplace-heading-accent">Orders</span>
            </h1>
            <p className="marketplace-text-muted">
              Monitor and manage all marketplace orders
            </p>
          </div>
          <Button
            onClick={fetchAllOrders}
            variant="outline"
            className="marketplace-btn-outline"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="marketplace-card">
            <CardContent className="p-6 text-center">
              <ShoppingCart className="h-10 w-10 text-orange-500 mx-auto mb-4" />
              <h3 className="text-2xl font-bold marketplace-text-primary mb-2">
                {totalOrders}
              </h3>
              <p className="marketplace-text-muted">Total Orders</p>
            </CardContent>
          </Card>

          <Card className="marketplace-card">
            <CardContent className="p-6 text-center">
              <CheckCircle className="h-10 w-10 text-green-500 mx-auto mb-4" />
              <h3 className="text-2xl font-bold marketplace-text-primary mb-2">
                {completedOrders}
              </h3>
              <p className="marketplace-text-muted">Fulfilled</p>
            </CardContent>
          </Card>

          <Card className="marketplace-card">
            <CardContent className="p-6 text-center">
              <Clock className="h-10 w-10 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-2xl font-bold marketplace-text-primary mb-2">
                {processingOrders}
              </h3>
              <p className="marketplace-text-muted">Processing</p>
            </CardContent>
          </Card>

          <Card className="marketplace-card">
            <CardContent className="p-6 text-center">
              <DollarSign className="h-10 w-10 text-indigo-500 mx-auto mb-4" />
              <h3 className="text-2xl font-bold marketplace-text-warm mb-2">
                {formatPrice(totalRevenue)}
              </h3>
              <p className="marketplace-text-muted">Total Revenue</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card className="marketplace-card mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Search & Filter Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  placeholder="Search orders, customers, or emails..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="flex gap-2 flex-wrap">
                {[
                  "all",
                  "pending",
                  "paid",
                  "shipped",
                  "fulfilled",
                  "cancelled",
                ].map((status) => (
                  <Button
                    key={status}
                    variant={filterStatus === status ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilterStatus(status)}
                    className={
                      filterStatus === status
                        ? "marketplace-btn-primary"
                        : "marketplace-btn-outline"
                    }
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders Table */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle>Orders ({filteredOrders.length})</CardTitle>
            <CardDescription>
              All marketplace orders and their current status
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredOrders.length === 0 ? (
              <div className="text-center py-12">
                <ShoppingCart className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium marketplace-text-primary mb-2">
                  No orders found
                </h3>
                <p className="marketplace-text-muted">
                  {searchTerm || filterStatus !== "all"
                    ? "Try adjusting your search or filters"
                    : "No orders available"}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-4 px-2 marketplace-text-primary font-semibold">
                        Order ID
                      </th>
                      <th className="text-left py-4 px-2 marketplace-text-primary font-semibold">
                        Customer
                      </th>
                      <th className="text-left py-4 px-2 marketplace-text-primary font-semibold">
                        Items
                      </th>
                      <th className="text-left py-4 px-2 marketplace-text-primary font-semibold">
                        Total
                      </th>
                      <th className="text-left py-4 px-2 marketplace-text-primary font-semibold">
                        Status
                      </th>
                      <th className="text-left py-4 px-2 marketplace-text-primary font-semibold">
                        Date
                      </th>
                      <th className="text-left py-4 px-2 marketplace-text-primary font-semibold">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredOrders.map((order) => (
                      <tr
                        key={order.id}
                        className="border-b hover:bg-gray-50 transition-colors"
                      >
                        <td className="py-4 px-2">
                          <div className="font-mono marketplace-text-warm font-semibold">
                            #{order.id.slice(-8).toUpperCase()}
                          </div>
                        </td>
                        <td className="py-4 px-2">
                          <div>
                            <p className="font-semibold marketplace-text-primary">
                              {order.buyer.name}
                            </p>
                            <p className="text-sm marketplace-text-muted">
                              {order.buyer.email}
                            </p>
                          </div>
                        </td>
                        <td className="py-4 px-2">
                          <div>
                            <p className="marketplace-text-primary font-semibold">
                              {order.items.length} items
                            </p>
                            <p className="text-sm marketplace-text-muted">
                              {order.items[0]?.product.title}
                              {order.items.length > 1 &&
                                ` +${order.items.length - 1} more`}
                            </p>
                          </div>
                        </td>
                        <td className="py-4 px-2 marketplace-text-warm font-bold">
                          {formatPrice(order.total)}
                        </td>
                        <td className="py-4 px-2">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(order.status)}
                            <span
                              className={`text-sm font-medium ${getStatusColor(
                                order.status
                              )}`}
                            >
                              {order.status.charAt(0).toUpperCase() +
                                order.status.slice(1)}
                            </span>
                          </div>
                        </td>
                        <td className="py-4 px-2 marketplace-text-muted">
                          {formatDate(order.createdAt)}
                        </td>
                        <td className="py-4 px-2">
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="marketplace-btn-outline"
                              asChild
                            >
                              <Link href={`/admin/orders/${order.id}`}>
                                <Eye className="h-4 w-4" />
                              </Link>
                            </Button>
                            {order.status === "pending" && (
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-green-600 hover:text-green-700 hover:border-green-600"
                                onClick={() =>
                                  updateOrderStatus(order.id, "paid")
                                }
                                disabled={updatingOrderId === order.id}
                                title="Mark as Paid"
                              >
                                {updatingOrderId === order.id ? (
                                  <RefreshCw className="h-4 w-4 animate-spin" />
                                ) : (
                                  <CheckCircle className="h-4 w-4" />
                                )}
                              </Button>
                            )}
                            {order.status === "paid" && (
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-blue-600 hover:text-blue-700 hover:border-blue-600"
                                onClick={() =>
                                  updateOrderStatus(order.id, "shipped")
                                }
                                disabled={updatingOrderId === order.id}
                                title="Mark as Shipped"
                              >
                                {updatingOrderId === order.id ? (
                                  <RefreshCw className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Truck className="h-4 w-4" />
                                )}
                              </Button>
                            )}
                            {order.status === "shipped" && (
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-green-600 hover:text-green-700 hover:border-green-600"
                                onClick={() =>
                                  updateOrderStatus(order.id, "fulfilled")
                                }
                                disabled={updatingOrderId === order.id}
                                title="Mark as Fulfilled"
                              >
                                {updatingOrderId === order.id ? (
                                  <RefreshCw className="h-4 w-4 animate-spin" />
                                ) : (
                                  <CheckCircle className="h-4 w-4" />
                                )}
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
          <Button className="marketplace-btn-primary">
            <Crown className="mr-2 h-5 w-5" />
            Bulk Actions
          </Button>
          <Button className="marketplace-btn-outline">
            <Package className="mr-2 h-5 w-5" />
            Export Orders
          </Button>
          <Button className="marketplace-btn-outline">
            <RefreshCw className="mr-2 h-5 w-5" />
            Generate Report
          </Button>
        </div>
      </div>
    </div>
  );
}
