"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/contexts/AuthContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Users,
  Search,
  Filter,
  UserPlus,
  MoreHorizontal,
  Crown,
  Store,
  ShoppingBag,
  ArrowLeft,
  <PERSON><PERSON>resh<PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>gle,
  Edit,
  Trash2,
  <PERSON>r<PERSON><PERSON><PERSON>,
  User<PERSON>,
} from "lucide-react";
import Link from "next/link";

interface User {
  id: string;
  name: string;
  email: string;
  role: "buyer" | "seller" | "admin";
  createdAt: string;
  updatedAt: string;
  status: "active" | "inactive" | "suspended";
  orderCount?: number;
  totalSpent?: number;
  totalEarned?: number;
  lastOrderDate?: string;
}

interface UserStats {
  totalUsers: number;
  totalAdmins: number;
  totalSellers: number;
  totalBuyers: number;
  newUsersToday: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
  activeUsers: number;
}

export default function AdminUsersPage() {
  const { isAuthenticated, token } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);
  const [isUpdatingRole, setIsUpdatingRole] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  useEffect(() => {
    if (isAuthenticated && token) {
      fetchUsers();
      fetchUserStats();
    }
  }, [isAuthenticated, token, currentPage, searchTerm, roleFilter]);

  const fetchUsers = async () => {
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "20",
      });

      if (searchTerm) params.append("search", searchTerm);
      if (roleFilter !== "all") params.append("role", roleFilter);

      const response = await fetch(`${API_BASE}/admin/users?${params}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch users");
      }

      const data = await response.json();
      setUsers(data.data.users || []);
      setTotalPages(data.data.totalPages || 1);
    } catch (error) {
      console.error("Error fetching users:", error);
      setError("Failed to load users");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUserStats = async () => {
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/admin/users/stats`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch user stats");
      }

      const data = await response.json();
      setStats(data.data);
    } catch (error) {
      console.error("Error fetching user stats:", error);
    }
  };

  const updateUserRole = async (userId: string, newRole: string) => {
    try {
      setIsUpdatingRole(true);
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/admin/users/${userId}/role`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ role: newRole }),
      });

      if (!response.ok) {
        throw new Error("Failed to update user role");
      }

      // Update the user in the local state
      setUsers((prevUsers) =>
        prevUsers.map((user) =>
          user.id === userId ? { ...user, role: newRole as any } : user
        )
      );

      // Refresh stats
      fetchUserStats();
      setIsRoleDialogOpen(false);
      setSelectedUser(null);
    } catch (error) {
      console.error("Error updating user role:", error);
      alert("Failed to update user role");
    } finally {
      setIsUpdatingRole(false);
    }
  };

  const toggleUserSuspension = async (userId: string, suspend: boolean) => {
    try {
      setIsUpdatingStatus(true);
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/admin/users/${userId}/status`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ suspend }),
      });

      if (!response.ok) {
        throw new Error("Failed to update user status");
      }

      // Update the user in the local state
      setUsers((prevUsers) =>
        prevUsers.map((user) =>
          user.id === userId
            ? {
                ...user,
                status: suspend ? "suspended" : "active",
                name: suspend
                  ? user.name.startsWith("[SUSPENDED]")
                    ? user.name
                    : `[SUSPENDED] ${user.name}`
                  : user.name.replace("[SUSPENDED] ", ""),
              }
            : user
        )
      );

      // Refresh stats
      fetchUserStats();
    } catch (error) {
      console.error("Error updating user status:", error);
      alert("Failed to update user status");
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Mock data for fallback - in real app, this would come from API
  const fallbackUsers = [
    {
      id: "1",
      name: "John Doe",
      email: "<EMAIL>",
      role: "buyer",
      status: "active",
      joinDate: "2024-01-15",
      lastActive: "2024-01-20",
    },
    {
      id: "2",
      name: "Jane Smith",
      email: "<EMAIL>",
      role: "seller",
      status: "active",
      joinDate: "2024-01-10",
      lastActive: "2024-01-19",
    },
    {
      id: "3",
      name: "Admin User",
      email: "<EMAIL>",
      role: "admin",
      status: "active",
      joinDate: "2024-01-01",
      lastActive: "2024-01-20",
    },
    {
      id: "4",
      name: "Bob Johnson",
      email: "<EMAIL>",
      role: "seller",
      status: "suspended",
      joinDate: "2024-01-12",
      lastActive: "2024-01-18",
    },
  ];

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "admin":
        return <Crown className="h-4 w-4" />;
      case "seller":
        return <Store className="h-4 w-4" />;
      case "buyer":
        return <ShoppingBag className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "admin":
        return "text-red-600 bg-red-100";
      case "seller":
        return "text-blue-600 bg-blue-100";
      case "buyer":
        return "text-green-600 bg-green-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-600 bg-green-100";
      case "suspended":
        return "text-red-600 bg-red-100";
      case "pending":
        return "text-yellow-600 bg-yellow-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  // Since we're using backend search, we don't need to filter again
  // But keep this for any client-side filtering if needed
  const filteredUsers = users;

  if (isLoading) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <AlertTriangle className="mx-auto h-16 w-16 text-red-500 mb-6" />
            <h2 className="text-2xl marketplace-heading mb-4">
              Error Loading Users
            </h2>
            <p className="marketplace-text-muted mb-8">{error}</p>
            <Button onClick={fetchUsers} className="marketplace-btn-primary">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="marketplace-theme min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl marketplace-heading mb-2">
              User{" "}
              <span className="marketplace-heading-accent">Management</span>
            </h1>
            <p className="marketplace-text-muted">
              Manage users, roles, and permissions
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              asChild
              variant="outline"
              className="marketplace-btn-outline"
            >
              <Link href="/admin/dashboard">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Dashboard
              </Link>
            </Button>
            <Button
              onClick={fetchUsers}
              variant="outline"
              className="marketplace-btn-outline"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Button className="marketplace-btn-primary">
              <UserPlus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="marketplace-card marketplace-hover-lift">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium marketplace-text-primary">
                Total Users
              </CardTitle>
              <Users className="h-4 w-4 marketplace-text-cool" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold marketplace-text-primary">
                {stats?.totalUsers || users.length}
              </div>
              <p className="text-xs marketplace-text-muted">
                +{stats?.newUsersToday || 0} new today
              </p>
            </CardContent>
          </Card>

          <Card className="marketplace-card marketplace-hover-lift">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium marketplace-text-primary">
                Admins
              </CardTitle>
              <Crown className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {stats?.totalAdmins ||
                  users.filter((u) => u.role === "admin").length}
              </div>
              <p className="text-xs marketplace-text-muted">
                System administrators
              </p>
            </CardContent>
          </Card>

          <Card className="marketplace-card marketplace-hover-lift">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium marketplace-text-primary">
                Sellers
              </CardTitle>
              <Store className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {stats?.totalSellers ||
                  users.filter((u) => u.role === "seller").length}
              </div>
              <p className="text-xs marketplace-text-muted">Product vendors</p>
            </CardContent>
          </Card>

          <Card className="marketplace-card marketplace-hover-lift">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium marketplace-text-primary">
                Buyers
              </CardTitle>
              <ShoppingBag className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {stats?.totalBuyers ||
                  users.filter((u) => u.role === "buyer").length}
              </div>
              <p className="text-xs marketplace-text-muted">Customers</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card className="marketplace-card mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Search & Filter Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search users by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="seller">Seller</SelectItem>
                  <SelectItem value="buyer">Buyer</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Users List */}
        <Card className="marketplace-card">
          <CardHeader>
            <CardTitle>Users ({filteredUsers.length})</CardTitle>
            <CardDescription>
              All registered users in the marketplace
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredUsers.length === 0 ? (
              <div className="text-center py-12">
                <Users className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No users found
                </h3>
                <p className="text-gray-600">
                  {searchTerm || roleFilter !== "all"
                    ? "Try adjusting your search or filters"
                    : "No users available"}
                </p>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Join Date</TableHead>
                      <TableHead>Last Active</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium marketplace-text-primary">
                              {user.name}
                            </div>
                            <div className="text-sm marketplace-text-muted">
                              {user.email}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getRoleColor(user.role)}>
                            <span className="flex items-center">
                              {getRoleIcon(user.role)}
                              <span className="ml-1 capitalize">
                                {user.role}
                              </span>
                            </span>
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(user.status)}>
                            {user.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="marketplace-text-muted">
                          {new Date(user.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="marketplace-text-muted">
                          {new Date(user.updatedAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedUser(user);
                                  setIsRoleDialogOpen(true);
                                }}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Change Role
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <UserCheck className="mr-2 h-4 w-4" />
                                View Profile
                              </DropdownMenuItem>
                              {user.status === "suspended" ? (
                                <DropdownMenuItem
                                  className="text-green-600"
                                  onClick={() =>
                                    toggleUserSuspension(user.id, false)
                                  }
                                  disabled={isUpdatingStatus}
                                >
                                  <UserCheck className="mr-2 h-4 w-4" />
                                  Unsuspend User
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={() =>
                                    toggleUserSuspension(user.id, true)
                                  }
                                  disabled={isUpdatingStatus}
                                >
                                  <UserX className="mr-2 h-4 w-4" />
                                  Suspend User
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Role Change Dialog */}
        <Dialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Change User Role</DialogTitle>
              <DialogDescription>
                Update the role for {selectedUser?.name} ({selectedUser?.email})
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Select New Role</label>
                <div className="grid grid-cols-3 gap-2">
                  {["buyer", "seller", "admin"].map((role) => (
                    <Button
                      key={role}
                      variant={
                        selectedUser?.role === role ? "default" : "outline"
                      }
                      onClick={() => {
                        if (selectedUser) {
                          updateUserRole(selectedUser.id, role);
                        }
                      }}
                      disabled={isUpdatingRole}
                      className="flex items-center gap-2"
                    >
                      {getRoleIcon(role)}
                      <span className="capitalize">{role}</span>
                    </Button>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsRoleDialogOpen(false)}
                disabled={isUpdatingRole}
              >
                Cancel
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
