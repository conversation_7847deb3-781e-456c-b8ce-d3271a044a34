"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { 
  MessageSquare, 
  Search, 
  Send,
  Reply,
  Archive,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  Crown,
  Zap,
  Eye,
  Trash2,
  ArrowLeft,
  RefreshCw,
  Filter
} from "lucide-react";

export default function AdminMessages() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");

  // Mock messages data
  const messages = [
    {
      id: 1,
      from: "Amara Okafor",
      email: "<EMAIL>",
      subject: "Issue with Product Order",
      preview: "Hi, I received my order but there seems to be an issue with the product...",
      type: "support",
      priority: "high",
      status: "unread",
      date: "2024-01-15 14:30",
      avatar: "AO"
    },
    {
      id: 2,
      from: "<PERSON>wame Asante", 
      email: "<EMAIL>",
      subject: "Partnership Opportunity",
      preview: "Hello, I represent a company interested in collaborating...",
      type: "business",
      priority: "medium",
      status: "read",
      date: "2024-01-14 09:15",
      avatar: "KA"
    },
    {
      id: 3,
      from: "Fatima Hassan",
      email: "<EMAIL>", 
      subject: "Seller Account Verification",
      preview: "I submitted my documents for verification but haven't heard back...",
      type: "verification",
      priority: "medium",
      status: "unread",
      date: "2024-01-13 16:45",
      avatar: "FH"
    },
    {
      id: 4,
      from: "John Smith",
      email: "<EMAIL>",
      subject: "Payment Processing Issue",
      preview: "My payment was deducted but the order status shows failed...",
      type: "payment",
      priority: "high", 
      status: "read",
      date: "2024-01-12 11:20",
      avatar: "JS"
    },
    {
      id: 5,
      from: "Sarah Johnson",
      email: "<EMAIL>",
      subject: "Feature Request",
      preview: "Would love to see bulk upload functionality for sellers...",
      type: "feature",
      priority: "low",
      status: "read",
      date: "2024-01-11 08:30",
      avatar: "SJ"
    }
  ];

  const messageStats = [
    { label: "Total Messages", value: "1,247", icon: MessageSquare },
    { label: "Unread", value: "23", icon: AlertCircle },
    { label: "Urgent", value: "5", icon: Clock },
    { label: "Resolved Today", value: "18", icon: CheckCircle }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-600 bg-red-100";
      case "medium":
        return "text-yellow-600 bg-yellow-100";
      case "low":
        return "text-green-600 bg-green-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "support":
        return "text-blue-600 bg-blue-100";
      case "business":
        return "text-purple-600 bg-purple-100";
      case "verification":
        return "text-orange-600 bg-orange-100";
      case "payment":
        return "text-red-600 bg-red-100";
      case "feature":
        return "text-green-600 bg-green-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const filteredMessages = messages.filter(message => {
    const matchesSearch = message.from.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.preview.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterType === "all" || 
                         (filterType === "unread" && message.status === "unread") ||
                         (filterType === "urgent" && message.priority === "high") ||
                         message.type === filterType;
    
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="marketplace-theme min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl marketplace-heading mb-2">
              <MessageSquare className="inline mr-3 h-8 w-8 marketplace-text-warm" />
              Messages & <span className="marketplace-heading-accent">Support</span>
            </h1>
            <p className="marketplace-text-muted">
              Manage customer communications and support requests
            </p>
          </div>
          <div className="flex gap-3">
            <Button asChild variant="outline" className="marketplace-btn-outline">
              <Link href="/admin/dashboard">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Dashboard
              </Link>
            </Button>
            <Button variant="outline" className="marketplace-btn-outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {messageStats.map((stat, index) => (
            <Card key={index} className="marketplace-card marketplace-hover-lift">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium marketplace-text-muted">
                  {stat.label}
                </CardTitle>
                <stat.icon className="h-4 w-4 marketplace-text-warm" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold marketplace-text-primary">
                  {stat.value}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Search and Filter */}
        <Card className="marketplace-card mb-8">
          <CardHeader>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 marketplace-text-muted" />
                  <Input
                    placeholder="Search messages..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 marketplace-input"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                {["all", "unread", "urgent", "support", "business"].map((filter) => (
                  <Button
                    key={filter}
                    variant={filterType === filter ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilterType(filter)}
                    className={
                      filterType === filter
                        ? "marketplace-btn-primary"
                        : "marketplace-btn-outline"
                    }
                  >
                    {filter.charAt(0).toUpperCase() + filter.slice(1)}
                  </Button>
                ))}
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Messages List */}
        <div className="space-y-4">
          {filteredMessages.map((message) => (
            <Card key={message.id} className={`marketplace-card marketplace-hover-lift cursor-pointer transition-all ${
              message.status === "unread" ? "border-l-4 border-l-orange-500" : ""
            }`}>
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  {/* Avatar */}
                  <div className="w-12 h-12 rounded-full marketplace-bg-accent flex items-center justify-center">
                    <span className="text-sm font-semibold marketplace-text-warm">
                      {message.avatar}
                    </span>
                  </div>
                  
                  {/* Message Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-3">
                        <h3 className="font-semibold marketplace-text-primary">
                          {message.from}
                        </h3>
                        <Badge className={getPriorityColor(message.priority)}>
                          {message.priority}
                        </Badge>
                        <Badge className={getTypeColor(message.type)}>
                          {message.type}
                        </Badge>
                        {message.status === "unread" && (
                          <Badge className="text-orange-600 bg-orange-100">
                            New
                          </Badge>
                        )}
                      </div>
                      <span className="text-sm marketplace-text-muted">
                        {message.date}
                      </span>
                    </div>
                    
                    <h4 className="font-medium marketplace-text-primary mb-2">
                      {message.subject}
                    </h4>
                    
                    <p className="marketplace-text-muted text-sm mb-4 line-clamp-2">
                      {message.preview}
                    </p>
                    
                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button size="sm" className="marketplace-btn-primary">
                        <Reply className="mr-2 h-4 w-4" />
                        Reply
                      </Button>
                      <Button size="sm" variant="outline" className="marketplace-btn-outline">
                        <Eye className="mr-2 h-4 w-4" />
                        View
                      </Button>
                      <Button size="sm" variant="outline" className="marketplace-btn-outline">
                        <Archive className="mr-2 h-4 w-4" />
                        Archive
                      </Button>
                      <Button size="sm" variant="outline" className="marketplace-btn-outline">
                        <Star className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
          <Card className="marketplace-card">
            <CardHeader>
              <div className="flex items-center gap-3">
                <Send className="h-6 w-6 marketplace-text-warm" />
                <CardTitle className="marketplace-text-primary">Compose Message</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="marketplace-text-muted mb-6">
                Send announcements or notifications to users.
              </p>
              <Button className="marketplace-btn-primary w-full">
                <Crown className="mr-2 h-5 w-5" />
                New Message
              </Button>
            </CardContent>
          </Card>

          <Card className="marketplace-card">
            <CardHeader>
              <div className="flex items-center gap-3">
                <Zap className="h-6 w-6 marketplace-text-warm" />
                <CardTitle className="marketplace-text-primary">Auto Responses</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="marketplace-text-muted mb-6">
                Manage automated response templates and settings.
              </p>
              <Button className="marketplace-btn-outline w-full">
                <Filter className="mr-2 h-5 w-5" />
                Manage Templates
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
