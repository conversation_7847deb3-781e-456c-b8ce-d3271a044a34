"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  MessageSquare, 
  Search, 
  Send,
  Reply,
  Archive,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  Crown,
  Zap,
  Eye,
  Trash2
} from "lucide-react";

export default function AdminMessages() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");

  // Mock messages data
  const messages = [
    {
      id: 1,
      from: "Amara Okafor",
      email: "<EMAIL>",
      subject: "Issue with Kente Smart Fabric Order",
      preview: "Hi, I received my order but the fabric seems to have a connectivity issue...",
      type: "support",
      priority: "high",
      status: "unread",
      date: "2024-01-15 14:30",
      avatar: "AO"
    },
    {
      id: 2,
      from: "<PERSON><PERSON><PERSON>", 
      email: "<EMAIL>",
      subject: "Partnership Opportunity",
      preview: "Hello, I represent a tech company interested in collaborating...",
      type: "business",
      priority: "medium",
      status: "read",
      date: "2024-01-14 09:15",
      avatar: "KA"
    },
    {
      id: 3,
      from: "Zara Mwangi",
      email: "<EMAIL>",
      subject: "Product Feedback - Ubuntu Watch",
      preview: "The Ubuntu Watch is amazing! I wanted to share some feedback...",
      type: "feedback",
      priority: "low",
      status: "replied",
      date: "2024-01-13 16:45",
      avatar: "ZM"
    },
    {
      id: 4,
      from: "Kofi Mensah",
      email: "<EMAIL>", 
      subject: "Refund Request",
      preview: "I need to request a refund for my recent purchase due to...",
      type: "support",
      priority: "high",
      status: "unread",
      date: "2024-01-12 11:20",
      avatar: "KM"
    },
    {
      id: 5,
      from: "Asha Kone",
      email: "<EMAIL>",
      subject: "Wholesale Inquiry",
      preview: "I'm interested in purchasing your products in bulk for my store...",
      type: "business",
      priority: "medium",
      status: "archived",
      date: "2024-01-11 13:10",
      avatar: "AK"
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "unread":
        return <AlertCircle className="h-5 w-5 text-yellow-400" />;
      case "read":
        return <Eye className="h-5 w-5 text-blue-400" />;
      case "replied":
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case "archived":
        return <Archive className="h-5 w-5 text-gray-400" />;
      default:
        return <MessageSquare className="h-5 w-5 text-gray-400" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "border-l-red-400 bg-red-900/10";
      case "medium":
        return "border-l-yellow-400 bg-yellow-900/10";
      case "low":
        return "border-l-green-400 bg-green-900/10";
      default:
        return "border-l-gray-400 bg-gray-900/10";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "support":
        return "text-red-400";
      case "business":
        return "text-purple-400";
      case "feedback":
        return "text-green-400";
      default:
        return "text-gray-400";
    }
  };

  const filteredMessages = messages.filter(message => {
    const matchesSearch = message.from.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.preview.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === "all" || message.type === filterType || message.status === filterType;
    return matchesSearch && matchesFilter;
  });

  const totalMessages = messages.length;
  const unreadMessages = messages.filter(msg => msg.status === "unread").length;
  const highPriorityMessages = messages.filter(msg => msg.priority === "high").length;

  return (
    <div className="afro-theme min-h-screen p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="afro-heading">Message Center</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Connect with your <span className="afro-text-neon">digital community</span> across the marketplace
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="afro-card-tech p-6 text-center">
            <MessageSquare className="h-10 w-10 afro-text-neon mx-auto mb-4" />
            <h3 className="text-2xl font-bold afro-text-warm mb-2">{totalMessages}</h3>
            <p className="text-gray-400">Total Messages</p>
          </div>
          
          <div className="afro-card-tech p-6 text-center">
            <AlertCircle className="h-10 w-10 text-yellow-400 mx-auto mb-4" />
            <h3 className="text-2xl font-bold afro-text-warm mb-2">{unreadMessages}</h3>
            <p className="text-gray-400">Unread</p>
          </div>
          
          <div className="afro-card-tech p-6 text-center">
            <Star className="h-10 w-10 text-red-400 mx-auto mb-4" />
            <h3 className="text-2xl font-bold afro-text-warm mb-2">{highPriorityMessages}</h3>
            <p className="text-gray-400">High Priority</p>
          </div>
          
          <div className="afro-card-tech p-6 text-center">
            <Clock className="h-10 w-10 text-purple-400 mx-auto mb-4" />
            <h3 className="text-2xl font-bold afro-text-warm mb-2">2.4h</h3>
            <p className="text-gray-400">Avg Response</p>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="afro-card p-6">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                placeholder="Search messages, senders, or subjects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-yellow-400"
              />
            </div>
            
            <div className="flex gap-2">
              {["all", "unread", "support", "business", "feedback"].map((filter) => (
                <Button
                  key={filter}
                  variant={filterType === filter ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilterType(filter)}
                  className={filterType === filter ? "afro-btn-primary" : "border-gray-600 text-gray-300 hover:border-yellow-400"}
                >
                  {filter.charAt(0).toUpperCase() + filter.slice(1)}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Messages List */}
        <div className="space-y-4">
          {filteredMessages.map((message) => (
            <div key={message.id} className={`afro-card p-6 border-l-4 ${getPriorityColor(message.priority)} hover:scale-[1.02] transition-all duration-300`}>
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4 flex-1">
                  {/* Avatar */}
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 flex items-center justify-center text-black font-bold">
                    {message.avatar}
                  </div>
                  
                  {/* Message Content */}
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold afro-text-warm">{message.from}</h3>
                      <span className="text-sm text-gray-400">{message.email}</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${getTypeColor(message.type)} bg-gray-800`}>
                        {message.type}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        message.priority === "high" ? "text-red-400 bg-red-900/20" :
                        message.priority === "medium" ? "text-yellow-400 bg-yellow-900/20" :
                        "text-green-400 bg-green-900/20"
                      }`}>
                        {message.priority}
                      </span>
                    </div>
                    
                    <h4 className="text-lg font-medium text-white mb-2">{message.subject}</h4>
                    <p className="text-gray-300 mb-3">{message.preview}</p>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-400">{message.date}</span>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(message.status)}
                        <span className="text-sm text-gray-400 capitalize">{message.status}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Actions */}
                <div className="flex gap-2 ml-4">
                  <Button size="sm" variant="outline" className="border-gray-600 text-gray-300 hover:border-blue-400 hover:text-blue-400">
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline" className="border-gray-600 text-gray-300 hover:border-green-400 hover:text-green-400">
                    <Reply className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline" className="border-gray-600 text-gray-300 hover:border-yellow-400 hover:text-yellow-400">
                    <Archive className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline" className="border-gray-600 text-gray-300 hover:border-red-400 hover:text-red-400">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button className="afro-btn-primary">
            <Crown className="mr-2 h-5 w-5" />
            Compose Message
          </Button>
          <Button className="afro-btn-tech">
            <Zap className="mr-2 h-5 w-5" />
            Bulk Actions
          </Button>
          <Button className="afro-btn-tech">
            <Star className="mr-2 h-5 w-5" />
            Message Templates
          </Button>
        </div>
      </div>
    </div>
  );
}
