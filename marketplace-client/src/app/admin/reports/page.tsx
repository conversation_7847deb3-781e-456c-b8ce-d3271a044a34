"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import {
  FileText,
  Download,
  Calendar,
  TrendingUp,
  Users,
  ShoppingBag,
  DollarSign,
  BarChart3,
  PieChart,
  Activity,
  Crown,
  Zap,
  ArrowLeft,
  RefreshCw,
} from "lucide-react";

export default function AdminReports() {
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");

  const reportTypes = [
    {
      title: "Revenue Analytics",
      description: "Comprehensive financial performance analysis",
      icon: DollarSign,
      lastGenerated: "2 hours ago",
      size: "2.4 MB"
    },
    {
      title: "User Engagement",
      description: "Customer behavior and interaction patterns",
      icon: Users,
      lastGenerated: "4 hours ago",
      size: "1.8 MB"
    },
    {
      title: "Sales Performance",
      description: "Product sales trends and market analysis",
      icon: TrendingUp,
      lastGenerated: "6 hours ago",
      size: "3.1 MB"
    },
    {
      title: "Inventory Status",
      description: "Stock levels and product availability",
      icon: ShoppingBag,
      lastGenerated: "1 day ago",
      size: "1.2 MB"
    },
    {
      title: "Market Insights",
      description: "Competitive analysis and market trends",
      icon: BarChart3,
      lastGenerated: "2 days ago",
      size: "4.7 MB"
    },
    {
      title: "Order Analytics",
      description: "Order patterns and fulfillment metrics",
      icon: Activity,
      lastGenerated: "3 days ago",
      size: "2.9 MB"
    }
  ];

  const quickStats = [
    {
      label: "Reports Generated",
      value: "1,247",
      icon: FileText,
    },
    {
      label: "Data Points",
      value: "2.4M",
      icon: Activity,
    },
    {
      label: "Insights Created",
      value: "89",
      icon: Crown,
    },
    {
      label: "Automated Reports",
      value: "24",
      icon: Zap,
    },
  ];

  return (
    <div className="marketplace-theme min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl marketplace-heading mb-2">
              <FileText className="inline mr-3 h-8 w-8 marketplace-text-warm" />
              Reports & <span className="marketplace-heading-accent">Analytics</span>
            </h1>
            <p className="marketplace-text-muted">
              Generate comprehensive reports and insights
            </p>
          </div>
          <div className="flex gap-3">
            <Button asChild variant="outline" className="marketplace-btn-outline">
              <Link href="/admin/dashboard">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Dashboard
              </Link>
            </Button>
            <Button variant="outline" className="marketplace-btn-outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat, index) => (
            <Card key={index} className="marketplace-card marketplace-hover-lift">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium marketplace-text-muted">
                  {stat.label}
                </CardTitle>
                <stat.icon className="h-4 w-4 marketplace-text-warm" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold marketplace-text-primary">
                  {stat.value}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Period Selection */}
        <Card className="marketplace-card mb-8">
          <CardHeader>
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <div className="flex items-center gap-3">
                <Calendar className="h-6 w-6 marketplace-text-warm" />
                <CardTitle className="marketplace-text-primary">Report Period</CardTitle>
              </div>
              <div className="flex gap-2">
                {["weekly", "monthly", "quarterly", "yearly"].map((period) => (
                  <Button
                    key={period}
                    variant={selectedPeriod === period ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedPeriod(period)}
                    className={
                      selectedPeriod === period
                        ? "marketplace-btn-primary"
                        : "marketplace-btn-outline"
                    }
                  >
                    {period.charAt(0).toUpperCase() + period.slice(1)}
                  </Button>
                ))}
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Reports Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {reportTypes.map((report, index) => (
            <Card key={index} className="marketplace-card marketplace-hover-lift">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg marketplace-bg-accent">
                    <report.icon className="h-6 w-6 marketplace-text-warm" />
                  </div>
                  <div>
                    <CardTitle className="marketplace-text-primary">{report.title}</CardTitle>
                    <CardDescription className="marketplace-text-muted">
                      {report.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="marketplace-text-muted">Last Generated:</span>
                    <span className="marketplace-text-warm">{report.lastGenerated}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="marketplace-text-muted">File Size:</span>
                    <span className="marketplace-text-primary">{report.size}</span>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" className="flex-1 marketplace-btn-primary">
                    <FileText className="mr-2 h-4 w-4" />
                    Generate
                  </Button>
                  <Button size="sm" variant="outline" className="marketplace-btn-outline">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Advanced Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card className="marketplace-card">
            <CardHeader>
              <div className="flex items-center gap-3">
                <PieChart className="h-6 w-6 marketplace-text-warm" />
                <CardTitle className="marketplace-text-primary">Custom Analytics</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="marketplace-text-muted mb-6">
                Create personalized reports with advanced filtering and custom metrics.
              </p>
              <Button className="marketplace-btn-primary w-full">
                <Crown className="mr-2 h-5 w-5" />
                Build Custom Report
              </Button>
            </CardContent>
          </Card>

          <Card className="marketplace-card">
            <CardHeader>
              <div className="flex items-center gap-3">
                <Zap className="h-6 w-6 marketplace-text-warm" />
                <CardTitle className="marketplace-text-primary">AI Insights</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="marketplace-text-muted mb-6">
                Let our AI analyze patterns and provide intelligent recommendations.
              </p>
              <Button className="marketplace-btn-outline w-full">
                <Activity className="mr-2 h-5 w-5" />
                Generate AI Report
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Scheduled Reports */}
        <Card className="marketplace-card">
          <CardHeader>
            <div className="flex items-center gap-3">
              <Calendar className="h-6 w-6 marketplace-text-warm" />
              <CardTitle className="marketplace-text-primary">Scheduled Reports</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 marketplace-bg-muted rounded-lg border marketplace-border">
                <h4 className="font-semibold marketplace-text-primary mb-2">Daily Sales Summary</h4>
                <p className="text-sm marketplace-text-muted mb-3">Automated daily at 9:00 AM</p>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-600">Active</span>
                </div>
              </div>
              
              <div className="p-4 marketplace-bg-muted rounded-lg border marketplace-border">
                <h4 className="font-semibold marketplace-text-primary mb-2">Weekly Performance</h4>
                <p className="text-sm marketplace-text-muted mb-3">Every Monday at 8:00 AM</p>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-600">Active</span>
                </div>
              </div>
              
              <div className="p-4 marketplace-bg-muted rounded-lg border marketplace-border">
                <h4 className="font-semibold marketplace-text-primary mb-2">Monthly Analytics</h4>
                <p className="text-sm marketplace-text-muted mb-3">First day of each month</p>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm text-yellow-600">Pending</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
