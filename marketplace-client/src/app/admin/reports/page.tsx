"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  FileText, 
  Download, 
  Calendar, 
  TrendingUp,
  Users,
  ShoppingBag,
  DollarSign,
  BarChart3,
  PieChart,
  Activity,
  Crown,
  Zap,
  Globe
} from "lucide-react";

export default function AdminReports() {
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");

  const reportTypes = [
    {
      title: "Revenue Analytics",
      description: "Comprehensive financial performance analysis",
      icon: DollarSign,
      color: "text-yellow-400",
      lastGenerated: "2 hours ago",
      size: "2.4 MB"
    },
    {
      title: "User Engagement",
      description: "Customer behavior and interaction patterns",
      icon: Users,
      color: "text-blue-400",
      lastGenerated: "4 hours ago",
      size: "1.8 MB"
    },
    {
      title: "Sales Performance",
      description: "Product sales trends and market analysis",
      icon: TrendingUp,
      color: "text-green-400",
      lastGenerated: "6 hours ago",
      size: "3.1 MB"
    },
    {
      title: "Inventory Status",
      description: "Stock levels and product availability",
      icon: ShoppingBag,
      color: "text-purple-400",
      lastGenerated: "1 day ago",
      size: "1.2 MB"
    },
    {
      title: "Market Insights",
      description: "Competitive analysis and market trends",
      icon: BarChart3,
      color: "text-orange-400",
      lastGenerated: "2 days ago",
      size: "4.7 MB"
    },
    {
      title: "Global Reach",
      description: "Geographic distribution and expansion metrics",
      icon: Globe,
      color: "text-cyan-400",
      lastGenerated: "3 days ago",
      size: "2.9 MB"
    }
  ];

  const quickStats = [
    { label: "Reports Generated", value: "1,247", icon: FileText, color: "text-blue-400" },
    { label: "Data Points", value: "2.4M", icon: Activity, color: "text-green-400" },
    { label: "Insights Created", value: "89", icon: Crown, color: "text-yellow-400" },
    { label: "Automated Reports", value: "24", icon: Zap, color: "text-purple-400" }
  ];

  return (
    <div className="afro-theme min-h-screen p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="afro-heading">Intelligence Reports</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Transform data into <span className="afro-text-neon">actionable wisdom</span> for your digital empire
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickStats.map((stat, index) => (
            <div key={index} className="afro-card-tech p-6 text-center group hover:scale-105 transition-all duration-300">
              <stat.icon className={`h-10 w-10 ${stat.color} mx-auto mb-4 group-hover:animate-pulse`} />
              <h3 className="text-2xl font-bold afro-text-warm mb-2">{stat.value}</h3>
              <p className="text-gray-400">{stat.label}</p>
            </div>
          ))}
        </div>

        {/* Period Selection */}
        <div className="afro-card p-6">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <Calendar className="h-6 w-6 afro-text-neon" />
              <h3 className="text-xl font-bold afro-text-warm">Report Period</h3>
            </div>
            <div className="flex gap-2">
              {["weekly", "monthly", "quarterly", "yearly"].map((period) => (
                <Button
                  key={period}
                  variant={selectedPeriod === period ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedPeriod(period)}
                  className={selectedPeriod === period ? "afro-btn-primary" : "border-gray-600 text-gray-300 hover:border-yellow-400"}
                >
                  {period.charAt(0).toUpperCase() + period.slice(1)}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Reports Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reportTypes.map((report, index) => (
            <div key={index} className="afro-card p-6 group hover:scale-105 transition-all duration-300">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-3 rounded-full bg-gray-800 border border-gray-600">
                    <report.icon className={`h-6 w-6 ${report.color} group-hover:animate-pulse`} />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold afro-text-warm">{report.title}</h3>
                    <p className="text-sm text-gray-400">{report.description}</p>
                  </div>
                </div>
              </div>

              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Last Generated:</span>
                  <span className="afro-text-neon">{report.lastGenerated}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">File Size:</span>
                  <span className="text-gray-300">{report.size}</span>
                </div>
              </div>

              <div className="flex gap-2">
                <Button size="sm" className="flex-1 afro-btn-tech">
                  <FileText className="mr-2 h-4 w-4" />
                  Generate
                </Button>
                <Button size="sm" variant="outline" className="border-gray-600 text-gray-300 hover:border-green-400 hover:text-green-400">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Advanced Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="afro-card p-8">
            <div className="flex items-center gap-3 mb-6">
              <PieChart className="h-6 w-6 text-purple-400" />
              <h3 className="text-2xl font-bold afro-text-warm">Custom Analytics</h3>
            </div>
            <p className="text-gray-300 mb-6">
              Create personalized reports with advanced filtering and custom metrics.
            </p>
            <Button className="afro-btn-primary w-full">
              <Crown className="mr-2 h-5 w-5" />
              Build Custom Report
            </Button>
          </div>

          <div className="afro-card p-8">
            <div className="flex items-center gap-3 mb-6">
              <Zap className="h-6 w-6 afro-text-neon" />
              <h3 className="text-2xl font-bold afro-text-warm">AI Insights</h3>
            </div>
            <p className="text-gray-300 mb-6">
              Let our AI analyze patterns and provide intelligent recommendations.
            </p>
            <Button className="afro-btn-tech w-full">
              <Activity className="mr-2 h-5 w-5" />
              Generate AI Report
            </Button>
          </div>
        </div>

        {/* Scheduled Reports */}
        <div className="afro-card p-8">
          <div className="flex items-center gap-3 mb-6">
            <Calendar className="h-6 w-6 text-blue-400" />
            <h3 className="text-2xl font-bold afro-text-warm">Scheduled Reports</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-gray-800/50 rounded-lg border border-gray-700">
              <h4 className="font-semibold afro-text-warm mb-2">Daily Sales Summary</h4>
              <p className="text-sm text-gray-400 mb-3">Automated daily at 9:00 AM</p>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-sm text-green-400">Active</span>
              </div>
            </div>
            
            <div className="p-4 bg-gray-800/50 rounded-lg border border-gray-700">
              <h4 className="font-semibold afro-text-warm mb-2">Weekly Performance</h4>
              <p className="text-sm text-gray-400 mb-3">Every Monday at 8:00 AM</p>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-sm text-green-400">Active</span>
              </div>
            </div>
            
            <div className="p-4 bg-gray-800/50 rounded-lg border border-gray-700">
              <h4 className="font-semibold afro-text-warm mb-2">Monthly Analytics</h4>
              <p className="text-sm text-gray-400 mb-3">First day of each month</p>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <span className="text-sm text-yellow-400">Pending</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
