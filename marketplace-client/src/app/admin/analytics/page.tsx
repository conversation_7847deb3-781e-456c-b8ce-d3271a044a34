"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/contexts/AuthContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  BarChart3,
  TrendingUp,
  Users,
  ShoppingCart,
  DollarSign,
  Package,
  Calendar,
  Download,
  RefreshCw,
  AlertTriangle,
  ArrowLeft,
} from "lucide-react";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import { formatPrice } from "@/lib/utils/formatPrice";
import Link from "next/link";

interface DashboardStats {
  totalUsers: number;
  totalProducts: number;
  totalOrders: number;
  totalRevenue: number;
  newUsersToday: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
  ordersToday: number;
  ordersThisWeek: number;
  ordersThisMonth: number;
  revenueToday: number;
  revenueThisWeek: number;
  revenueThisMonth: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  averageOrderValue: number;
}

interface TopProduct {
  id: string;
  title: string;
  totalSales: number;
  totalRevenue: number;
  orderCount: number;
}

interface RevenueData {
  date: string;
  revenue: number;
  orders: number;
}

interface UserGrowthData {
  date: string;
  newUsers: number;
  totalUsers: number;
}

export default function AdminAnalytics() {
  const { isAuthenticated, token } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
  const [userGrowthData, setUserGrowthData] = useState<UserGrowthData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState(30);

  useEffect(() => {
    if (isAuthenticated && token) {
      fetchComprehensiveAnalytics();
    }
  }, [isAuthenticated, token, dateRange]);

  const fetchComprehensiveAnalytics = async () => {
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(
        `${API_BASE}/analytics/admin/comprehensive?days=${dateRange}&topProductsLimit=10`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch analytics data");
      }

      const data = await response.json();
      setStats(data.data.dashboardStats);
      setTopProducts(data.data.topProducts);
      setRevenueData(data.data.revenueData);
      setUserGrowthData(data.data.userGrowthData);
    } catch (error) {
      console.error("Error fetching analytics:", error);
      setError("Failed to load analytics data");
    } finally {
      setIsLoading(false);
    }
  };

  // Chart colors
  const COLORS = ["#f97316", "#3b82f6", "#10b981", "#f59e0b", "#ef4444"];

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="text-gray-600">{`Date: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {`${entry.dataKey}: ${
                entry.dataKey.includes("revenue") ||
                entry.dataKey.includes("Revenue")
                  ? formatPrice(entry.value)
                  : entry.value.toLocaleString()
              }`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <AlertTriangle className="mx-auto h-16 w-16 text-red-500 mb-6" />
            <h2 className="text-2xl marketplace-heading mb-4">
              Error Loading Analytics
            </h2>
            <p className="marketplace-text-muted mb-8">
              {error || "Failed to load analytics data"}
            </p>
            <Button
              onClick={fetchComprehensiveAnalytics}
              className="marketplace-btn-primary"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="marketplace-theme min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl marketplace-heading mb-2">
              Advanced{" "}
              <span className="marketplace-heading-accent">Analytics</span>
            </h1>
            <p className="marketplace-text-muted">
              Comprehensive business intelligence and insights
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              asChild
              variant="outline"
              className="marketplace-btn-outline"
            >
              <Link href="/admin/dashboard">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Dashboard
              </Link>
            </Button>
            <Button
              onClick={fetchComprehensiveAnalytics}
              variant="outline"
              className="marketplace-btn-outline"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Button className="marketplace-btn-primary">
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Date Range Filter */}
        <div className="flex justify-center mb-8">
          <div className="flex gap-2">
            {[7, 30, 90].map((days) => (
              <Button
                key={days}
                variant={dateRange === days ? "default" : "outline"}
                onClick={() => setDateRange(days)}
                className={
                  dateRange === days
                    ? "marketplace-btn-primary"
                    : "marketplace-btn-outline"
                }
              >
                {days} Days
              </Button>
            ))}
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="marketplace-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium marketplace-text-muted">
                Total Revenue
              </CardTitle>
              <DollarSign className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold marketplace-text-primary">
                {formatPrice(stats.totalRevenue)}
              </div>
              <p className="text-xs marketplace-text-muted">
                +{formatPrice(stats.revenueThisMonth)} this month
              </p>
            </CardContent>
          </Card>

          <Card className="marketplace-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium marketplace-text-muted">
                Total Users
              </CardTitle>
              <Users className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold marketplace-text-primary">
                {stats.totalUsers.toLocaleString()}
              </div>
              <p className="text-xs marketplace-text-muted">
                +{stats.newUsersThisMonth} this month
              </p>
            </CardContent>
          </Card>

          <Card className="marketplace-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium marketplace-text-muted">
                Total Orders
              </CardTitle>
              <ShoppingCart className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold marketplace-text-primary">
                {stats.totalOrders.toLocaleString()}
              </div>
              <p className="text-xs marketplace-text-muted">
                +{stats.ordersThisMonth} this month
              </p>
            </CardContent>
          </Card>

          <Card className="marketplace-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium marketplace-text-muted">
                Avg Order Value
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold marketplace-text-primary">
                {formatPrice(stats.averageOrderValue)}
              </div>
              <p className="text-xs marketplace-text-muted">
                Per order average
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Revenue Chart */}
          <Card className="marketplace-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-orange-500" />
                Revenue Trends
              </CardTitle>
              <CardDescription>
                Daily revenue over the last {dateRange} days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) =>
                        new Date(value).toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                        })
                      }
                    />
                    <YAxis
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stroke="#f97316"
                      fill="#f97316"
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* User Growth Chart */}
          <Card className="marketplace-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-500" />
                User Growth
              </CardTitle>
              <CardDescription>
                New user registrations over the last {dateRange} days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={userGrowthData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) =>
                        new Date(value).toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                        })
                      }
                    />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip content={<CustomTooltip />} />
                    <Line
                      type="monotone"
                      dataKey="newUsers"
                      stroke="#3b82f6"
                      strokeWidth={2}
                      dot={{ fill: "#3b82f6" }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Top Products */}
          <Card className="marketplace-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5 text-green-500" />
                Top Performing Products
              </CardTitle>
              <CardDescription>
                Best selling products by revenue
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topProducts.slice(0, 5).map((product, index) => (
                  <div
                    key={product.id}
                    className="flex items-center justify-between p-3 rounded-lg border marketplace-border hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-orange-400 to-orange-600 flex items-center justify-center">
                        <span className="text-white text-sm font-bold">
                          {index + 1}
                        </span>
                      </div>
                      <div>
                        <h4 className="font-medium marketplace-text-primary">
                          {product.title}
                        </h4>
                        <p className="text-sm marketplace-text-muted">
                          {product.totalSales} units sold
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold marketplace-text-primary">
                        {formatPrice(product.totalRevenue)}
                      </p>
                      <p className="text-sm marketplace-text-muted">
                        {product.orderCount} orders
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Order Status Breakdown */}
          <Card className="marketplace-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-purple-500" />
                Order Status Distribution
              </CardTitle>
              <CardDescription>Current order status breakdown</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={[
                        {
                          name: "Pending",
                          value: stats.pendingOrders,
                          color: "#f59e0b",
                        },
                        {
                          name: "Completed",
                          value: stats.completedOrders,
                          color: "#10b981",
                        },
                        {
                          name: "Cancelled",
                          value: stats.cancelledOrders,
                          color: "#ef4444",
                        },
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) =>
                        `${name} ${(percent * 100).toFixed(0)}%`
                      }
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {[
                        {
                          name: "Pending",
                          value: stats.pendingOrders,
                          color: "#f59e0b",
                        },
                        {
                          name: "Completed",
                          value: stats.completedOrders,
                          color: "#10b981",
                        },
                        {
                          name: "Cancelled",
                          value: stats.cancelledOrders,
                          color: "#ef4444",
                        },
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value: any) => [
                        value.toLocaleString(),
                        "Orders",
                      ]}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="marketplace-card">
            <CardHeader>
              <CardTitle className="text-lg">Today's Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="marketplace-text-muted">Revenue:</span>
                  <span className="font-semibold">
                    {formatPrice(stats.revenueToday)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="marketplace-text-muted">Orders:</span>
                  <span className="font-semibold">{stats.ordersToday}</span>
                </div>
                <div className="flex justify-between">
                  <span className="marketplace-text-muted">New Users:</span>
                  <span className="font-semibold">{stats.newUsersToday}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="marketplace-card">
            <CardHeader>
              <CardTitle className="text-lg">This Week</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="marketplace-text-muted">Revenue:</span>
                  <span className="font-semibold">
                    {formatPrice(stats.revenueThisWeek)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="marketplace-text-muted">Orders:</span>
                  <span className="font-semibold">{stats.ordersThisWeek}</span>
                </div>
                <div className="flex justify-between">
                  <span className="marketplace-text-muted">New Users:</span>
                  <span className="font-semibold">
                    {stats.newUsersThisWeek}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="marketplace-card">
            <CardHeader>
              <CardTitle className="text-lg">This Month</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="marketplace-text-muted">Revenue:</span>
                  <span className="font-semibold">
                    {formatPrice(stats.revenueThisMonth)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="marketplace-text-muted">Orders:</span>
                  <span className="font-semibold">{stats.ordersThisMonth}</span>
                </div>
                <div className="flex justify-between">
                  <span className="marketplace-text-muted">New Users:</span>
                  <span className="font-semibold">
                    {stats.newUsersThisMonth}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
