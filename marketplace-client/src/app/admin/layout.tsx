"use client";

import { useEffect, useRef } from "react";
import { usePathname, useRouter } from "next/navigation";
import Layout from "@/components/layout/Layout";
import AdminNavigation from "@/components/admin/AdminNavigation";
import AccessDenied from "@/components/AccessDenied";
import { useAuth } from "@/lib/contexts/AuthContext";
import { SidebarProvider } from "@/lib/contexts/SidebarContext";
import {
  CollapsibleSidebar,
  useSidebarMargin,
} from "@/components/layout/CollapsibleSidebar";

interface AdminLayoutProps {
  children: React.ReactNode;
}

// Inner component that uses the sidebar context
function AdminLayoutContent({ children }: AdminLayoutProps) {
  const marginClass = useSidebarMargin();

  return (
    <div className="flex h-screen bg-gray-50">
      <CollapsibleSidebar>
        <AdminNavigation />
      </CollapsibleSidebar>
      <div
        className={`flex-1 overflow-y-auto transition-all duration-300 ${marginClass}`}
      >
        <div className="p-6">{children}</div>
      </div>
    </div>
  );
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const { user, isAuthenticated, isLoading, isInitialized } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const hasCheckedAuth = useRef(false);
  const lastAuthState = useRef({ isAuthenticated, userRole: user?.role });

  const known404Routes = [
    "/admin/analytics",
    "/admin/categories",
    "/admin/reports",
    "/admin/inventory",
    "/admin/orders",
    "/admin/messages",
  ];

  const isNavigatingFromNotFound =
    typeof window !== "undefined" &&
    sessionStorage.getItem("navigatingFrom404") === "true" &&
    (pathname === "/admin/dashboard" ||
      pathname === "/admin/users" ||
      pathname === "/admin/settings");

  const isKnown404Route = known404Routes.some((route) =>
    pathname.includes(route.split("/").pop() || "")
  );

  useEffect(() => {
    if (typeof window !== "undefined") {
      if (isKnown404Route) {
        // Set flag when user is on a 404 page
        sessionStorage.setItem("navigatingFrom404", "true");
      } else if (pathname.startsWith("/admin/")) {
        // Track the route for navigation purposes
        sessionStorage.setItem("lastAdminRoute", pathname);
      }
    }
  }, [pathname, isKnown404Route]);

  useEffect(() => {
    const authStateChanged =
      lastAuthState.current.isAuthenticated !== isAuthenticated ||
      lastAuthState.current.userRole !== user?.role;

    const isFirstLoad = !hasCheckedAuth.current;
    const shouldRunAuthCheck =
      !isLoading &&
      isInitialized &&
      !isKnown404Route &&
      (isFirstLoad || authStateChanged);

    if (process.env.NODE_ENV === "development") {
      console.log("AdminLayout AuthGuard:", {
        pathname,
        isAuthenticated,
        userRole: user?.role,
        isFirstLoad,
        authStateChanged,
        isLoading,
        isInitialized,
        isKnown404Route,
        shouldRunAuthCheck,
        lastAuthState: lastAuthState.current,
      });
    }

    if (isKnown404Route) {
      console.log(
        "AdminLayout: Skipping auth check for known 404 route:",
        pathname
      );
    } else if (isNavigatingFromNotFound) {
      console.log(
        "AdminLayout: Allowing navigation from 404 page to:",
        pathname
      );
      // Don't run auth check, but clear the flag since we're now on a real page
      if (typeof window !== "undefined") {
        sessionStorage.removeItem("navigatingFrom404");
      }
    } else if (shouldRunAuthCheck) {
      console.log("AdminLayout: Running auth check because:", {
        isFirstLoad,
        authStateChanged,
      });

      hasCheckedAuth.current = true;
      lastAuthState.current = { isAuthenticated, userRole: user?.role };

      if (!isAuthenticated) {
        console.log("AdminLayout: Redirecting to login - not authenticated");
        router.replace("/auth/login");
      } else if (user?.role !== "admin") {
        console.log("AdminLayout: Redirecting - wrong role:", user?.role);
        const roleRedirects: Record<string, string> = {
          seller: "/seller/dashboard",
          buyer: "/products",
        };
        const redirectPath = user?.role
          ? roleRedirects[user.role] || "/products"
          : "/products";
        router.replace(redirectPath);
      } else {
        console.log("AdminLayout: Auth check passed - user is admin");
      }
    } else if (!isLoading && isInitialized) {
      console.log("AdminLayout: Skipping auth check - no state change");
    }
  }, [
    isLoading,
    isInitialized,
    isAuthenticated,
    user?.role,
    pathname,
    isKnown404Route,
    isNavigatingFromNotFound,
    router,
  ]);

  useEffect(() => {
    if (!isAuthenticated) {
      hasCheckedAuth.current = false;
    }
  }, [isAuthenticated]);

  const shouldShowAdminInterface =
    (isAuthenticated && user?.role === "admin") ||
    (isKnown404Route && pathname.includes("/admin/")) ||
    (isNavigatingFromNotFound &&
      (isAuthenticated || isLoading || !isInitialized));
  return (
    <Layout>
      {isLoading || !isInitialized ? (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
        </div>
      ) : !shouldShowAdminInterface ? (
        <AccessDenied
          title="Admin Access Required"
          description="You need administrator privileges to access this area."
          redirectPath="/auth/login"
          redirectLabel="Sign In as Admin"
          countdown={5}
        />
      ) : (
        <SidebarProvider>
          <AdminLayoutContent>{children}</AdminLayoutContent>
        </SidebarProvider>
      )}
    </Layout>
  );
}
