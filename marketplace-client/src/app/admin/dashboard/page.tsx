"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/contexts/AuthContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Package,
  ShoppingCart,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Crown,
  Activity,
  RefreshCw,
} from "lucide-react";
import Link from "next/link";
import { formatPrice } from "@/lib/utils/formatPrice";

interface DashboardStats {
  totalUsers: number;
  totalProducts: number;
  totalOrders: number;
  totalRevenue: number;
  newUsersToday: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
  ordersToday: number;
  ordersThisWeek: number;
  ordersThisMonth: number;
  revenueToday: number;
  revenueThisWeek: number;
  revenueThisMonth: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  averageOrderValue: number;
  activeDisputes: number;
}

interface ActivityItem {
  id: string;
  type:
    | "new_user"
    | "new_order"
    | "new_product"
    | "order_fulfilled"
    | "order_cancelled"
    | "high_value_order";
  message: string;
  time: string;
  severity: "info" | "success" | "warning" | "error";
  relatedId?: string;
  amount?: number;
}

export default function AdminDashboard() {
  const { user, isAuthenticated, token } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isAuthenticated && token) {
      fetchDashboardStats();
    }
  }, [isAuthenticated, token]);

  const fetchDashboardStats = async () => {
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      // Fetch both stats and activities
      const [statsResponse, activitiesResponse] = await Promise.all([
        fetch(`${API_BASE}/analytics/admin/dashboard`, {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }),
        fetch(`${API_BASE}/analytics/admin/activities?limit=8`, {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }),
      ]);

      if (!statsResponse.ok || !activitiesResponse.ok) {
        throw new Error("Failed to fetch dashboard data");
      }

      const [statsData, activitiesData] = await Promise.all([
        statsResponse.json(),
        activitiesResponse.json(),
      ]);

      setStats(statsData.data);
      setActivities(activitiesData.data);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      setError("Failed to load dashboard data");
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get activity icon
  const getActivityIcon = (type: ActivityItem["type"]) => {
    switch (type) {
      case "new_user":
        return <Users className="h-4 w-4" />;
      case "new_order":
        return <ShoppingCart className="h-4 w-4" />;
      case "new_product":
        return <Package className="h-4 w-4" />;
      case "order_fulfilled":
        return <TrendingUp className="h-4 w-4" />;
      case "order_cancelled":
        return <AlertTriangle className="h-4 w-4" />;
      case "high_value_order":
        return <DollarSign className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getSeverityColor = (severity: ActivityItem["severity"]) => {
    switch (severity) {
      case "warning":
        return "text-orange-700 bg-orange-100";
      case "error":
        return "text-red-700 bg-red-100";
      case "success":
        return "text-green-700 bg-green-100";
      default:
        return "text-indigo-700 bg-indigo-100";
    }
  };

  if (isLoading) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <AlertTriangle className="mx-auto h-16 w-16 text-red-500 mb-6" />
            <h2 className="text-2xl marketplace-heading mb-4">
              Error Loading Dashboard
            </h2>
            <p className="marketplace-text-muted mb-8">
              {error || "Failed to load dashboard data"}
            </p>
            <Button
              onClick={fetchDashboardStats}
              className="marketplace-btn-primary"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="marketplace-theme space-y-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-4xl marketplace-heading flex items-center justify-center mb-4">
          <Crown className="mr-3 h-8 w-8 marketplace-text-warm" />
          Admin <span className="marketplace-heading-accent">Dashboard</span>
        </h1>
        <p className="text-lg marketplace-text-muted">
          Welcome back, {user?.name}!
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 mt-6">
          <Button asChild className="marketplace-btn-outline w-full sm:w-auto">
            <Link href="/admin/users">
              <Users className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Manage Users</span>
              <span className="sm:hidden">Users</span>
            </Link>
          </Button>
          <Button asChild className="marketplace-btn-primary w-full sm:w-auto">
            <Link href="/admin/analytics">
              <Activity className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">View Analytics</span>
              <span className="sm:hidden">Analytics</span>
            </Link>
          </Button>
          <Button
            onClick={fetchDashboardStats}
            variant="outline"
            className="marketplace-btn-outline w-full sm:w-auto"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="marketplace-card marketplace-hover-lift">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium marketplace-text-primary">
              Total Users
            </CardTitle>
            <Users className="h-4 w-4 marketplace-text-cool" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold marketplace-text-primary">
              {stats.totalUsers.toLocaleString()}
            </div>
            <p className="text-xs marketplace-text-muted">
              +{stats.newUsersToday} new today
            </p>
          </CardContent>
        </Card>

        <Card className="marketplace-card marketplace-hover-lift">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium marketplace-text-primary">
              Total Products
            </CardTitle>
            <Package className="h-4 w-4 marketplace-text-cool" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold marketplace-text-primary">
              {stats.totalProducts.toLocaleString()}
            </div>
            <p className="text-xs marketplace-text-muted">
              <Link
                href="/admin/products"
                className="marketplace-text-warm hover:underline"
              >
                Manage products
              </Link>
            </p>
          </CardContent>
        </Card>

        <Card className="marketplace-card marketplace-hover-lift">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium marketplace-text-primary">
              Total Orders
            </CardTitle>
            <ShoppingCart className="h-4 w-4 marketplace-text-cool" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold marketplace-text-primary">
              {stats.totalOrders.toLocaleString()}
            </div>
            <p className="text-xs marketplace-text-muted">
              {stats.pendingOrders} pending
            </p>
          </CardContent>
        </Card>

        <Card className="marketplace-card marketplace-hover-lift">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium marketplace-text-primary">
              Total Revenue
            </CardTitle>
            <DollarSign className="h-4 w-4 marketplace-text-warm" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold marketplace-text-warm">
              {formatPrice(stats.totalRevenue)}
            </div>
            <p className="text-xs marketplace-text-muted">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              +12% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Alert Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="text-yellow-800 flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5" />
              Pending Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-900">
              {stats.pendingOrders}
            </div>
            <Button asChild variant="outline" size="sm" className="mt-2">
              <Link href="/admin/orders?status=pending">Review Orders</Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="text-orange-800 flex items-center">
              <Package className="mr-2 h-5 w-5" />
              Cancelled Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-900">
              {stats.cancelledOrders}
            </div>
            <Button asChild variant="outline" size="sm" className="mt-2">
              <Link href="/admin/orders">View Orders</Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800 flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5" />
              Active Disputes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-900">
              {stats.activeDisputes}
            </div>
            <Button asChild variant="outline" size="sm" className="mt-2">
              <Link href="/admin/disputes">Resolve Disputes</Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest system events and notifications
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {activities.length === 0 ? (
              <div className="text-center py-8">
                <Activity className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-500">No recent activity</p>
              </div>
            ) : (
              activities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div
                    className={`p-2 rounded-full ${getSeverityColor(
                      activity.severity
                    )}`}
                  >
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm marketplace-text-primary">
                      {activity.message}
                    </p>
                    <p className="text-xs marketplace-text-muted">
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common administrative tasks</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button asChild variant="outline" className="w-full justify-start">
              <Link href="/admin/users">
                <Users className="mr-2 h-4 w-4" />
                Manage Users & Roles
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full justify-start">
              <Link href="/admin/categories">
                <Package className="mr-2 h-4 w-4" />
                Manage Categories
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full justify-start">
              <Link href="/admin/orders">
                <ShoppingCart className="mr-2 h-4 w-4" />
                Review Orders
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full justify-start">
              <Link href="/admin/analytics">
                <Activity className="mr-2 h-4 w-4" />
                View System Analytics
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
