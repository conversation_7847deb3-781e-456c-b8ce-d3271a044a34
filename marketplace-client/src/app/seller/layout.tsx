"use client";

import { useEffect, useRef } from "react";
import { usePathname, useRouter } from "next/navigation";
import Layout from "@/components/layout/Layout";
import SellerNavigation from "@/components/seller/SellerNavigation";
import AccessDenied from "@/components/AccessDenied";
import { useAuth } from "@/lib/contexts/AuthContext";
import { SidebarProvider } from "@/lib/contexts/SidebarContext";
import {
  CollapsibleSidebar,
  useSidebarMargin,
} from "@/components/layout/CollapsibleSidebar";

interface SellerLayoutProps {
  children: React.ReactNode;
}

// Inner component that uses the sidebar context
function SellerLayoutContent({ children }: SellerLayoutProps) {
  const marginClass = useSidebarMargin();

  return (
    <div className="flex h-screen bg-gray-50">
      <CollapsibleSidebar>
        <SellerNavigation />
      </CollapsibleSidebar>
      <div
        className={`flex-1 overflow-y-auto transition-all duration-300 ${marginClass}`}
      >
        <div className="p-6">{children}</div>
      </div>
    </div>
  );
}

export default function SellerLayout({ children }: SellerLayoutProps) {
  const { user, isAuthenticated, isLoading, isInitialized } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const hasCheckedAuth = useRef(false);
  const lastAuthState = useRef({ isAuthenticated, userRole: user?.role });

  const known404Routes = [
    "/seller/inventory",
    "/seller/reports",
    "/seller/customers",
    "/seller/promotions",
    "/seller/shipping",
  ];

  const isNavigatingFromNotFound =
    typeof window !== "undefined" &&
    sessionStorage.getItem("navigatingFromSeller404") === "true" &&
    (pathname === "/seller/dashboard" || pathname === "/seller/products");

  const isKnown404Route = known404Routes.some((route) =>
    pathname.includes(route.split("/").pop() || "")
  );

  useEffect(() => {
    if (typeof window !== "undefined") {
      if (isKnown404Route) {
        // Set flag when user is on a 404 page
        sessionStorage.setItem("navigatingFromSeller404", "true");
        console.log(
          "SellerLayout: Set navigatingFromSeller404 flag for",
          pathname
        );
      } else if (pathname.startsWith("/seller/")) {
        // Just track the route, flag clearing is handled in auth logic
        sessionStorage.setItem("lastSellerRoute", pathname);
        console.log("SellerLayout: Tracking seller route:", pathname);
      }
    }
  }, [pathname, isKnown404Route]);

  useEffect(() => {
    const authStateChanged =
      lastAuthState.current.isAuthenticated !== isAuthenticated ||
      lastAuthState.current.userRole !== user?.role;

    const isFirstLoad = !hasCheckedAuth.current;
    const shouldRunAuthCheck =
      !isLoading &&
      isInitialized &&
      !isKnown404Route &&
      (isFirstLoad || authStateChanged);

    if (process.env.NODE_ENV === "development") {
      console.log("SellerLayout AuthGuard:", {
        pathname,
        isAuthenticated,
        userRole: user?.role,
        isFirstLoad,
        authStateChanged,
        isLoading,
        isInitialized,
        isKnown404Route,
        shouldRunAuthCheck,
        lastAuthState: lastAuthState.current,
      });
    }

    if (isKnown404Route) {
      console.log(
        "SellerLayout: Skipping auth check for known 404 route:",
        pathname
      );
    } else if (isNavigatingFromNotFound) {
      console.log(
        "SellerLayout: Allowing navigation from 404 page to:",
        pathname
      );
      // Don't run auth check, but clear the flag since we're now on a real page
      if (typeof window !== "undefined") {
        sessionStorage.removeItem("navigatingFromSeller404");
      }
    } else if (shouldRunAuthCheck) {
      console.log("SellerLayout: Running auth check because:", {
        isFirstLoad,
        authStateChanged,
      });

      hasCheckedAuth.current = true;
      lastAuthState.current = { isAuthenticated, userRole: user?.role };

      if (!isAuthenticated) {
        console.log("SellerLayout: Redirecting to login - not authenticated");
        router.replace("/auth/login");
      } else if (user?.role !== "seller") {
        console.log("SellerLayout: Redirecting - wrong role:", user?.role);
        const roleRedirects: Record<string, string> = {
          admin: "/admin/dashboard",
          buyer: "/products",
        };
        const redirectPath = user?.role
          ? roleRedirects[user.role] || "/products"
          : "/products";
        router.replace(redirectPath);
      } else {
        console.log("SellerLayout: Auth check passed - user is seller");
      }
    } else if (!isLoading && isInitialized) {
      console.log("SellerLayout: Skipping auth check - no state change");
    }
  }, [
    isLoading,
    isInitialized,
    isAuthenticated,
    user?.role,
    pathname,
    isKnown404Route,
    isNavigatingFromNotFound,
    router,
  ]);

  useEffect(() => {
    if (!isAuthenticated) {
      hasCheckedAuth.current = false;
    }
  }, [isAuthenticated]);

  const shouldShowSellerInterface =
    (isAuthenticated && user?.role === "seller") ||
    (isKnown404Route && pathname.includes("/seller/")) ||
    (isNavigatingFromNotFound &&
      (isAuthenticated || isLoading || !isInitialized));
  return (
    <Layout>
      {isLoading || !isInitialized ? (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
        </div>
      ) : !shouldShowSellerInterface ? (
        <AccessDenied
          title="Seller Access Required"
          description="You need seller privileges to access this area."
          redirectPath="/auth/login"
          redirectLabel="Sign In as Seller"
          countdown={5}
        />
      ) : (
        <SidebarProvider>
          <SellerLayoutContent>{children}</SellerLayoutContent>
        </SidebarProvider>
      )}
    </Layout>
  );
}
