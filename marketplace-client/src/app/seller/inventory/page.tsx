"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Package,
  Search,
  Filter,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  Eye,
  Edit,
  Plus,
  Minus,
  RotateCcw,
  Download,
  Upload,
} from "lucide-react";
import {
  useGetSellerInventoryQuery,
  useGetInventoryStatsQuery,
  useUpdateStockMutation,
  useAdjustStockMutation,
  Inventory,
} from "@/lib/api/inventory";
import { useNotifications } from "@/lib/providers/NotificationProvider";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { InventoryAnalytics } from "@/components/inventory/InventoryAnalytics";
import { EditInventoryModal } from "@/components/inventory/EditInventoryModal";

export default function SellerInventory() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [editingInventory, setEditingInventory] = useState<Inventory | null>(
    null
  );
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const notifications = useNotifications();

  const {
    data: inventoryData,
    isLoading: inventoryLoading,
    error: inventoryError,
    refetch: refetchInventory,
  } = useGetSellerInventoryQuery({
    search: searchTerm || undefined,
    status: filterStatus !== "all" ? filterStatus : undefined,
    page: currentPage,
    limit: 20,
  });

  const { data: statsData, isLoading: statsLoading } =
    useGetInventoryStatsQuery();

  const [updateStock] = useUpdateStockMutation();
  const [adjustStock] = useAdjustStockMutation();

  const inventories = inventoryData?.data?.inventories || [];
  const stats = statsData?.data;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "in_stock":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "low_stock":
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case "out_of_stock":
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Package className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "in_stock":
        return "bg-green-100 text-green-800 border-green-200";
      case "low_stock":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "out_of_stock":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const handleQuickAdjust = async (
    inventoryId: string,
    adjustment: number,
    currentStock: number
  ) => {
    try {
      const newQuantity = Math.max(0, currentStock + adjustment);
      await adjustStock({
        inventoryId,
        newQuantity,
        reason: `Quick adjustment: ${adjustment > 0 ? "+" : ""}${adjustment}`,
      }).unwrap();

      notifications.success("Stock updated successfully!");
      refetchInventory();
    } catch (error: any) {
      notifications.error(error?.data?.message || "Failed to update stock");
    }
  };

  const handleEditInventory = (inventory: Inventory) => {
    setEditingInventory(inventory);
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setEditingInventory(null);
  };

  const handleEditSuccess = () => {
    refetchInventory();
  };

  const filteredItems = inventories.filter((item) => {
    const matchesSearch =
      !searchTerm ||
      item.product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.sku?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter =
      filterStatus === "all" || item.status === filterStatus;

    return matchesSearch && matchesFilter;
  });

  if (inventoryLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (inventoryError) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold text-red-600 mb-2">
          Error Loading Inventory
        </h3>
        <p className="text-gray-600 mb-4">Please try again later.</p>
        <Button onClick={() => refetchInventory()}>
          <RotateCcw className="mr-2 h-4 w-4" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold marketplace-heading">
            Inventory Management
          </h1>
          <p className="marketplace-text-muted">
            Track and manage your product stock levels
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={() => refetchInventory()}>
            <RotateCcw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
        </div>
      </div>

      {/* Inventory Analytics */}
      <InventoryAnalytics />

      {/* Search and Filter */}
      <Card className="bg-white border border-gray-200 shadow-sm">
        <CardContent className="p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="relative flex-1 max-w-md w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                placeholder="Search products, SKU..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 border-gray-300 focus:border-orange-500 focus:ring-orange-500"
              />
            </div>
            <div className="flex gap-2 w-full sm:w-auto">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="all">All Status</option>
                <option value="in_stock">In Stock</option>
                <option value="low_stock">Low Stock</option>
                <option value="out_of_stock">Out of Stock</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Table */}
      <Card className="marketplace-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Inventory Items
          </CardTitle>
          <CardDescription>
            Manage your product stock levels and track inventory status
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 sm:px-6 font-semibold marketplace-text-primary">
                    Product
                  </th>
                  <th className="text-left py-3 px-4 sm:px-6 font-semibold marketplace-text-primary">
                    SKU
                  </th>
                  <th className="text-left py-3 px-4 sm:px-6 font-semibold marketplace-text-primary">
                    Stock
                  </th>
                  <th className="text-left py-3 px-4 sm:px-6 font-semibold marketplace-text-primary">
                    Status
                  </th>
                  <th className="text-left py-3 px-4 sm:px-6 font-semibold marketplace-text-primary">
                    Price
                  </th>
                  <th className="text-left py-3 px-4 sm:px-6 font-semibold marketplace-text-primary">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredItems.map((item) => (
                  <tr
                    key={item.id}
                    className="border-b border-gray-100 hover:bg-gray-50 transition-colors"
                  >
                    <td className="py-3 px-4 sm:px-6">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                          {item.product.images?.[0] ? (
                            <img
                              src={item.product.images[0]}
                              alt={item.product.title}
                              className="w-full h-full object-cover rounded-lg"
                            />
                          ) : (
                            <Package className="h-5 w-5 sm:h-6 sm:w-6 text-gray-400" />
                          )}
                        </div>
                        <div>
                          <p className="font-semibold marketplace-text-primary text-sm sm:text-base">
                            {item.product.title}
                          </p>
                          <p className="text-xs sm:text-sm marketplace-text-muted">
                            ID: {item.product.id.slice(0, 8)}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4 sm:px-6 text-sm marketplace-text-muted font-mono">
                      {item.sku || "N/A"}
                    </td>
                    <td className="py-3 px-4 sm:px-6">
                      <div className="flex items-center gap-2">
                        <span className="font-semibold text-sm sm:text-base">
                          {item.currentStock}
                        </span>
                        {item.reservedStock > 0 && (
                          <span className="text-xs text-gray-500">
                            ({item.reservedStock} reserved)
                          </span>
                        )}
                      </div>
                      <p className="text-xs text-gray-500">
                        Threshold: {item.lowStockThreshold}
                      </p>
                    </td>
                    <td className="py-3 px-4 sm:px-6">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(item.status)}
                        <Badge
                          className={`text-xs ${getStatusColor(item.status)}`}
                        >
                          {item.status.replace("_", " ").toUpperCase()}
                        </Badge>
                      </div>
                    </td>
                    <td className="py-3 px-4 sm:px-6 font-semibold marketplace-text-warm text-sm sm:text-base">
                      {new Intl.NumberFormat("en-KE", {
                        style: "currency",
                        currency: "KES",
                      }).format(item.product.price)}
                    </td>
                    <td className="py-3 px-4 sm:px-6">
                      <div className="flex items-center gap-1 sm:gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() =>
                            handleQuickAdjust(item.id, -1, item.currentStock)
                          }
                          disabled={item.currentStock <= 0}
                          className="h-8 w-8 p-0"
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() =>
                            handleQuickAdjust(item.id, 1, item.currentStock)
                          }
                          className="h-8 w-8 p-0"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                          onClick={() => handleEditInventory(item)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredItems.length === 0 && (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold marketplace-text-primary mb-2">
                No inventory items found
              </h3>
              <p className="marketplace-text-muted">
                {searchTerm || filterStatus !== "all"
                  ? "Try adjusting your search or filters"
                  : "Start by adding products to your inventory"}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Inventory Modal */}
      <EditInventoryModal
        inventory={editingInventory}
        isOpen={isEditModalOpen}
        onClose={handleCloseEditModal}
        onSuccess={handleEditSuccess}
      />
    </div>
  );
}
