"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/contexts/AuthContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ShoppingCart,
  Package,
  Truck,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  RefreshCw,
  Filter,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { getFirstImageUrl } from "@/lib/utils/imageUtils";

interface SellerOrderItem {
  id: string;
  product: {
    id: string;
    title: string;
    price: number;
    images: string[];
  };
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  status: "PENDING" | "SHIPPED" | "DELIVERED";
  order: {
    id: string;
    createdAt: string;
    buyer: {
      id: string;
      name: string;
      email: string;
    };
  };
}

export default function SellerOrdersPage() {
  const [orderItems, setOrderItems] = useState<SellerOrderItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const { isAuthenticated, token } = useAuth();

  useEffect(() => {
    if (isAuthenticated && token) {
      fetchSellerOrders();
    }
  }, [isAuthenticated, token]);

  const fetchSellerOrders = async () => {
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/orders/sold`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch orders");
      }

      const data = await response.json();
      setOrderItems(data.orders || []);
    } catch (error) {
      console.error("Error fetching seller orders:", error);
      setError("Failed to load orders");
    } finally {
      setIsLoading(false);
    }
  };

  const updateOrderStatus = async (orderItemId: string, newStatus: string) => {
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/orders/${orderItemId}/status`, {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error("Failed to update order status");
      }

      // Refresh orders after update
      await fetchSellerOrders();
    } catch (error) {
      console.error("Error updating order status:", error);
      setError("Failed to update order status");
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "SHIPPED":
        return <Truck className="h-4 w-4 text-orange-500" />;
      case "DELIVERED":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return "bg-yellow-100 text-yellow-800";
      case "SHIPPED":
        return "bg-orange-100 text-orange-800";
      case "DELIVERED":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const filteredOrderItems = orderItems.filter(
    (item) => statusFilter === "all" || item.status === statusFilter
  );

  return (
    <div className="marketplace-theme space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl marketplace-heading">
            Order <span className="marketplace-heading-accent">Management</span>
          </h1>
          <p className="marketplace-text-muted">
            Track and fulfill your customer orders
          </p>
        </div>
        <Button
          onClick={fetchSellerOrders}
          variant="outline"
          className="marketplace-btn-outline"
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card className="marketplace-card">
        <CardHeader>
          <CardTitle className="marketplace-text-primary flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex items-center gap-2">
              <label className="text-sm marketplace-text-muted">Status:</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Orders</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="SHIPPED">Shipped</SelectItem>
                  <SelectItem value="DELIVERED">Delivered</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Orders List */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
        </div>
      ) : error ? (
        <Card className="marketplace-card">
          <CardContent className="text-center py-12">
            <XCircle className="mx-auto h-16 w-16 text-red-500 mb-6" />
            <h2 className="text-2xl marketplace-heading mb-4">
              Error Loading Orders
            </h2>
            <p className="marketplace-text-muted mb-8">{error}</p>
            <Button
              onClick={fetchSellerOrders}
              className="marketplace-btn-primary"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      ) : filteredOrderItems.length === 0 ? (
        <Card className="marketplace-card">
          <CardContent className="text-center py-16">
            <ShoppingCart className="mx-auto h-16 w-16 marketplace-text-muted mb-6" />
            <h2 className="text-2xl marketplace-heading mb-4">
              {statusFilter === "all"
                ? "No Orders Yet"
                : `No ${statusFilter.toLowerCase()} Orders`}
            </h2>
            <p className="marketplace-text-muted mb-8 max-w-md mx-auto">
              {statusFilter === "all"
                ? "You haven't received any orders yet. Keep promoting your products!"
                : `No orders with ${statusFilter.toLowerCase()} status found.`}
            </p>
            <Button asChild className="marketplace-btn-primary">
              <Link href="/seller/products">Manage Products</Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredOrderItems.map((item) => (
            <Card key={item.id} className="marketplace-card">
              <CardContent className="p-6">
                <div className="flex gap-4">
                  {/* Product Image */}
                  <div className="relative w-20 h-20 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                    <Image
                      src={getFirstImageUrl(item.product.images)}
                      alt={item.product.title}
                      fill
                      className="object-cover"
                      onError={(e) => {
                        e.currentTarget.src = "/placeholder-product.svg";
                      }}
                    />
                  </div>

                  {/* Order Details */}
                  <div className="flex-1">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-semibold marketplace-text-primary text-lg">
                          {item.product.title}
                        </h3>
                        <p className="marketplace-text-muted">
                          Order #{item.order.id.slice(-8).toUpperCase()}
                        </p>
                        <p className="text-sm marketplace-text-muted">
                          Customer: {item.order.buyer.name}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(item.status)}>
                          <div className="flex items-center gap-1">
                            {getStatusIcon(item.status)}
                            {item.status}
                          </div>
                        </Badge>
                        <p className="text-lg font-bold marketplace-text-warm mt-2">
                          {formatPrice(item.totalPrice)}
                        </p>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <div className="text-sm marketplace-text-muted">
                        <p>Quantity: {item.quantity}</p>
                        <p>Ordered: {formatDate(item.order.createdAt)}</p>
                      </div>

                      <div className="flex gap-2">
                        {/* Status Update */}
                        {item.status === "PENDING" && (
                          <Button
                            onClick={() =>
                              updateOrderStatus(item.id, "SHIPPED")
                            }
                            size="sm"
                            className="marketplace-btn-primary"
                          >
                            <Truck className="mr-2 h-4 w-4" />
                            Mark as Shipped
                          </Button>
                        )}

                        {item.status === "SHIPPED" && (
                          <Button
                            onClick={() =>
                              updateOrderStatus(item.id, "DELIVERED")
                            }
                            size="sm"
                            className="marketplace-btn-primary"
                          >
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Mark as Delivered
                          </Button>
                        )}

                        <Button
                          asChild
                          variant="outline"
                          size="sm"
                          className="marketplace-btn-outline"
                        >
                          <Link href={`/seller/orders/${item.order.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
