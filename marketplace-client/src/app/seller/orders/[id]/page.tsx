"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/contexts/AuthContext";
import { useNotifications } from "@/lib/hooks/useNotifications";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Package,
  User,
  MapPin,
  Phone,
  Calendar,
  CreditCard,
  Truck,
  CheckCircle,
  Clock,
  ArrowLeft,
  Mail,
  Hash,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { getFirstImageUrl } from "@/lib/utils/imageUtils";

interface SellerOrderDetails {
  id: string;
  buyer: {
    id: string;
    name: string;
    email: string;
  };
  items: Array<{
    id: string;
    product: {
      id: string;
      title: string;
      price: number;
      images: string[];
      seller: {
        id: string;
        name: string;
      };
    };
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    status: "PENDING" | "SHIPPED" | "DELIVERED";
  }>;
  total: number;
  status: "pending" | "paid" | "shipped" | "fulfilled" | "cancelled";
  createdAt: string;
  shippingAddress?: {
    fullName: string;
    phone: string;
    street: string;
    city: string;
    county: string;
    postalCode: string;
    country: string;
  };
  paymentInfo?: {
    method: string;
    status: string;
    transactionId?: string;
  };
}

export default function SellerOrderDetailsPage({
  params,
}: {
  params: { id: string };
}) {
  const [order, setOrder] = useState<SellerOrderDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [failedImages, setFailedImages] = useState<Set<string>>(new Set());
  const { isAuthenticated, token } = useAuth();
  const notifications = useNotifications();

  useEffect(() => {
    if (isAuthenticated && token && params.id) {
      fetchOrderDetails();
    }
  }, [isAuthenticated, token, params.id]);

  const fetchOrderDetails = async () => {
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/orders/${params.id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch order details");
      }

      const data = await response.json();
      console.log("Order data received:", data.data);
      console.log("First item images:", data.data?.items?.[0]?.product?.images);
      setOrder(data.data);
    } catch (error) {
      console.error("Error fetching order details:", error);
      setError("Failed to load order details");
    } finally {
      setIsLoading(false);
    }
  };

  const updateOrderItemStatus = async (
    orderItemId: string,
    newStatus: string
  ) => {
    setIsUpdating(true);
    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/orders/${orderItemId}/status`, {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error("Failed to update order status");
      }

      // Refresh order details after update
      await fetchOrderDetails();
      notifications.success(
        `Order status updated to ${newStatus.toLowerCase()}`
      );
    } catch (error) {
      console.error("Error updating order status:", error);
      notifications.error("Failed to update order status");
    } finally {
      setIsUpdating(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "SHIPPED":
        return <Truck className="h-4 w-4 text-orange-500" />;
      case "DELIVERED":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return "bg-yellow-100 text-yellow-800";
      case "SHIPPED":
        return "bg-orange-100 text-orange-800";
      case "DELIVERED":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <Package className="mx-auto h-16 w-16 marketplace-text-muted mb-6" />
            <h1 className="text-3xl marketplace-heading mb-4">Access Denied</h1>
            <p className="marketplace-text-muted mb-8">
              Please sign in to view order details.
            </p>
            <Button asChild className="marketplace-btn-primary">
              <Link href="/auth/login">Sign In</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <Package className="mx-auto h-16 w-16 text-red-500 mb-6" />
            <h2 className="text-2xl marketplace-heading mb-4">
              Order Not Found
            </h2>
            <p className="marketplace-text-muted mb-8">
              {error || "The requested order could not be found."}
            </p>
            <div className="space-x-4">
              <Button
                onClick={fetchOrderDetails}
                className="marketplace-btn-primary"
              >
                Try Again
              </Button>
              <Button
                asChild
                variant="outline"
                className="marketplace-btn-outline"
              >
                <Link href="/seller/orders">Back to Orders</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="marketplace-theme min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl marketplace-heading mb-2">
              Order{" "}
              <span className="marketplace-heading-accent">
                #{order.id.slice(-8).toUpperCase()}
              </span>
            </h1>
            <p className="marketplace-text-muted">
              Placed on {formatDate(order.createdAt)}
            </p>
          </div>
          <Button asChild variant="outline" className="marketplace-btn-outline">
            <Link href="/seller/orders">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Orders
            </Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Items */}
          <div className="lg:col-span-2 space-y-6">
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary flex items-center">
                  <Package className="mr-2 h-5 w-5" />
                  Order Items ({order.items.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {order.items.map((item) => (
                  <div
                    key={item.id}
                    className="flex gap-4 p-4 bg-gray-50 rounded-lg"
                  >
                    <div className="relative w-20 h-20 bg-white rounded overflow-hidden flex-shrink-0">
                      {failedImages.has(item.id) ? (
                        <div className="w-full h-full flex items-center justify-center bg-gray-100">
                          <Package className="h-8 w-8 text-gray-400" />
                        </div>
                      ) : (
                        <Image
                          src={getFirstImageUrl(item.product.images)}
                          alt={item.product.title}
                          fill
                          className="object-cover"
                          onError={(e) => {
                            console.log("Image failed to load:", {
                              src: e.currentTarget.src,
                              originalImages: item.product.images,
                              generatedUrl: getFirstImageUrl(
                                item.product.images
                              ),
                            });
                            setFailedImages((prev) =>
                              new Set(prev).add(item.id)
                            );
                          }}
                        />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-semibold marketplace-text-primary">
                            {item.product.title}
                          </h3>
                          <p className="text-sm marketplace-text-muted">
                            Quantity: {item.quantity} ×{" "}
                            {formatPrice(item.unitPrice)}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold marketplace-text-warm">
                            {formatPrice(item.totalPrice)}
                          </p>
                          <Badge className={getStatusColor(item.status)}>
                            <div className="flex items-center gap-1">
                              {getStatusIcon(item.status)}
                              {item.status}
                            </div>
                          </Badge>
                        </div>
                      </div>

                      {/* Status Update Buttons */}
                      <div className="flex gap-2 mt-3">
                        {item.status === "PENDING" && (
                          <Button
                            onClick={() =>
                              updateOrderItemStatus(item.id, "SHIPPED")
                            }
                            disabled={isUpdating}
                            size="sm"
                            className="marketplace-btn-primary"
                          >
                            <Truck className="mr-2 h-4 w-4" />
                            Mark as Shipped
                          </Button>
                        )}

                        {item.status === "SHIPPED" && (
                          <Button
                            onClick={() =>
                              updateOrderItemStatus(item.id, "DELIVERED")
                            }
                            disabled={isUpdating}
                            size="sm"
                            className="marketplace-btn-primary"
                          >
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Mark as Delivered
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Order Summary & Customer Info */}
          <div className="space-y-6">
            {/* Customer Information */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 marketplace-text-muted" />
                  <span className="marketplace-text-primary font-medium">
                    {order.buyer.name}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 marketplace-text-muted" />
                  <span className="marketplace-text-primary">
                    {order.buyer.email}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Shipping Address */}
            {order.shippingAddress && (
              <Card className="marketplace-card">
                <CardHeader>
                  <CardTitle className="marketplace-text-primary flex items-center">
                    <MapPin className="mr-2 h-5 w-5" />
                    Shipping Address
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 marketplace-text-muted" />
                        <span className="marketplace-text-primary font-medium">
                          {order.shippingAddress.fullName}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 marketplace-text-muted" />
                        <span className="marketplace-text-primary">
                          {order.shippingAddress.phone}
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <MapPin className="h-4 w-4 marketplace-text-muted mt-0.5" />
                        <div className="marketplace-text-primary">
                          <div>{order.shippingAddress.street}</div>
                          <div>
                            {order.shippingAddress.city},{" "}
                            {order.shippingAddress.county}
                          </div>
                          <div>
                            {order.shippingAddress.postalCode},{" "}
                            {order.shippingAddress.country}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Payment Information */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary flex items-center">
                  <CreditCard className="mr-2 h-5 w-5" />
                  Payment & Order Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {order.paymentInfo && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="marketplace-text-muted">
                          Payment Method:
                        </span>
                        <span className="marketplace-text-primary font-medium">
                          {order.paymentInfo.method}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="marketplace-text-muted">
                          Payment Status:
                        </span>
                        <Badge className="bg-green-100 text-green-800">
                          {order.paymentInfo.status}
                        </Badge>
                      </div>
                      {order.paymentInfo.transactionId && (
                        <div className="flex justify-between">
                          <span className="marketplace-text-muted">
                            Transaction ID:
                          </span>
                          <span className="marketplace-text-primary font-mono text-sm">
                            {order.paymentInfo.transactionId}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="marketplace-text-muted">Items Total:</span>
                    <span className="marketplace-text-primary">
                      {formatPrice(order.total)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="marketplace-text-muted">Shipping:</span>
                    <span className="marketplace-text-primary">Free</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between text-lg font-bold">
                    <span className="marketplace-text-primary">Total:</span>
                    <span className="marketplace-text-warm">
                      {formatPrice(order.total)}
                    </span>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Hash className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-blue-800 font-medium">
                      Order ID: #{order.id.slice(-8).toUpperCase()}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-blue-800">
                      Placed: {formatDate(order.createdAt)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
