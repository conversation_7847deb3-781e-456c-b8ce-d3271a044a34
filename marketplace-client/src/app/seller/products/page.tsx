"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Pagination, PaginationInfo } from "@/components/ui/pagination";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  useGetSellerProductsQuery,
  useDeleteProductMutation,
} from "@/lib/api/productsApi";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { ProductActionsMenu } from "@/components/ui/product-actions-menu";
import { ProductQuickActions } from "@/components/ui/product-quick-actions";
import { useNotifications } from "@/lib/hooks/useNotifications";
import Link from "next/link";
import { getFirstImageUrl } from "@/lib/utils/imageUtils";
import {
  Package,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  DollarSign,
  Calendar,
  Image as ImageIcon,
} from "lucide-react";

export default function SellerProductsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchType, setSearchType] = useState<"normal" | "fuzzy">("normal");
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<{
    id: string;
    title: string;
  } | null>(null);

  const itemsPerPage = 12;
  const {
    data: productsData,
    isLoading,
    error,
    refetch: refetchProducts,
  } = useGetSellerProductsQuery(
    {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      searchType,
      sortBy,
      sortOrder,
    },
    {
      // Force refetch to avoid cache issues
      refetchOnMountOrArgChange: true,
    }
  );
  const [deleteProduct, { isLoading: isDeleting }] = useDeleteProductMutation();
  const notifications = useNotifications();

  // Handle both array and object responses from the API
  const products = Array.isArray(productsData?.data)
    ? productsData.data
    : productsData?.data?.products && Array.isArray(productsData.data.products)
    ? productsData.data.products
    : [];

  // No need for client-side filtering since we're doing server-side search
  const filteredProducts = products;

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const handleDeleteClick = (product: { id: string; title: string }) => {
    setProductToDelete(product);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!productToDelete) return;

    try {
      await deleteProduct(productToDelete.id).unwrap();
      notifications.success("Product deleted successfully!");
      setDeleteDialogOpen(false);
      setProductToDelete(null);
    } catch (error: any) {
      notifications.error(error?.data?.message || "Failed to delete product");
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setProductToDelete(null);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Products</h1>
          <p className="text-gray-600">Manage your product catalog</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => refetchProducts()}
            className="bg-blue-50 hover:bg-blue-100"
          >
            🔄 Refresh
          </Button>
          <Button asChild>
            <Link href="/seller/products/new">
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Link>
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="space-y-4">
            {/* Search Row */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={
                    searchType === "fuzzy"
                      ? "Search products (fuzzy matching)..."
                      : "Search products..."
                  }
                  value={searchTerm}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select
                value={searchType}
                onValueChange={(value) =>
                  setSearchType(value as "normal" | "fuzzy")
                }
              >
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Search Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="normal">Normal Search</SelectItem>
                  <SelectItem value="fuzzy">Fuzzy Search</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Sort and Info Row */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <span>{filteredProducts.length} products</span>
              </div>

              <Select
                value={`${sortBy}-${sortOrder}`}
                onValueChange={(value) => {
                  const [newSortBy, newSortOrder] = value.split("-");
                  setSortBy(newSortBy);
                  setSortOrder(newSortOrder as "asc" | "desc");
                }}
              >
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt-desc">Newest First</SelectItem>
                  <SelectItem value="createdAt-asc">Oldest First</SelectItem>
                  <SelectItem value="price-asc">Price: Low to High</SelectItem>
                  <SelectItem value="price-desc">Price: High to Low</SelectItem>
                  <SelectItem value="title-asc">Name: A to Z</SelectItem>
                  <SelectItem value="title-desc">Name: Z to A</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Grid */}
      {error ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Package className="mx-auto h-12 w-12 text-red-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Error Loading Products
            </h3>
            <p className="text-gray-600 mb-4">
              {error?.data?.message ||
                "There was an error loading your products. Please try again."}
            </p>
            <div className="space-y-2">
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
              <p className="text-xs text-gray-500">
                If the problem persists, you may need to log in again.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="aspect-square bg-gray-200 rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded mb-2 w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : error ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Package className="mx-auto h-12 w-12 text-red-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Error Loading Products
            </h3>
            <p className="text-gray-600 mb-4">
              There was an error loading your products. Please try again.
            </p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </CardContent>
        </Card>
      ) : filteredProducts.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm ? "No products found" : "No products yet"}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm
                ? "Try adjusting your search terms"
                : "Start building your catalog by adding your first product"}
            </p>
            {!searchTerm && (
              <Button asChild>
                <Link href="/seller/products/new">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Your First Product
                </Link>
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map((product) => (
            <Card
              key={product.id}
              className="group hover:shadow-lg transition-shadow"
            >
              <CardContent className="p-0">
                {/* Product Image */}
                <div className="aspect-square relative overflow-hidden rounded-t-lg bg-gray-100">
                  {product.images && product.images.length > 0 ? (
                    <img
                      src={getFirstImageUrl(product.images)}
                      alt={product.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <ImageIcon className="h-12 w-12 text-gray-400" />
                    </div>
                  )}

                  {/* Action Menu */}
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="bg-white rounded-md shadow-sm">
                      <ProductActionsMenu
                        product={product}
                        onDelete={handleDeleteClick}
                        isLoading={isDeleting}
                      />
                    </div>
                  </div>
                </div>

                {/* Product Info */}
                <div className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-gray-900 line-clamp-2 flex-1">
                      {product.title}
                    </h3>
                  </div>

                  <div className="flex items-center justify-between mb-2">
                    <span className="text-lg font-bold text-green-600">
                      {formatPrice(product.price)}
                    </span>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">
                        {product.category?.name || "No Category"}
                      </Badge>
                      <Badge
                        variant={
                          product.quantity > 0 ? "default" : "destructive"
                        }
                        className="text-xs"
                      >
                        {product.quantity > 0
                          ? `${product.quantity} in stock`
                          : "Out of stock"}
                      </Badge>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                    {product.description}
                  </p>

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      {formatDate(product.createdAt)}
                    </div>
                    <ProductQuickActions
                      product={product}
                      onDelete={handleDeleteClick}
                      compact={true}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {productsData && productsData.totalPages > 1 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <PaginationInfo
                currentPage={productsData.currentPage}
                totalPages={productsData.totalPages}
                totalItems={productsData.totalItems}
                itemsPerPage={itemsPerPage}
              />
              <Pagination
                currentPage={productsData.currentPage}
                totalPages={productsData.totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Product"
        itemName={productToDelete?.title}
        isLoading={isDeleting}
      />
    </div>
  );
}
