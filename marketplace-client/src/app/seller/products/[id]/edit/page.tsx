"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ImageUpload } from "@/components/ui/image-upload";
import { getImageUrl } from "@/lib/utils/imageUtils";
import {
  useGetProductQuery,
  useUpdateProductMutation,
  useGetCategoriesQuery,
  productsApi,
} from "@/lib/api/productsApi";
import { useNotifications } from "@/lib/hooks/useNotifications";
import { ArrowLeft, Save, Package, Loader2 } from "lucide-react";
import Link from "next/link";
import { useDispatch } from "react-redux";

const productSchema = z
  .object({
    title: z
      .string()
      .min(1, "Product title is required")
      .min(3, "Title must be at least 3 characters"),
    description: z
      .string()
      .min(1, "Product description is required")
      .min(10, "Description must be at least 10 characters"),
    price: z.number().min(0.01, "Price must be greater than 0"),
    category: z.string().min(1, "Category is required"),
    quantity: z.number().min(0, "Quantity cannot be negative"),
    images: z.array(z.instanceof(File)).max(5, "Maximum 5 images allowed"),
    imagesToKeep: z.array(z.string()).optional(),
  })
  .refine(
    (data) => {
      const totalImages = (data.imagesToKeep?.length || 0) + data.images.length;
      return totalImages >= 1;
    },
    {
      message: "At least one image is required",
      path: ["images"],
    }
  );

type ProductFormData = z.infer<typeof productSchema>;

export default function EditProductPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;
  const notifications = useNotifications();

  // Debug: Log the product ID being used
  useEffect(() => {
    console.log("🆔 Product ID from URL params:", productId);
    console.log("🔗 Full URL params:", params);
  }, [productId, params]);

  const {
    data: productData,
    isLoading: isLoadingProduct,
    error,
    refetch: refetchProduct,
  } = useGetProductQuery(productId, {
    // Force refetch to avoid cache issues
    refetchOnMountOrArgChange: true,
  });
  const { data: categoriesData, isLoading: categoriesLoading } =
    useGetCategoriesQuery();
  const [updateProduct, { isLoading: isUpdating }] = useUpdateProductMutation();
  const dispatch = useDispatch();

  const categories = categoriesData?.data || [];

  const [newImages, setNewImages] = useState<File[]>([]);
  const [existingImages, setExistingImages] = useState<string[]>([]);

  // Debug function to manually refresh data
  const handleRefreshData = () => {
    console.log("🔄 Manually refreshing product data...");
    refetchProduct();
  };

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      title: "",
      description: "",
      price: 0,
      category: "",
      quantity: 0,
      images: [],
      imagesToKeep: [],
    },
  });

  const selectedCategory = watch("category");
  const watchedValues = watch();

  // Debug: Log form values
  useEffect(() => {
    console.log("📊 Current form values:", watchedValues);
  }, [watchedValues]);

  // Populate form when product data loads
  useEffect(() => {
    if (productData?.data) {
      const product = productData.data;
      console.log("🔄 Populating form with product data:", product);

      const formData = {
        title: product.title || "",
        description: product.description || "",
        //@ts-ignore
        price: parseFloat(product.price) || 0,
        category: product.category?.id || "",
        quantity: parseInt(product.quantity.toString()) || 0,
        images: [],
        imagesToKeep: product.images || [],
      };

      console.log("📝 Form data being set:", formData);
      reset(formData);
      setExistingImages(product.images || []);
    }
  }, [productData, reset]);

  const onSubmit = async (data: ProductFormData) => {
    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append("title", data.title);
      formData.append("description", data.description);
      formData.append("price", data.price.toString());
      formData.append("category", data.category);
      formData.append("quantity", data.quantity.toString());

      // Add images to keep
      existingImages.forEach((imageUrl) => {
        formData.append("imagesToKeep", imageUrl);
      });

      // Add new images
      newImages.forEach((image) => {
        formData.append("images", image);
      });

      const result = await updateProduct({ id: productId, formData }).unwrap();

      // Manually invalidate all product-related caches
      console.log("🧹 Manually clearing all product caches...");
      dispatch(productsApi.util.invalidateTags(["Product"]));
      dispatch(
        productsApi.util.invalidateTags([
          { type: "Product", id: "SELLER_LIST" },
        ])
      );
      dispatch(
        productsApi.util.invalidateTags([{ type: "Product", id: productId }])
      );

      // Also reset the entire cache for products API
      dispatch(productsApi.util.resetApiState());

      notifications.success("Product updated successfully!");
      router.push("/seller/products");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update product";
      notifications.error(errorMessage);
    }
  };

  const handleNewImagesChange = (files: File[]) => {
    setNewImages(files);
    setValue("images", files, { shouldValidate: true });
  };

  const handleRemoveExistingImage = (imageUrl: string) => {
    const updatedImages = existingImages.filter((img) => img !== imageUrl);
    setExistingImages(updatedImages);
    setValue("imagesToKeep", updatedImages, { shouldValidate: true });
  };

  if (isLoadingProduct) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading product...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/seller/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="p-8 text-center">
            <Package className="mx-auto h-12 w-12 text-red-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Product Not Found
            </h3>
            <p className="text-gray-600 mb-4">
              The product you're trying to edit could not be found.
            </p>
            <Button asChild>
              <Link href="/seller/products">Back to Products</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/seller/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit Product</h1>
            <p className="text-gray-600">Update your product information</p>
            <p className="text-xs text-gray-500">ID: {productId}</p>
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefreshData}
          className="bg-blue-50 hover:bg-blue-100"
        >
          🔄 Refresh Data
        </Button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="mr-2 h-5 w-5" />
                  Basic Information
                </CardTitle>
                <CardDescription>
                  Update the basic details of your product
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Product Title *</Label>
                  <Input
                    id="title"
                    {...register("title")}
                    placeholder="Enter product title"
                    className={errors.title ? "border-red-500" : ""}
                  />
                  {errors.title && (
                    <p className="text-sm text-red-600 mt-1">
                      {errors.title.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    {...register("description")}
                    placeholder="Describe your product in detail"
                    rows={4}
                    className={errors.description ? "border-red-500" : ""}
                  />
                  {errors.description && (
                    <p className="text-sm text-red-600 mt-1">
                      {errors.description.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="price">Price ($) *</Label>
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      min="0"
                      {...register("price", { valueAsNumber: true })}
                      placeholder="0.00"
                      className={errors.price ? "border-red-500" : ""}
                    />
                    {errors.price && (
                      <p className="text-sm text-red-600 mt-1">
                        {errors.price.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="quantity">Stock Quantity *</Label>
                    <Input
                      id="quantity"
                      type="number"
                      min="0"
                      {...register("quantity", { valueAsNumber: true })}
                      placeholder="0"
                      className={errors.quantity ? "border-red-500" : ""}
                    />
                    {errors.quantity && (
                      <p className="text-sm text-red-600 mt-1">
                        {errors.quantity.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="category">Category *</Label>
                    <Select
                      value={selectedCategory}
                      onValueChange={(value) =>
                        setValue("category", value, { shouldValidate: true })
                      }
                    >
                      <SelectTrigger
                        className={errors.category ? "border-red-500" : ""}
                      >
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categoriesLoading ? (
                          <SelectItem value="loading" disabled>
                            Loading categories...
                          </SelectItem>
                        ) : categories.length > 0 ? (
                          categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-categories" disabled>
                            No categories available
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    {errors.category && (
                      <p className="text-sm text-red-600 mt-1">
                        {errors.category.message}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Images */}
            <Card>
              <CardHeader>
                <CardTitle>Product Images</CardTitle>
                <CardDescription>Manage your product images</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Existing Images */}
                {existingImages.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium">
                      Current Images ({existingImages.length})
                    </Label>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      {existingImages.map((imageUrl, index) => (
                        <div key={index} className="relative group">
                          <div className="aspect-square rounded-lg overflow-hidden border border-gray-200">
                            <img
                              src={getImageUrl(imageUrl)}
                              alt={`Product image ${index + 1}`}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                console.error(
                                  `Failed to load image: ${getImageUrl(
                                    imageUrl
                                  )}`
                                );
                                e.currentTarget.src =
                                  "/placeholder-product.svg";
                              }}
                              onLoad={() => {
                                console.log(
                                  `Successfully loaded image: ${getImageUrl(
                                    imageUrl
                                  )}`
                                );
                              }}
                            />
                          </div>
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
                            onClick={() => handleRemoveExistingImage(imageUrl)}
                          >
                            ×
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Add New Images */}
                <div>
                  <Label className="text-sm font-medium">Add New Images</Label>
                  <div className="mt-2">
                    <ImageUpload
                      value={newImages}
                      onChange={handleNewImagesChange}
                      maxFiles={5 - existingImages.length}
                      maxSize={5}
                      className={errors.images ? "border-red-500" : ""}
                    />
                  </div>
                  {errors.images && (
                    <p className="text-sm text-red-600 mt-1">
                      {errors.images.message}
                    </p>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    You can have up to 5 images total. Currently:{" "}
                    {existingImages.length + newImages.length}/5
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isUpdating}
                  >
                    {isUpdating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Updating...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Update Product
                      </>
                    )}
                  </Button>

                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => router.push("/seller/products")}
                    disabled={isUpdating}
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
