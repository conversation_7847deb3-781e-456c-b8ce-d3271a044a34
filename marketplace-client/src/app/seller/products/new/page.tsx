"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ImageUpload } from "@/components/ui/image-upload";
import { ImagePreview } from "@/components/ui/image-preview";
import {
  useCreateProductMutation,
  useGetCategoriesQuery,
} from "@/lib/api/productsApi";
import { useNotifications } from "@/lib/hooks/useNotifications";
import {
  ArrowLeft,
  Save,
  Package,
  ImageIcon,
  X,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import Link from "next/link";

const productSchema = z.object({
  title: z
    .string()
    .min(1, "Product title is required")
    .min(3, "Title must be at least 3 characters"),
  description: z
    .string()
    .min(1, "Product description is required")
    .min(10, "Description must be at least 10 characters"),
  price: z.number().min(0.01, "Price must be greater than 0"),
  category: z.string().min(1, "Category is required"),
  quantity: z.number().min(0, "Quantity cannot be negative"),
  images: z
    .array(z.instanceof(File))
    .min(1, "At least one image is required")
    .max(5, "Maximum 5 images allowed"),
});

type ProductFormData = z.infer<typeof productSchema>;

export default function CreateProductPage() {
  const router = useRouter();
  const notifications = useNotifications();
  const [createProduct, { isLoading }] = useCreateProductMutation();
  const { data: categoriesData, isLoading: categoriesLoading } =
    useGetCategoriesQuery();
  const [images, setImages] = useState<File[]>([]);

  const categories = categoriesData?.data || [];

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      title: "",
      description: "",
      price: 0,
      category: "",
      quantity: 1,
      images: [],
    },
  });

  const selectedCategory = watch("category");

  const onSubmit = async (data: ProductFormData) => {
    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append("title", data.title);
      formData.append("description", data.description);
      formData.append("price", data.price.toString());
      formData.append("category", data.category);
      formData.append("quantity", data.quantity.toString());

      // Append images
      data.images.forEach((image, index) => {
        formData.append("images", image);
      });

      const result = await createProduct(formData).unwrap();

      notifications.success("Product created successfully!");
      router.push("/seller/products");
    } catch (error: any) {
      notifications.error(error?.data?.message || "Failed to create product");
    }
  };

  const handleImagesChange = (newImages: File[]) => {
    setImages(newImages);
    setValue("images", newImages, { shouldValidate: true });
  };

  const handleImageReorder = (fromIndex: number, toIndex: number) => {
    const newImages = [...images];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);
    handleImagesChange(newImages);
  };

  const handleRemoveImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    handleImagesChange(newImages);
  };

  return (
    <div className="marketplace-theme space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl marketplace-heading mb-4">
          Create New <span className="marketplace-heading-accent">Product</span>
        </h1>
        <p className="text-lg marketplace-text-muted mb-6">
          Add a new product to your catalog
        </p>
        <Button
          variant="outline"
          size="sm"
          asChild
          className="marketplace-btn-outline"
        >
          <Link href="/seller/products">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Products
          </Link>
        </Button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="flex items-center marketplace-text-primary">
                  <Package className="mr-2 h-5 w-5" />
                  Basic Information
                </CardTitle>
                <CardDescription className="marketplace-text-muted">
                  Enter the basic details of your product
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Product Title *</Label>
                  <Input
                    id="title"
                    {...register("title")}
                    placeholder="Enter product title"
                    className={errors.title ? "border-red-500" : ""}
                  />
                  {errors.title && (
                    <p className="text-sm text-red-600 mt-1">
                      {errors.title.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    {...register("description")}
                    placeholder="Describe your product in detail"
                    rows={4}
                    className={errors.description ? "border-red-500" : ""}
                  />
                  {errors.description && (
                    <p className="text-sm text-red-600 mt-1">
                      {errors.description.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="price">Price ($) *</Label>
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      min="0"
                      {...register("price", { valueAsNumber: true })}
                      placeholder="0.00"
                      className={errors.price ? "border-red-500" : ""}
                    />
                    {errors.price && (
                      <p className="text-sm text-red-600 mt-1">
                        {errors.price.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="quantity">Stock Quantity *</Label>
                    <Input
                      id="quantity"
                      type="number"
                      min="0"
                      {...register("quantity", { valueAsNumber: true })}
                      placeholder="0"
                      className={errors.quantity ? "border-red-500" : ""}
                    />
                    {errors.quantity && (
                      <p className="text-sm text-red-600 mt-1">
                        {errors.quantity.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="category">Category *</Label>
                    <Select
                      value={selectedCategory}
                      onValueChange={(value) =>
                        setValue("category", value, { shouldValidate: true })
                      }
                    >
                      <SelectTrigger
                        className={errors.category ? "border-red-500" : ""}
                      >
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categoriesLoading ? (
                          <SelectItem value="loading" disabled>
                            Loading categories...
                          </SelectItem>
                        ) : categories.length > 0 ? (
                          categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-categories" disabled>
                            No categories available
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    {errors.category && (
                      <p className="text-sm text-red-600 mt-1">
                        {errors.category.message}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Image Preview Section */}
            {images.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <ImageIcon className="mr-2 h-5 w-5" />
                    Image Preview ({images.length}/5)
                  </CardTitle>
                  <CardDescription>
                    Preview of your product images
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {images.map((file, index) => (
                      <div key={index} className="relative group">
                        <div className="relative border-2 border-gray-200 hover:border-gray-300 transition-colors rounded-lg overflow-hidden">
                          <ImagePreview
                            file={file}
                            alt={`Product image ${index + 1}`}
                            className="w-full"
                            aspectRatio="square"
                          />

                          {/* Overlay with controls */}
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center gap-2">
                            {/* Move left button */}
                            {index > 0 && (
                              <Button
                                type="button"
                                variant="secondary"
                                size="sm"
                                className="opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() =>
                                  handleImageReorder(index, index - 1)
                                }
                                title="Move left"
                              >
                                <ChevronLeft className="h-4 w-4" />
                              </Button>
                            )}
                            {/* Remove button */}
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              className="opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={() => handleRemoveImage(index)}
                              title="Remove image"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                            {/* Move right button */}
                            {index < images.length - 1 && (
                              <Button
                                type="button"
                                variant="secondary"
                                size="sm"
                                className="opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() =>
                                  handleImageReorder(index, index + 1)
                                }
                                title="Move right"
                              >
                                <ChevronRight className="h-4 w-4" />
                              </Button>
                            )}
                          </div>

                          {/* Primary image indicator */}
                          {index === 0 && (
                            <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded z-10">
                              Primary
                            </div>
                          )}

                          {/* Image number */}
                          <div className="absolute bottom-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded z-10">
                            {index + 1}
                          </div>
                        </div>

                        {/* File name */}
                        <p
                          className="text-xs text-gray-500 mt-1 truncate text-center"
                          title={file.name}
                        >
                          {file.name}
                        </p>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>Tip:</strong> The first image will be used as the
                      primary product image. Hover over images to reorder them
                      using the arrow buttons, or remove unwanted images with
                      the × button.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Images */}
            <Card>
              <CardHeader>
                <CardTitle>Product Images *</CardTitle>
                <CardDescription>
                  Upload up to 5 high-quality images
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ImageUpload
                  value={images}
                  onChange={handleImagesChange}
                  maxFiles={5}
                  maxSize={5}
                  className={errors.images ? "border-red-500" : ""}
                />
                {errors.images && (
                  <p className="text-sm text-red-600 mt-2">
                    {errors.images.message}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    <Save className="mr-2 h-4 w-4" />
                    {isLoading ? "Creating..." : "Create Product"}
                  </Button>

                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => router.push("/seller/products")}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
