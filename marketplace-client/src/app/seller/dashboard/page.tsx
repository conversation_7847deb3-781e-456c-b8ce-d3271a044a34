"use client";

import { useAuth } from "@/lib/contexts/AuthContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import {
  Package,
  ShoppingCart,
  DollarSign,
  TrendingUp,
  Plus,
  Eye,
  MessageSquare,
  BarChart3,
} from "lucide-react";
import { LowStockAlert } from "@/components/inventory/LowStockAlert";

export default function SellerDashboard() {
  const { user } = useAuth();

  // Mock data - in real app, this would come from API
  const stats = {
    totalProducts: 12,
    totalOrders: 45,
    totalRevenue: 2850.5,
    pendingOrders: 8,
  };

  const recentOrders = [
    {
      id: "ORD-001",
      customer: "John <PERSON>",
      product: "iPhone 15 Pro",
      amount: 1299.99,
      status: "pending",
      date: "2024-01-15",
    },
    {
      id: "ORD-002",
      customer: "<PERSON>",
      product: "MacBook Air",
      amount: 1199.99,
      status: "shipped",
      date: "2024-01-14",
    },
    {
      id: "ORD-003",
      customer: "<PERSON> Johnson",
      product: "AirPods Pro",
      amount: 249.99,
      status: "delivered",
      date: "2024-01-13",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "text-orange-700 bg-orange-100";
      case "shipped":
        return "text-indigo-700 bg-indigo-100";
      case "delivered":
        return "text-green-700 bg-green-100";
      default:
        return "text-gray-700 bg-gray-100";
    }
  };

  return (
    <div className="marketplace-theme space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl marketplace-heading mb-4">
          Seller <span className="marketplace-heading-accent">Dashboard</span>
        </h1>
        <p className="text-lg marketplace-text-muted mb-6">
          Welcome back, {user?.name}!
        </p>
        <div className="flex justify-center">
          <Button asChild className="marketplace-btn-primary">
            <Link href="/seller/products/new">
              <Plus className="mr-2 h-4 w-4" />
              Add New Product
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Products
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              <Link
                href="/seller/products"
                className="text-blue-600 hover:underline"
              >
                View all products
              </Link>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">
                {stats.pendingOrders} pending
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Intl.NumberFormat("en-KE", {
                style: "currency",
                currency: "KES",
              }).format(stats.totalRevenue)}
            </div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+12% from last month</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Growth</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+12%</div>
            <p className="text-xs text-muted-foreground">
              <Link
                href="/seller/analytics"
                className="text-blue-600 hover:underline"
              >
                View analytics
              </Link>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Low Stock Alert */}
      <LowStockAlert maxItems={3} />

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="mr-2 h-5 w-5" />
              Product Management
            </CardTitle>
            <CardDescription>Manage your product catalog</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button asChild variant="outline" className="w-full">
              <Link href="/seller/products">
                <Eye className="mr-2 h-4 w-4" />
                View Products
              </Link>
            </Button>
            <Button asChild className="w-full">
              <Link href="/seller/products/new">
                <Plus className="mr-2 h-4 w-4" />
                Add New Product
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ShoppingCart className="mr-2 h-5 w-5" />
              Order Management
            </CardTitle>
            <CardDescription>Track and fulfill orders</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button asChild variant="outline" className="w-full">
              <Link href="/seller/orders">
                <Eye className="mr-2 h-4 w-4" />
                View Orders
              </Link>
            </Button>
            <div className="text-sm text-gray-600">
              {stats.pendingOrders} orders need attention
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquare className="mr-2 h-5 w-5" />
              Customer Communication
            </CardTitle>
            <CardDescription>Connect with your customers</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button asChild variant="outline" className="w-full">
              <Link href="/seller/messages">
                <MessageSquare className="mr-2 h-4 w-4" />
                View Messages
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full">
              <Link href="/seller/analytics">
                <BarChart3 className="mr-2 h-4 w-4" />
                View Analytics
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Orders */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Orders</CardTitle>
          <CardDescription>Your latest customer orders</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentOrders.map((order) => (
              <div
                key={order.id}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-4">
                    <div>
                      <p className="font-medium">{order.id}</p>
                      <p className="text-sm text-gray-600">{order.customer}</p>
                    </div>
                    <div>
                      <p className="text-sm">{order.product}</p>
                      <p className="text-sm text-gray-600">{order.date}</p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
                      order.status
                    )}`}
                  >
                    {order.status}
                  </span>
                  <p className="font-medium">${order.amount}</p>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4">
            <Button asChild variant="outline" className="w-full">
              <Link href="/seller/orders">View All Orders</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
