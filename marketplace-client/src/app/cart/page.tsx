"use client";

import { useCart } from "@/lib/contexts/CartContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  ArrowRight,
  Package,
  ArrowLeft,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useAuth } from "@/lib/contexts/AuthContext";
import { getFirstImageUrl } from "@/lib/utils/imageUtils";
import { OptimisticQuantityControl } from "@/components/cart/OptimisticQuantityControl";

export default function CartPage() {
  const {
    items,
    itemCount,
    total,
    isLoading,
    updateCartItem,
    removeFromCart,
    clearCart,
  } = useCart();
  const { isAuthenticated } = useAuth();

  const handleQuantityChange = async (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      await removeFromCart(itemId);
    } else {
      await updateCartItem(itemId, newQuantity);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
    }).format(price);
  };

  if (!isAuthenticated) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <ShoppingCart className="mx-auto h-16 w-16 marketplace-text-muted mb-6" />
            <h1 className="text-3xl marketplace-heading mb-4">
              Sign In to View Your Cart
            </h1>
            <p className="marketplace-text-muted mb-8">
              Please sign in to access your shopping cart and continue shopping.
            </p>
            <div className="space-x-4">
              <Button asChild className="marketplace-btn-primary">
                <Link href="/auth/login">Sign In</Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="marketplace-btn-outline"
              >
                <Link href="/products">Browse Products</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="marketplace-theme min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl marketplace-heading mb-2">
              Shopping <span className="marketplace-heading-accent">Cart</span>
            </h1>
            <p className="marketplace-text-muted">
              {itemCount === 0
                ? "Your cart is empty"
                : `${itemCount} item${itemCount !== 1 ? "s" : ""} in your cart`}
            </p>
          </div>
          <Button asChild variant="outline" className="marketplace-btn-outline">
            <Link href="/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Continue Shopping
            </Link>
          </Button>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        ) : items.length === 0 ? (
          <div className="text-center py-16">
            <Package className="mx-auto h-16 w-16 marketplace-text-muted mb-6" />
            <h2 className="text-2xl marketplace-heading mb-4">
              Your cart is empty
            </h2>
            <p className="marketplace-text-muted mb-8 max-w-md mx-auto">
              Looks like you haven't added any items to your cart yet. Start
              shopping to fill it up!
            </p>
            <Button asChild className="marketplace-btn-primary">
              <Link href="/products">
                Browse Products
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl marketplace-text-primary font-semibold">
                  Cart Items
                </h2>
                <Button
                  variant="ghost"
                  onClick={clearCart}
                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Clear Cart
                </Button>
              </div>

              {items.map((item) => (
                <Card key={item.id} className="marketplace-card">
                  <CardContent className="p-6">
                    <div className="flex gap-4">
                      {/* Product Image */}
                      <div className="relative w-24 h-24 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                        {item.product.images &&
                        item.product.images.length > 0 ? (
                          <Image
                            src={getFirstImageUrl(item.product.images)}
                            alt={item.product.title}
                            fill
                            className="object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Package className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                      </div>

                      {/* Product Details */}
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-semibold marketplace-text-primary text-lg mb-1">
                              {item.product.title}
                            </h3>
                            <p className="marketplace-text-muted mb-2">
                              Sold by {item.product.seller.name}
                            </p>
                            <div className="text-xl font-bold marketplace-text-warm">
                              {formatPrice(item.product.price)}
                            </div>
                          </div>

                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => removeFromCart(item.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="flex items-center justify-between mt-4">
                          {/* Optimistic Quantity Controls */}
                          <div className="flex items-center gap-4">
                            <span className="marketplace-text-muted font-medium">
                              Quantity:
                            </span>
                            <OptimisticQuantityControl
                              itemId={item.id}
                              initialQuantity={item.quantity}
                              onQuantityChange={(quantity) => {}}
                              disabled={item.quantity >= item.product.quantity}
                            />
                          </div>

                          {/* Item Total */}
                          <div className="text-right">
                            <div className="text-lg font-bold marketplace-text-primary">
                              {formatPrice(item.product.price * item.quantity)}
                            </div>
                            {item.quantity > 1 && (
                              <div className="text-sm marketplace-text-muted">
                                {formatPrice(item.product.price)} each
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Stock Warning */}
                        {item.product.quantity < 5 && (
                          <div className="mt-3 p-2 bg-orange-50 border border-orange-200 rounded-md">
                            <p className="text-sm text-orange-700">
                              ⚠️ Only {item.product.quantity} left in stock
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <Card className="marketplace-card sticky top-4">
                <CardHeader>
                  <CardTitle className="marketplace-text-primary">
                    Order Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="marketplace-text-muted">
                        Subtotal ({itemCount} items)
                      </span>
                      <span className="marketplace-text-primary">
                        {formatPrice(total)}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="marketplace-text-muted">Shipping</span>
                      <span className="marketplace-text-primary">
                        Calculated at checkout
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="marketplace-text-muted">Tax</span>
                      <span className="marketplace-text-primary">
                        Calculated at checkout
                      </span>
                    </div>
                  </div>

                  <Separator />

                  <div className="flex justify-between text-lg font-semibold">
                    <span className="marketplace-text-primary">Total</span>
                    <span className="marketplace-text-warm">
                      {formatPrice(total)}
                    </span>
                  </div>

                  <Button asChild className="w-full marketplace-btn-primary">
                    <Link href="/checkout">
                      Proceed to Checkout
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>

                  <div className="text-center">
                    <Button
                      asChild
                      variant="ghost"
                      className="marketplace-text-muted"
                    >
                      <Link href="/products">Continue Shopping</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
