"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useGetProductsQuery,
  useGetCategoriesQuery,
} from "@/lib/api/productsApi";
import PredictiveSearchInput from "@/components/search/PredictiveSearchInput";
import LoadingSpinner from "@/components/common/LoadingSpinner";
import {
  Search,
  Star,
  ShoppingCart,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { getFirstImageUrl } from "@/lib/utils/imageUtils";
import { useCart } from "@/lib/contexts/CartContext";
import { useAuth } from "@/lib/contexts/AuthContext";
import { toast } from "sonner";

export default function ProductsPage() {
  const [search, setSearch] = useState("");
  const [searchType, setSearchType] = useState<
    "normal" | "predictive" | "fuzzy"
  >("normal");
  const [category, setCategory] = useState("");
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [page, setPage] = useState(1);
  const [minPrice, setMinPrice] = useState<number | undefined>();
  const [maxPrice, setMaxPrice] = useState<number | undefined>();
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [addingToCart, setAddingToCart] = useState<string | null>(null);

  const { addToCart } = useCart();
  const { isAuthenticated } = useAuth();

  const handleAddToCart = async (productId: string) => {
    if (!isAuthenticated) {
      toast.error("Please sign in to add items to cart");
      return;
    }

    setAddingToCart(productId);
    try {
      await addToCart(productId, 1);
      toast.success("Product added to cart!");
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to add to cart"
      );
    } finally {
      setAddingToCart(null);
    }
  };

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
    }, 300);

    return () => clearTimeout(timer);
  }, [search]);

  // Reset page when filters change
  useEffect(() => {
    setPage(1);
  }, [
    debouncedSearch,
    category,
    sortBy,
    sortOrder,
    minPrice,
    maxPrice,
    searchType,
  ]);

  const {
    data: productsData,
    isLoading: productsLoading,
    error: productsError,
  } = useGetProductsQuery({
    search: debouncedSearch || undefined,
    searchType,
    category: category || undefined,
    sortBy,
    sortOrder,
    page,
    limit: 12,
    minPrice,
    maxPrice,
  });

  const { data: categoriesData } = useGetCategoriesQuery();

  if (productsLoading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <LoadingSpinner size="lg" className="min-h-[400px]" />
        </div>
      </Layout>
    );
  }

  if (productsError) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">
              Error Loading Products
            </h1>
            <p className="text-gray-600">Please try again later.</p>
          </div>
        </div>
      </Layout>
    );
  }

  const products = productsData?.data || [];
  const categories = categoriesData?.data || [];
  const pagination = {
    currentPage: productsData?.currentPage || 1,
    totalItems: productsData?.totalItems || 0,
    totalPages: productsData?.totalPages || 1,
  };

  return (
    <div className="marketplace-theme">
      <Layout>
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="text-4xl marketplace-heading mb-4">
              Discover{" "}
              <span className="marketplace-heading-accent">
                Premium Products
              </span>
            </h1>
            <p className="text-lg marketplace-text-muted max-w-2xl mx-auto">
              Explore our curated collection of quality products from trusted
              sellers worldwide
            </p>
          </div>

          {/* Filters */}
          <div className="marketplace-card p-6 mb-8 space-y-4">
            {/* Search Row */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                {searchType === "predictive" ? (
                  <PredictiveSearchInput
                    value={search}
                    onChange={setSearch}
                    placeholder="Search products with suggestions..."
                  />
                ) : (
                  <>
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 marketplace-text-muted h-4 w-4" />
                    <Input
                      placeholder={
                        searchType === "fuzzy"
                          ? "Search products (fuzzy matching)..."
                          : "Search products..."
                      }
                      value={search}
                      onChange={(e) => setSearch(e.target.value)}
                      className="pl-10 border-gray-300 focus:border-orange-400"
                    />
                  </>
                )}
              </div>
              <Select
                value={searchType}
                onValueChange={(value) =>
                  setSearchType(value as "normal" | "predictive" | "fuzzy")
                }
              >
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Search Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="normal">Normal Search</SelectItem>
                  <SelectItem value="predictive">Predictive Search</SelectItem>
                  <SelectItem value="fuzzy">Fuzzy Search</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Filters Row */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex gap-2 flex-1">
                <Input
                  type="number"
                  placeholder="Min Price"
                  value={minPrice || ""}
                  onChange={(e) =>
                    setMinPrice(
                      e.target.value ? Number(e.target.value) : undefined
                    )
                  }
                  className="w-full"
                />
                <Input
                  type="number"
                  placeholder="Max Price"
                  value={maxPrice || ""}
                  onChange={(e) =>
                    setMaxPrice(
                      e.target.value ? Number(e.target.value) : undefined
                    )
                  }
                  className="w-full"
                />
              </div>

              <Select
                value={category || "all"}
                onValueChange={(value) =>
                  setCategory(value === "all" ? "" : value)
                }
              >
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map((cat) => (
                    <SelectItem key={cat.id} value={cat.id}>
                      {cat.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={`${sortBy}-${sortOrder}`}
                onValueChange={(value) => {
                  const [newSortBy, newSortOrder] = value.split("-");
                  setSortBy(newSortBy);
                  setSortOrder(newSortOrder as "asc" | "desc");
                }}
              >
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt-desc">Newest First</SelectItem>
                  <SelectItem value="createdAt-asc">Oldest First</SelectItem>
                  <SelectItem value="price-asc">Price: Low to High</SelectItem>
                  <SelectItem value="price-desc">Price: High to Low</SelectItem>
                  <SelectItem value="title-asc">Name: A to Z</SelectItem>
                  <SelectItem value="title-desc">Name: Z to A</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Products Grid */}
          {products.length === 0 ? (
            <div className="text-center py-12">
              <h3 className="text-lg font-semibold marketplace-text-primary mb-2">
                No products found
              </h3>
              <p className="marketplace-text-muted">
                Try adjusting your search or filters
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {products.map((product) => (
                <Card
                  key={product.id}
                  className="marketplace-card marketplace-hover-lift group"
                >
                  <CardHeader className="p-0">
                    <div className="relative aspect-square overflow-hidden rounded-t-lg">
                      <Image
                        src={getFirstImageUrl(product.images)}
                        alt={product.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform"
                      />
                      {product.featured && (
                        <Badge className="absolute top-2 left-2 bg-orange-500">
                          Featured
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <CardTitle className="text-lg mb-2 line-clamp-2">
                      <Link
                        href={`/products/${product.id}`}
                        className="marketplace-text-primary hover:marketplace-text-warm transition-colors"
                      >
                        {product.title}
                      </Link>
                    </CardTitle>
                    <CardDescription className="mb-3 line-clamp-2 marketplace-text-muted">
                      {product.description}
                    </CardDescription>

                    {(product.rating || product.reviewCount) && (
                      <div className="flex items-center gap-1 mb-3">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm font-medium">
                          {product.rating || "N/A"}
                        </span>
                        <span className="text-sm text-gray-500">
                          ({product.reviewCount || 0})
                        </span>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-2xl font-bold marketplace-text-warm">
                          {new Intl.NumberFormat("en-KE", {
                            style: "currency",
                            currency: "KES",
                          }).format(product.price)}
                        </span>
                        {product.originalPrice && (
                          <span className="text-sm marketplace-text-muted line-through ml-2">
                            {new Intl.NumberFormat("en-KE", {
                              style: "currency",
                              currency: "KES",
                            }).format(product.originalPrice)}
                          </span>
                        )}
                      </div>
                      <Button
                        size="sm"
                        className="marketplace-btn-primary"
                        onClick={() => handleAddToCart(product.id)}
                        disabled={
                          addingToCart === product.id || product.quantity === 0
                        }
                      >
                        {addingToCart === product.id ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                        ) : (
                          <ShoppingCart className="h-4 w-4 mr-1" />
                        )}
                        {product.quantity === 0
                          ? "Out of Stock"
                          : "Add to Cart"}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Search Results Info & Pagination */}
          {products.length > 0 && (
            <div className="mt-8 space-y-4">
              {/* Results Info */}
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                <div className="text-sm text-gray-600">
                  Showing {(pagination.currentPage - 1) * 12 + 1} to{" "}
                  {Math.min(pagination.currentPage * 12, pagination.totalItems)}{" "}
                  of {pagination.totalItems} products
                  {debouncedSearch && (
                    <span className="ml-2">
                      for "<strong>{debouncedSearch}</strong>" ({searchType}{" "}
                      search)
                    </span>
                  )}
                </div>

                {/* Clear Filters */}
                {(debouncedSearch || category || minPrice || maxPrice) && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="marketplace-btn-outline"
                    onClick={() => {
                      setSearch("");
                      setCategory("");
                      setMinPrice(undefined);
                      setMaxPrice(undefined);
                      setPage(1);
                    }}
                  >
                    Clear Filters
                  </Button>
                )}
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="flex justify-center items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page - 1)}
                    disabled={page <= 1}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Previous
                  </Button>

                  <div className="flex space-x-1">
                    {pagination.totalPages > 0 &&
                      Array.from(
                        { length: Math.min(5, pagination.totalPages) },
                        (_, i) => {
                          let pageNum;
                          if (pagination.totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (page <= 3) {
                            pageNum = i + 1;
                          } else if (page >= pagination.totalPages - 2) {
                            pageNum = Math.max(
                              1,
                              pagination.totalPages - 4 + i
                            );
                          } else {
                            pageNum = Math.max(1, page - 2 + i);
                          }

                          // Ensure pageNum is within valid range
                          if (pageNum > pagination.totalPages) return null;

                          return (
                            <Button
                              key={pageNum}
                              variant={page === pageNum ? "default" : "outline"}
                              size="sm"
                              onClick={() => setPage(pageNum)}
                              className="w-10"
                            >
                              {pageNum}
                            </Button>
                          );
                        }
                      ).filter(Boolean)}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page + 1)}
                    disabled={page >= pagination.totalPages}
                  >
                    Next
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </Layout>
    </div>
  );
}
