"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useCart } from "@/lib/contexts/CartContext";
import { useAuth } from "@/lib/contexts/AuthContext";
import { useNotifications } from "@/lib/hooks/useNotifications";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  ShoppingCart,
  Package,
  ArrowLeft,
  CreditCard,
  Truck,
  Shield,
  CheckCircle,
  AlertCircle,
  MapPin,
  Phone,
  User,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { getFirstImageUrl } from "@/lib/utils/imageUtils";

interface ShippingAddress {
  fullName: string;
  phone: string;
  street: string;
  city: string;
  county: string;
  postalCode: string;
  country: string;
}

export default function CheckoutPage() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isLoadingAddress, setIsLoadingAddress] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState("mpesa");
  const [notes, setNotes] = useState("");
  const [useDefaultAddress, setUseDefaultAddress] = useState(true);
  const [shippingAddress, setShippingAddress] = useState<ShippingAddress>({
    fullName: "",
    phone: "",
    street: "",
    city: "",
    county: "",
    postalCode: "",
    country: "Kenya",
  });

  const { items, itemCount, total, clearCart } = useCart();
  const { isAuthenticated, token } = useAuth();
  const notifications = useNotifications();
  const router = useRouter();

  // Load user's default shipping address on component mount
  useEffect(() => {
    const loadDefaultAddress = async () => {
      if (!isAuthenticated || !token) {
        setIsLoadingAddress(false);
        return;
      }

      try {
        const API_BASE =
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

        const response = await fetch(`${API_BASE}/orders/shipping-address`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          if (data.data) {
            setShippingAddress(data.data);
            setUseDefaultAddress(true);
          } else {
            setUseDefaultAddress(false);
          }
        }
      } catch (error) {
        console.error("Failed to load default address:", error);
        setUseDefaultAddress(false);
      } finally {
        setIsLoadingAddress(false);
      }
    };

    loadDefaultAddress();
  }, [isAuthenticated, token]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
    }).format(price);
  };

  const validateForm = () => {
    const requiredFields = [
      "fullName",
      "phone",
      "street",
      "city",
      "county",
      "postalCode",
    ] as const;

    for (const field of requiredFields) {
      if (!shippingAddress[field].trim()) {
        setError(
          `Please fill in ${field.replace(/([A-Z])/g, " $1").toLowerCase()}`
        );
        return false;
      }
    }

    if (!paymentMethod) {
      setError("Please select a payment method");
      return false;
    }

    return true;
  };

  const handleCheckout = async () => {
    if (!isAuthenticated || !token) {
      router.push("/auth/login");
      return;
    }

    if (items.length === 0) {
      setError("Your cart is empty");
      return;
    }

    if (!validateForm()) {
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const API_BASE =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

      const response = await fetch(`${API_BASE}/orders/checkout`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          shippingAddress,
          paymentMethod,
          notes: notes.trim() || undefined,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Checkout failed");
      }

      const orderData = await response.json();

      // Show success notification
      notifications.success(
        "Order placed successfully! Check your email for confirmation."
      );

      // Clear cart after successful order
      await clearCart();

      // Redirect to order confirmation
      router.push(`/orders/${orderData.data.id}`);
    } catch (error) {
      console.error("Checkout error:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Checkout failed. Please try again.";

      setError(errorMessage);
      notifications.error(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <ShoppingCart className="mx-auto h-16 w-16 marketplace-text-muted mb-6" />
            <h1 className="text-3xl marketplace-heading mb-4">
              Sign In to Checkout
            </h1>
            <p className="marketplace-text-muted mb-8">
              Please sign in to complete your purchase.
            </p>
            <div className="space-x-4">
              <Button asChild className="marketplace-btn-primary">
                <Link href="/auth/login">Sign In</Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="marketplace-btn-outline"
              >
                <Link href="/cart">Back to Cart</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div className="marketplace-theme min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <Package className="mx-auto h-16 w-16 marketplace-text-muted mb-6" />
            <h1 className="text-3xl marketplace-heading mb-4">
              Your Cart is Empty
            </h1>
            <p className="marketplace-text-muted mb-8">
              Add some items to your cart before checking out.
            </p>
            <div className="space-x-4">
              <Button asChild className="marketplace-btn-primary">
                <Link href="/products">Browse Products</Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="marketplace-btn-outline"
              >
                <Link href="/cart">View Cart</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="marketplace-theme min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl marketplace-heading mb-2">
              <span className="marketplace-heading-accent">Checkout</span>
            </h1>
            <p className="marketplace-text-muted">
              Review your order and complete your purchase
            </p>
          </div>
          <Button asChild variant="outline" className="marketplace-btn-outline">
            <Link href="/cart">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Cart
            </Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Items */}
          <div className="lg:col-span-2 space-y-6">
            {/* Shipping Address */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary flex items-center">
                  <MapPin className="mr-2 h-5 w-5" />
                  Shipping Address
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Address Options */}
                {!isLoadingAddress && shippingAddress.fullName && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="useDefault"
                        name="addressOption"
                        checked={useDefaultAddress}
                        onChange={() => setUseDefaultAddress(true)}
                        className="text-orange-600"
                      />
                      <Label
                        htmlFor="useDefault"
                        className="marketplace-text-primary"
                      >
                        Use saved address
                      </Label>
                    </div>

                    {useDefaultAddress && (
                      <div className="bg-gray-50 p-4 rounded-lg border">
                        <div className="text-sm marketplace-text-primary">
                          <strong>{shippingAddress.fullName}</strong>
                          <br />
                          {shippingAddress.phone}
                          <br />
                          {shippingAddress.street}
                          <br />
                          {shippingAddress.city}, {shippingAddress.county}
                          <br />
                          {shippingAddress.postalCode},{" "}
                          {shippingAddress.country}
                        </div>
                      </div>
                    )}

                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="useNew"
                        name="addressOption"
                        checked={!useDefaultAddress}
                        onChange={() => setUseDefaultAddress(false)}
                        className="text-orange-600"
                      />
                      <Label
                        htmlFor="useNew"
                        className="marketplace-text-primary"
                      >
                        Enter new address
                      </Label>
                    </div>
                  </div>
                )}

                {/* Address Form - Show if no default address or user wants to enter new */}
                {(isLoadingAddress ||
                  !useDefaultAddress ||
                  !shippingAddress.fullName) && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label
                          htmlFor="fullName"
                          className="marketplace-text-primary"
                        >
                          Full Name *
                        </Label>
                        <Input
                          id="fullName"
                          value={shippingAddress.fullName}
                          onChange={(e) =>
                            setShippingAddress({
                              ...shippingAddress,
                              fullName: e.target.value,
                            })
                          }
                          placeholder="Enter your full name"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label
                          htmlFor="phone"
                          className="marketplace-text-primary"
                        >
                          Phone Number *
                        </Label>
                        <Input
                          id="phone"
                          value={shippingAddress.phone}
                          onChange={(e) =>
                            setShippingAddress({
                              ...shippingAddress,
                              phone: e.target.value,
                            })
                          }
                          placeholder="e.g., +254 700 000 000"
                          className="mt-1"
                        />
                      </div>
                    </div>

                    <div>
                      <Label
                        htmlFor="street"
                        className="marketplace-text-primary"
                      >
                        Street Address *
                      </Label>
                      <Input
                        id="street"
                        value={shippingAddress.street}
                        onChange={(e) =>
                          setShippingAddress({
                            ...shippingAddress,
                            street: e.target.value,
                          })
                        }
                        placeholder="Enter your street address"
                        className="mt-1"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label
                          htmlFor="city"
                          className="marketplace-text-primary"
                        >
                          City *
                        </Label>
                        <Input
                          id="city"
                          value={shippingAddress.city}
                          onChange={(e) =>
                            setShippingAddress({
                              ...shippingAddress,
                              city: e.target.value,
                            })
                          }
                          placeholder="e.g., Nairobi"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label
                          htmlFor="county"
                          className="marketplace-text-primary"
                        >
                          County *
                        </Label>
                        <Input
                          id="county"
                          value={shippingAddress.county}
                          onChange={(e) =>
                            setShippingAddress({
                              ...shippingAddress,
                              county: e.target.value,
                            })
                          }
                          placeholder="e.g., Nairobi County"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label
                          htmlFor="postalCode"
                          className="marketplace-text-primary"
                        >
                          Postal Code *
                        </Label>
                        <Input
                          id="postalCode"
                          value={shippingAddress.postalCode}
                          onChange={(e) =>
                            setShippingAddress({
                              ...shippingAddress,
                              postalCode: e.target.value,
                            })
                          }
                          placeholder="e.g., 00100"
                          className="mt-1"
                        />
                      </div>
                    </div>

                    <div>
                      <Label
                        htmlFor="country"
                        className="marketplace-text-primary"
                      >
                        Country
                      </Label>
                      <Input
                        id="country"
                        value={shippingAddress.country}
                        onChange={(e) =>
                          setShippingAddress({
                            ...shippingAddress,
                            country: e.target.value,
                          })
                        }
                        className="mt-1"
                        disabled
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payment Method */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary flex items-center">
                  <CreditCard className="mr-2 h-5 w-5" />
                  Payment Method
                </CardTitle>
              </CardHeader>
              <CardContent>
                <RadioGroup
                  value={paymentMethod}
                  onValueChange={setPaymentMethod}
                >
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                      <RadioGroupItem value="mpesa" id="mpesa" />
                      <Label htmlFor="mpesa" className="flex-1 cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium marketplace-text-primary">
                              M-Pesa
                            </p>
                            <p className="text-sm marketplace-text-muted">
                              Pay with your M-Pesa mobile money
                            </p>
                          </div>
                          <div className="text-2xl">📱</div>
                        </div>
                      </Label>
                    </div>

                    <div className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                      <RadioGroupItem value="card" id="card" />
                      <Label htmlFor="card" className="flex-1 cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium marketplace-text-primary">
                              Credit/Debit Card
                            </p>
                            <p className="text-sm marketplace-text-muted">
                              Visa, Mastercard, American Express
                            </p>
                          </div>
                          <div className="text-2xl">💳</div>
                        </div>
                      </Label>
                    </div>

                    <div className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                      <RadioGroupItem value="bank" id="bank" />
                      <Label htmlFor="bank" className="flex-1 cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium marketplace-text-primary">
                              Bank Transfer
                            </p>
                            <p className="text-sm marketplace-text-muted">
                              Direct bank transfer
                            </p>
                          </div>
                          <div className="text-2xl">🏦</div>
                        </div>
                      </Label>
                    </div>
                  </div>
                </RadioGroup>
              </CardContent>
            </Card>

            {/* Order Notes */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary flex items-center">
                  <Package className="mr-2 h-5 w-5" />
                  Order Notes (Optional)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Any special instructions for your order..."
                  className="min-h-[80px]"
                />
              </CardContent>
            </Card>

            {/* Order Summary */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary flex items-center">
                  <Package className="mr-2 h-5 w-5" />
                  Order Summary ({itemCount} items)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {items.map((item) => (
                  <div
                    key={item.id}
                    className="flex gap-4 p-4 bg-gray-50 rounded-lg"
                  >
                    {/* Product Image */}
                    <div className="relative w-16 h-16 bg-white rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        src={getFirstImageUrl(item.product.images)}
                        alt={item.product.title}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          // Fallback to placeholder if image fails to load
                          e.currentTarget.src = "/placeholder-product.svg";
                        }}
                      />
                    </div>

                    {/* Product Details */}
                    <div className="flex-1">
                      <h4 className="font-medium marketplace-text-primary">
                        {item.product.title}
                      </h4>
                      <p className="text-sm marketplace-text-muted">
                        by {item.product.seller.name}
                      </p>
                      <div className="flex justify-between items-center mt-2">
                        <span className="text-sm marketplace-text-muted">
                          Qty: {item.quantity}
                        </span>
                        <span className="font-semibold marketplace-text-warm">
                          {formatPrice(item.product.price * item.quantity)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Security Features */}
            <Card className="marketplace-card">
              <CardHeader>
                <CardTitle className="marketplace-text-primary flex items-center">
                  <Shield className="mr-2 h-5 w-5" />
                  Secure Checkout
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Shield className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium marketplace-text-primary">
                        SSL Encrypted
                      </p>
                      <p className="text-sm marketplace-text-muted">
                        Secure payment
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Truck className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium marketplace-text-primary">
                        Fast Shipping
                      </p>
                      <p className="text-sm marketplace-text-muted">
                        Quick delivery
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="font-medium marketplace-text-primary">
                        Quality Assured
                      </p>
                      <p className="text-sm marketplace-text-muted">
                        Verified sellers
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Summary */}
          <div className="lg:col-span-1">
            <Card className="marketplace-card sticky top-4">
              <CardHeader>
                <CardTitle className="marketplace-text-primary">
                  Payment Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="marketplace-text-muted">
                      Subtotal ({itemCount} items)
                    </span>
                    <span className="marketplace-text-primary">
                      {formatPrice(total)}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="marketplace-text-muted">Shipping</span>
                    <span className="marketplace-text-primary">Free</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="marketplace-text-muted">Tax</span>
                    <span className="marketplace-text-primary">$0.00</span>
                  </div>
                </div>

                <Separator />

                <div className="flex justify-between text-lg font-semibold">
                  <span className="marketplace-text-primary">Total</span>
                  <span className="marketplace-text-warm">
                    {formatPrice(total)}
                  </span>
                </div>

                {error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  </div>
                )}

                <Button
                  onClick={handleCheckout}
                  disabled={isProcessing}
                  className="w-full marketplace-btn-primary"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard className="mr-2 h-4 w-4" />
                      Complete Order
                    </>
                  )}
                </Button>

                <p className="text-xs marketplace-text-muted text-center">
                  By completing your order, you agree to our Terms of Service
                  and Privacy Policy.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
