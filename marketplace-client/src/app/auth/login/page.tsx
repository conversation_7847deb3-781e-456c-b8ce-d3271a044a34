"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/lib/contexts/AuthContext";
import { useNotifications } from "@/lib/hooks/useNotifications";
import Layout from "@/components/layout/Layout";
import { Eye, EyeOff, LogIn } from "lucide-react";

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { login } = useAuth();
  const notifications = useNotifications();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await login(email, password);
      notifications.loginSuccess();

      // Check if there's a redirect path stored
      const redirectPath = sessionStorage.getItem("redirectAfterLogin");
      if (redirectPath) {
        sessionStorage.removeItem("redirectAfterLogin");
        router.push(redirectPath);
        return;
      }

      // Get user from auth context to determine redirect
      const userData = JSON.parse(localStorage.getItem("user") || "{}");

      // Role-based redirect
      if (userData.role === "seller") {
        router.push("/seller/dashboard");
      } else if (userData.role === "admin") {
        router.push("/admin/dashboard");
      } else {
        router.push("/products");
      }
    } catch (error: any) {
      notifications.error(error.message || "Invalid email or password");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="marketplace-theme">
      <Layout showHeader={false} showFooter={false}>
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 to-indigo-50 py-12 px-4 sm:px-6 lg:px-8">
          <div className="marketplace-pattern-subtle"></div>
          <div className="max-w-md w-full space-y-8 relative z-10">
            <div className="text-center">
              <h2 className="mt-6 text-4xl marketplace-heading">
                Welcome <span className="marketplace-heading-accent">Back</span>
              </h2>
              <p className="mt-4 text-lg marketplace-text-muted">
                Or{" "}
                <Link
                  href="/auth/register"
                  className="font-medium marketplace-text-warm hover:underline"
                >
                  create a new account
                </Link>
              </p>
            </div>

            <Card className="marketplace-card-elevated">
              <CardHeader>
                <CardTitle className="marketplace-text-primary">
                  Welcome back
                </CardTitle>
                <CardDescription className="marketplace-text-muted">
                  Enter your credentials to access your account
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <Label htmlFor="email">Email address</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email"
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="password">Password</Label>
                    <div className="relative mt-1">
                      <Input
                        id="password"
                        name="password"
                        type={showPassword ? "text" : "password"}
                        autoComplete="current-password"
                        required
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Enter your password"
                        className="pr-10"
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-sm">
                      <Link
                        href="/auth/forgot-password"
                        className="font-medium marketplace-text-warm hover:underline"
                      >
                        Forgot your password?
                      </Link>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full marketplace-btn-primary"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      "Signing in..."
                    ) : (
                      <>
                        <LogIn className="mr-2 h-4 w-4" />
                        Sign in
                      </>
                    )}
                  </Button>
                </form>

                <div className="mt-6">
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-300" />
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="px-2 bg-white text-gray-500">
                        Demo Accounts
                      </span>
                    </div>
                  </div>

                  <div className="mt-4 space-y-2 text-sm text-gray-600">
                    <p>
                      <strong>Admin:</strong> <EMAIL>
                    </p>
                    <p>
                      <strong>Seller:</strong> <EMAIL>
                    </p>
                    <p>
                      <strong>Buyer:</strong> <EMAIL>
                    </p>
                    <p className="text-xs">
                      Use any password for demo accounts
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="text-center">
              <Link
                href="/"
                className="text-sm marketplace-text-muted hover:marketplace-text-primary"
              >
                ← Back to home
              </Link>
            </div>
          </div>
        </div>
      </Layout>
    </div>
  );
}
