"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForgotPasswordMutation } from "@/lib/api/authApi";
import { useNotifications } from "@/lib/hooks/useNotifications";
import Layout from "@/components/layout/Layout";
import { Mail, ArrowLeft } from "lucide-react";

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isResending, setIsResending] = useState(false);

  const [forgotPassword] = useForgotPasswordMutation();
  const notifications = useNotifications();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const result = await forgotPassword({ email }).unwrap();
      notifications.success(result.message);
      setIsSubmitted(true);
    } catch (error: any) {
      notifications.error(error?.data?.message || "Failed to send reset email");
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    router.push("/auth/login");
  };

  const handleProceedToReset = () => {
    router.push(`/auth/reset-password?email=${encodeURIComponent(email)}`);
  };

  const handleResendOTP = async () => {
    setIsResending(true);
    try {
      const result = await forgotPassword({ email }).unwrap();
      notifications.success("OTP resent successfully!");
    } catch (error: any) {
      notifications.error(error?.data?.message || "Failed to resend OTP");
    } finally {
      setIsResending(false);
    }
  };

  if (isSubmitted) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-8">
            <Card>
              <CardHeader className="text-center">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                  <Mail className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle className="mt-6 text-2xl font-bold text-gray-900">
                  Check your email
                </CardTitle>
                <CardDescription className="mt-2 text-sm text-gray-600">
                  We've sent a password reset OTP to {email}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="text-sm text-gray-600 text-center">
                  <p>
                    Please check your email and enter the OTP code to reset your
                    password.
                  </p>
                  <p className="mt-2">The code will expire in 10 minutes.</p>
                </div>

                <div className="space-y-4">
                  <Button onClick={handleProceedToReset} className="w-full">
                    I have the OTP code
                  </Button>

                  <Button
                    variant="outline"
                    onClick={handleResendOTP}
                    disabled={isResending}
                    className="w-full"
                  >
                    <Mail className="mr-2 h-4 w-4" />
                    {isResending ? "Resending..." : "Resend OTP"}
                  </Button>

                  <Button
                    variant="outline"
                    onClick={handleBackToLogin}
                    className="w-full"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to login
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold text-gray-900">
                Forgot your password?
              </CardTitle>
              <CardDescription className="mt-2 text-sm text-gray-600">
                Enter your email address and we'll send you an OTP to reset your
                password.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <Label htmlFor="email">Email address</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    className="mt-1"
                  />
                </div>

                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    "Sending..."
                  ) : (
                    <>
                      <Mail className="mr-2 h-4 w-4" />
                      Send reset OTP
                    </>
                  )}
                </Button>

                <div className="text-center">
                  <Link
                    href="/auth/login"
                    className="text-sm text-blue-600 hover:text-blue-500"
                  >
                    <ArrowLeft className="inline mr-1 h-4 w-4" />
                    Back to login
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
}
