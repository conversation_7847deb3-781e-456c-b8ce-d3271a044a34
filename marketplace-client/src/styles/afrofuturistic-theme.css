/* Modern African-Inspired Marketplace Theme - Premium & Welcoming */

@import url("https://fonts.googleapis.com/css2?family=DM+Serif+Display:ital@0;1&family=Inter:wght@300;400;500;600;700&display=swap");

:root {
  /* Primary Neutral Palette - Clean & Modern */
  --marketplace-ivory: #fefcf8; /* Ivory white base */
  --marketplace-charcoal: #2c2c2c; /* Charcoal gray */
  --marketplace-beige: #f5f2ed; /* Warm beige */
  --marketplace-light-gray: #f8f7f5; /* Very light warm gray */
  --marketplace-medium-gray: #8b8680; /* Medium warm gray */
  --marketplace-dark-gray: #4a453f; /* Dark warm gray */

  /* African-Inspired Accent Colors - Subtle & Elegant */
  --marketplace-burnt-orange: #d2691e; /* Burnt orange - warm & inviting */
  --marketplace-burnt-orange-light: #e8944a; /* Lighter burnt orange */
  --marketplace-burnt-orange-dark: #b8571a; /* Darker burnt orange */
  --marketplace-indigo: #4b0082; /* Deep indigo blue */
  --marketplace-indigo-light: #6a4c93; /* Lighter indigo */
  --marketplace-indigo-dark: #3a0066; /* Darker indigo */

  /* Supporting Colors */
  --marketplace-terracotta: #cd853f; /* Warm terracotta */
  --marketplace-sage: #9caf88; /* Soft sage green */
  --marketplace-cream: #fff8dc; /* Warm cream */
  --marketplace-sand: #f4e4bc; /* Sandy beige */

  /* High Contrast Text Colors */
  --marketplace-text-primary: #1a1a1a; /* Very dark for high contrast */
  --marketplace-text-secondary: #4a4a4a; /* Medium dark for secondary text */
  --marketplace-text-muted: #6b6b6b; /* Darker muted text for better readability */

  /* Elegant Gradients */
  --marketplace-gradient-warm: linear-gradient(
    135deg,
    var(--marketplace-burnt-orange) 0%,
    var(--marketplace-terracotta) 100%
  );
  --marketplace-gradient-cool: linear-gradient(
    135deg,
    var(--marketplace-indigo) 0%,
    var(--marketplace-indigo-light) 100%
  );
  --marketplace-gradient-neutral: linear-gradient(
    135deg,
    var(--marketplace-beige) 0%,
    var(--marketplace-light-gray) 100%
  );
  --marketplace-gradient-hero: linear-gradient(
    135deg,
    var(--marketplace-ivory) 0%,
    var(--marketplace-beige) 50%,
    var(--marketplace-light-gray) 100%
  );

  /* Subtle Shadows - Premium Feel */
  --marketplace-shadow-soft: 0 2px 8px rgba(44, 44, 44, 0.08);
  --marketplace-shadow-medium: 0 4px 16px rgba(44, 44, 44, 0.12);
  --marketplace-shadow-strong: 0 8px 32px rgba(44, 44, 44, 0.16);
  --marketplace-shadow-warm: 0 4px 16px rgba(210, 105, 30, 0.15);
  --marketplace-shadow-cool: 0 4px 16px rgba(75, 0, 130, 0.15);

  /* Typography - Elegant & Readable */
  --marketplace-font-serif: "DM Serif Display", "Georgia", serif;
  --marketplace-font-sans: "Inter", system-ui, sans-serif;
  --marketplace-font-mono: "JetBrains Mono", "Fira Code", monospace;
}

/* Base Modern Marketplace Theme */
.marketplace-theme {
  background: var(--marketplace-ivory);
  color: var(--marketplace-charcoal);
  font-family: var(--marketplace-font-sans);
  line-height: 1.6;
}

.marketplace-pattern-subtle {
  background-image: radial-gradient(
      circle at 20% 20%,
      var(--marketplace-burnt-orange) 1px,
      transparent 1px
    ),
    radial-gradient(
      circle at 80% 80%,
      var(--marketplace-indigo) 1px,
      transparent 1px
    );
  background-size: 40px 40px;
  opacity: 0.03;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.marketplace-pattern-textile {
  background-image: linear-gradient(
      45deg,
      var(--marketplace-burnt-orange) 1px,
      transparent 1px
    ),
    linear-gradient(-45deg, var(--marketplace-indigo) 1px, transparent 1px);
  background-size: 30px 30px;
  opacity: 0.02;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.marketplace-card {
  background: var(--marketplace-ivory);
  border: 1px solid rgba(139, 134, 128, 0.15);
  border-radius: 12px;
  box-shadow: var(--marketplace-shadow-soft);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.marketplace-card:hover {
  box-shadow: var(--marketplace-shadow-medium);
  transform: translateY(-2px);
}

.marketplace-card-elevated {
  background: var(--marketplace-ivory);
  border: 1px solid rgba(139, 134, 128, 0.1);
  border-radius: 16px;
  box-shadow: var(--marketplace-shadow-medium);
}

.marketplace-card-warm {
  background: var(--marketplace-cream);
  border: 1px solid rgba(210, 105, 30, 0.15);
  box-shadow: var(--marketplace-shadow-warm);
}

.marketplace-card-cool {
  background: var(--marketplace-ivory);
  border: 1px solid rgba(75, 0, 130, 0.15);
  box-shadow: var(--marketplace-shadow-cool);
}

.afro-neon-border {
  border: 2px solid var(--afro-accent-2);
  box-shadow: var(--afro-glow-neon);
  border-radius: 8px;
}

.afro-hologram {
  background: linear-gradient(
    45deg,
    transparent 30%,
    var(--afro-accent-2) 50%,
    transparent 70%
  );
  background-size: 200% 200%;
  animation: hologram-shift 3s ease-in-out infinite;
}

@keyframes hologram-shift {
  0%,
  100% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
}

/* Card Styles */
.afro-card {
  background: rgba(26, 11, 11, 0.8);
  border: 1px solid var(--afro-primary);
  border-radius: 12px;
  box-shadow: var(--afro-shadow-primary);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.marketplace-btn-primary {
  background: var(--marketplace-gradient-warm);
  color: var(--marketplace-ivory);
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-family: var(--marketplace-font-sans);
  transition: all 0.3s ease;
  box-shadow: var(--marketplace-shadow-soft);
  cursor: pointer;
}

.marketplace-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--marketplace-shadow-warm);
}

.marketplace-btn-secondary {
  background: transparent;
  color: var(--marketplace-burnt-orange);
  border: 2px solid var(--marketplace-burnt-orange);
  padding: 10px 22px;
  border-radius: 8px;
  font-weight: 600;
  font-family: var(--marketplace-font-sans);
  transition: all 0.3s ease;
  cursor: pointer;
}

.marketplace-btn-secondary:hover {
  background: var(--marketplace-burnt-orange);
  color: var(--marketplace-ivory);
  transform: translateY(-1px);
}

.marketplace-btn-outline {
  background: transparent;
  color: var(--marketplace-charcoal);
  border: 1px solid var(--marketplace-medium-gray);
  padding: 10px 22px;
  border-radius: 8px;
  font-weight: 500;
  font-family: var(--marketplace-font-sans);
  transition: all 0.3s ease;
  cursor: pointer;
}

.marketplace-btn-outline:hover {
  border-color: var(--marketplace-burnt-orange);
  color: var(--marketplace-burnt-orange);
  transform: translateY(-1px);
}

/* Modern Navigation Styles */
.marketplace-nav {
  background: var(--marketplace-ivory);
  border-bottom: 1px solid rgba(139, 134, 128, 0.15);
  backdrop-filter: blur(10px);
}

.marketplace-nav-item {
  color: var(--marketplace-charcoal);
  transition: all 0.3s ease;
  position: relative;
  font-weight: 500;
}

.marketplace-nav-item:hover {
  color: var(--marketplace-burnt-orange);
}

.marketplace-nav-item.active {
  color: var(--marketplace-burnt-orange);
}

.marketplace-nav-item::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--marketplace-burnt-orange);
  transition: width 0.3s ease;
}

.marketplace-nav-item:hover::after,
.marketplace-nav-item.active::after {
  width: 100%;
}

.marketplace-heading {
  font-family: var(--marketplace-font-serif);
  color: var(--marketplace-charcoal);
  font-weight: 400;
  line-height: 1.2;
}

.marketplace-heading-accent {
  color: var(--marketplace-burnt-orange);
}

.marketplace-text-warm {
  color: var(--marketplace-burnt-orange);
}

.marketplace-text-cool {
  color: var(--marketplace-indigo);
}

.marketplace-text-muted {
  color: var(--marketplace-text-muted);
}

.marketplace-text-primary {
  color: var(--marketplace-text-primary);
}

.marketplace-text-secondary {
  color: var(--marketplace-text-secondary);
}

/* Animations */
.marketplace-fade-in {
  animation: marketplace-fade-in 0.6s ease-out;
}

@keyframes marketplace-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.marketplace-slide-in {
  animation: marketplace-slide-in 0.8s ease-out;
}

@keyframes marketplace-slide-in {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Hover Effects */
.marketplace-hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.marketplace-hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--marketplace-shadow-medium);
}

/* Responsive Design */
@media (max-width: 768px) {
  .marketplace-card {
    margin: 8px;
    border-radius: 8px;
  }

  .marketplace-btn-primary,
  .marketplace-btn-secondary,
  .marketplace-btn-outline {
    padding: 10px 18px;
    font-size: 14px;
  }

  .marketplace-heading {
    font-size: 1.5rem;
  }
}

/* Legacy Support - Gradual Migration */
.afro-theme {
  background: var(--marketplace-gradient-hero);
  color: var(--marketplace-charcoal);
  font-family: var(--marketplace-font-sans);
}

.afro-heading {
  font-family: var(--marketplace-font-serif);
  color: var(--marketplace-burnt-orange);
  font-weight: 400;
}

.afro-text-warm {
  color: var(--marketplace-burnt-orange);
}

.afro-text-neon {
  color: var(--marketplace-indigo);
}

.afro-card {
  background: var(--marketplace-ivory);
  border: 1px solid rgba(139, 134, 128, 0.15);
  border-radius: 12px;
  box-shadow: var(--marketplace-shadow-soft);
}

.afro-card-tech {
  background: var(--marketplace-cream);
  border: 1px solid rgba(210, 105, 30, 0.15);
  box-shadow: var(--marketplace-shadow-warm);
}

.afro-btn-primary {
  background: var(--marketplace-gradient-warm);
  color: var(--marketplace-ivory);
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: var(--marketplace-shadow-soft);
}

.afro-btn-tech {
  background: transparent;
  color: var(--marketplace-indigo);
  border: 2px solid var(--marketplace-indigo);
  padding: 10px 22px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.afro-nav {
  background: var(--marketplace-ivory);
  border-bottom: 1px solid rgba(139, 134, 128, 0.15);
}
