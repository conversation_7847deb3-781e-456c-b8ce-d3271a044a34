#!/usr/bin/env node

const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function testAuthWorkflow() {
  console.log('🚀 Testing Authentication Workflow\n');

  try {
    // Test 1: User Registration
    console.log('1️⃣ Testing User Registration...');
    const registerResponse = await axios.post(`${API_BASE}/auth/signup`, {
      name: 'Test User 2',
      email: '<EMAIL>',
      password: 'password123',
      role: 'buyer'
    });
    console.log('✅ Registration successful:', registerResponse.data.message);
    console.log('   User ID:', registerResponse.data.user.id);
    console.log('   User Role:', registerResponse.data.user.role);

    // Test 2: User Login
    console.log('\n2️⃣ Testing User Login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/signin`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    console.log('✅ Login successful');
    console.log('   Access Token:', loginResponse.data.accessToken.substring(0, 20) + '...');
    console.log('   User Name:', loginResponse.data.user.name);

    // Test 3: Forgot Password
    console.log('\n3️⃣ Testing Forgot Password...');
    const forgotResponse = await axios.post(`${API_BASE}/auth/forgot-password`, {
      email: '<EMAIL>'
    });
    console.log('✅ Forgot password successful:', forgotResponse.data.message);

    // Test 4: Invalid Login
    console.log('\n4️⃣ Testing Invalid Login...');
    try {
      await axios.post(`${API_BASE}/auth/signin`, {
        email: '<EMAIL>',
        password: 'wrongpassword'
      });
    } catch (error) {
      console.log('✅ Invalid login properly rejected:', error.response.data.message);
    }

    // Test 5: Reset Password with Invalid OTP
    console.log('\n5️⃣ Testing Reset Password with Invalid OTP...');
    try {
      await axios.post(`${API_BASE}/auth/reset-password`, {
        email: '<EMAIL>',
        otp: '123456',
        newPassword: 'newpassword123'
      });
    } catch (error) {
      console.log('✅ Invalid OTP properly rejected:', error.response.data.message);
    }

    // Test 6: Token Refresh
    console.log('\n6️⃣ Testing Token Refresh...');
    try {
      const refreshResponse = await axios.post(`${API_BASE}/auth/refresh`, {
        refreshToken: loginResponse.data.refreshToken
      });
      console.log('✅ Token refresh successful');
      console.log('   New Access Token:', refreshResponse.data.accessToken.substring(0, 20) + '...');
    } catch (error) {
      console.log('❌ Token refresh failed:', error.response?.data?.message || error.message);
    }

    console.log('\n🎉 All authentication tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

// Run the test
testAuthWorkflow();
